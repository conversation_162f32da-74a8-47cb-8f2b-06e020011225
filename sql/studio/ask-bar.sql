#问吧库`ask-bar`sql

### 新增工作室功能相关表
DROP TABLE IF EXISTS `ask-bar`.`t_studio`;
CREATE TABLE `ask-bar`.`t_studio`
(
  `f_id`               varchar(40)  NOT NULL COMMENT '主键ID',
  `f_name`             varchar(10)  NOT NULL COMMENT '工作室名称',
  `f_organization_id`  varchar(40)  NOT NULL COMMENT '归属组织机构id',
  `f_status`           tinyint(4)   NOT NULL COMMENT '状态  所有内容需关联只查启用状态的数据（0：禁用，1：启用）',
  `f_recommend_flag`   tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否推荐(0：否,1：是)',
  `f_sequence`         int(11)      NOT NULL DEFAULT 0 COMMENT '排序，推荐业务使用',
  `f_property`         tinyint(4)   NOT NULL DEFAULT 0 COMMENT '工作室属性(1：首席科学家 2：首席专家 3：省级专家 4：其他专家)',
  `f_positional_title` varchar(20)  NOT NULL COMMENT '专家职称',
  `f_maxim`            varchar(50)           DEFAULT NULL COMMENT '工作室格言',
  `f_introduction`        text               DEFAULT NULL COMMENT '工作室简介富文本',
  `f_introduction_txt` varchar(2500)         DEFAULT NULL COMMENT '工作室简介',
  `f_cover_id`         varchar(40)  NOT NULL DEFAULT '' COMMENT '封面id',
  `f_cover_path`       varchar(100) NOT NULL DEFAULT '' COMMENT '封面path',
  `f_portrait_id`      varchar(40)  NOT NULL DEFAULT '' COMMENT '头像id',
  `f_portrait_path`    varchar(100) NOT NULL COMMENT '头像path',
  `f_content_num`      int(11)      NOT NULL DEFAULT 0 COMMENT '内容数',
  `f_popular`          int(11)      NOT NULL DEFAULT 0 COMMENT '人气值(定时任务算)',
  `f_delete_flag`      tinyint(4)   NOT NULL COMMENT '是否删除(0：否,1：是)',
  `f_create_time`      bigint(20)   NOT NULL COMMENT '创建时间',
  `f_modify_date`      timestamp    NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_studio_recommend_flag_seq_p` (`f_recommend_flag`, `f_sequence`, `f_popular`),
  KEY `idx_studio_create_time` (`f_create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室表';

DROP TABLE IF EXISTS `ask-bar`.`t_studio_attention`;
CREATE TABLE `ask-bar`.`t_studio_attention`
(
  `f_id`          varchar(40) NOT NULL COMMENT '主键ID',
  `f_studio_id`   varchar(40) NOT NULL COMMENT '工作室id',
  `f_member_id`   varchar(40) NOT NULL COMMENT '人员id',
  `f_create_time` bigint(20)  NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_studio_attention_studio_id` (`f_studio_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室人员关注表';

DROP TABLE IF EXISTS `ask-bar`.`t_studio_content`;
CREATE TABLE `ask-bar`.`t_studio_content`
(
  `f_id`            varchar(40)  NOT NULL COMMENT '主键ID',
  `f_studio_id`     varchar(40)  NOT NULL COMMENT '工作室id',
  `f_business_id`   varchar(40)  NOT NULL COMMENT '内容id（对应各业务类型表.f_id）',
  `f_business_type` tinyint(4)   NOT NULL DEFAULT 0 COMMENT '内容类型(1：直播,2：课程,3：文章,4：讨论,5：文档,6：问答)',
  `f_business_name` varchar(200) NOT NULL COMMENT '内容名称（对应各业务类型表.f_name或者f_title字段）',
  `f_popular`       int(11)      NOT NULL DEFAULT 0 COMMENT '人气值 ( 定时任务每天晚上算一次)',
  `f_delete_flag`   tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除(0：否,1：是)',
  `f_status`        tinyint(4)   NOT NULL DEFAULT 0 COMMENT '发布状态(0：未发布,1：已发布)',
  `f_cancel_flag`   tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否取消状态（0：否，1：是）',
  `f_create_time`   bigint(20)   NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_studio_content_studio_id` (`f_studio_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室内容关联表';

DROP TABLE IF EXISTS `ask-bar`.`t_studio_content_audit`;
CREATE TABLE `ask-bar`.`t_studio_content_audit`
(
  `f_id`                varchar(40) NOT NULL COMMENT '主键ID',
  `f_studio_content_id` varchar(40) NOT NULL COMMENT '工作室内容关联表id',
  `f_audit_status`      tinyint(4)  NOT NULL DEFAULT 0 COMMENT '审核状态(０：待审核,１：审核通过,２：拒绝)',
  `f_audit_member_id`   varchar(40)          DEFAULT NULL COMMENT '审核人',
  `f_audit_time`        bigint(20)           DEFAULT NULL COMMENT '审核时间',
  `f_audit_note`        varchar(1000)        DEFAULT NULL COMMENT '审核备注',
  `f_create_member_id`  varchar(40) NOT NULL COMMENT '提交人',
  `f_create_time`       bigint(20)  NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_content_audit_content_id` (`f_studio_content_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室内容审核表';

DROP TABLE IF EXISTS `ask-bar`.`t_studio_discuss`;
CREATE TABLE `ask-bar`.`t_studio_discuss`
(
  `f_id`               varchar(40)  NOT NULL COMMENT '主键ID',
  `f_title`            varchar(200) NOT NULL COMMENT '标题',
  `f_cover_id`         varchar(40)  NOT NULL DEFAULT '' COMMENT '封面id',
  `f_cover_path`       varchar(100) NOT NULL DEFAULT '' COMMENT '封面path',
  `f_content`          text                  DEFAULT NULL COMMENT '讨论详情/内容富文本',
  `f_content_txt`      text                  DEFAULT NULL COMMENT '讨论详情/内容纯文本',
  `f_introduction`     varchar(200) NOT NULL DEFAULT '' COMMENT '讨论简介',
  `f_discuss_num`      int(11)      NOT NULL DEFAULT 0 COMMENT '评论/讨论数 实时统计',
  `f_collect_num`      int(11)      NOT NULL DEFAULT 0 COMMENT '收藏数',
  `f_praise_num`       int(11)      NOT NULL DEFAULT 0 COMMENT '点赞数',
  `f_browse_num`       int(11)      NOT NULL DEFAULT 0 COMMENT '浏览数',
  `f_audit_status`     tinyint(4)   NOT NULL DEFAULT 0 COMMENT '审核状态:1待审核 2通过 3拒绝',
  `f_delete_flag`      tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除（0：否，1：是）',
  `f_create_member_id` varchar(40)  NOT NULL COMMENT '创建人',
  `f_create_time`      bigint(20)   NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室讨论表';

DROP TABLE IF EXISTS `ask-bar`.`t_studio_member`;
CREATE TABLE `ask-bar`.`t_studio_member`
(
  `f_id`          varchar(40) NOT NULL DEFAULT '' COMMENT '主键ID',
  `f_studio_id`   varchar(40) NOT NULL COMMENT '工作室id',
  `f_member_id`   varchar(40) NOT NULL COMMENT '人员id',
  `f_master_flag` tinyint(4)  NOT NULL DEFAULT 0 COMMENT '是否总管理员（0：否，1：是）',
  `f_create_time` bigint(20)  NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_studio_member_studio_id` (`f_studio_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室人员关联表（成员）';

DROP TABLE IF EXISTS `ask-bar`.`t_studio_question`;
CREATE TABLE `ask-bar`.`t_studio_question`
(
  `f_id`               varchar(40)  NOT NULL COMMENT '主键ID',
  `f_title`            varchar(200) NOT NULL COMMENT '标题',
  `f_content`          text                  DEFAULT NULL COMMENT '问题描述/内容富文本',
  `f_content_txt`      text                  DEFAULT NULL COMMENT '问题描述/内容纯文本',
  `f_answer`           text                  DEFAULT NULL COMMENT '回答富文本',
  `f_answer_txt`       text                  DEFAULT NULL COMMENT '回答纯文本',
  `f_answer_flag`      tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否已回答(0：否,1：是)',
  `f_delete_flag`      tinyint(4)   NOT NULL DEFAULT 0 COMMENT '是否删除(0：否,1：是)',
  `f_studio_id`        varchar(40)           DEFAULT NULL COMMENT '回答工作室ID',
  `f_answer_member_id` varchar(40)           DEFAULT NULL COMMENT '回答工作室成员id，回答多次使用最后一次回答的成员',
  `f_create_member_id` varchar(40)  NOT NULL COMMENT '提问人',
  `f_answer_time`      bigint(20)            DEFAULT NULL COMMENT '回答时间，回答多次使用最后一次的时间',
  `f_create_time`      bigint(20)   NOT NULL COMMENT '创建时间',
  `f_update_time`      bigint(20)            DEFAULT NULL COMMENT '提问修改时间',
  PRIMARY KEY (`f_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室问答表';

DROP TABLE IF EXISTS `ask-bar`.`t_studio_topic`;
CREATE TABLE `ask-bar`.`t_studio_topic`
(
  `f_id`          varchar(40) NOT NULL COMMENT '主键ID',
  `f_studio_id`   varchar(40) NOT NULL COMMENT '工作室id',
  `f_topic_id`    varchar(40) NOT NULL COMMENT '标签id',
  `f_create_time` bigint(20)  NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`),
  KEY `idx_studio_topic_studio_id` (`f_studio_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='专家工作室标签关联表（研究方向）';


# 新增专家工作室创建标识字段
ALTER TABLE `ask-bar`.t_question
  ADD COLUMN f_from_studio tinyint DEFAULT 0 NOT NULL COMMENT '是否专家工作室创建：0否，1是',
  ADD COLUMN f_collect_num int DEFAULT 0 NOT NULL COMMENT '收藏数，专家工作室文章类型才有';

# 点赞表增加类型和字段用以区分专家工作室的数据
ALTER TABLE `ask-bar`.`t_praise`
  MODIFY COLUMN `f_object_type` TINYINT DEFAULT NULL COMMENT '点赞对象类型 1:讨论 2回复 3文章 4专家工作室讨论';


