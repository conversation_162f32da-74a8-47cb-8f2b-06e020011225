# 学习库`course-study`sql
ALTER TABLE `course-study`.t_knowledge_info
  ADD COLUMN f_from_studio TINYINT DEFAULT 0 NOT NULL COMMENT '是否专家工作室创建：0否，1是';

ALTER TABLE `course-study`.`t_gensee_web_cast`
  ADD COLUMN `f_scene` int(11) DEFAULT 0 COMMENT '直播模式，(0大讲堂, 1小班课 默认值为：0)',
  ADD COLUMN `f_studio_id` varchar(40) DEFAULT NULL COMMENT '专家工作室id',
  ADD COLUMN `f_compere` varchar(40) DEFAULT NULL COMMENT '作为云视讯直播主持人ID',
  ADD COLUMN `f_ysx_code` varchar(40) DEFAULT NULL COMMENT '作为云视讯直播的入会密码';

ALTER TABLE `course-study`.`t_course_info`
    MODIFY COLUMN `f_status` int NULL DEFAULT NULL COMMENT '状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)',
    MODIFY COLUMN `f_source` int NULL DEFAULT NULL COMMENT '课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室)';

