# 新增专家工作室菜单sql

INSERT INTO system.`t_menu` (`f_id`, `f_name`, `f_level`, `f_uri`, `f_create_time`, `f_parent_id`, `f_order`, `f_icon`,
                             `f_path`, `f_organization_id`, `f_code`, `f_number`, `f_init`, `f_create_member_id`)
VALUES ('003003', '专家工作室', '2', NULL, '003003', '003', '3', NULL, '003,003003,', '1', 144, NULL, '0', '1');
INSERT INTO system.`t_role_menu`
VALUES (UUID(), '1', '003003', '1610155695746');


INSERT INTO system.`t_menu` (`f_id`, `f_name`, `f_level`, `f_uri`, `f_create_time`, `f_parent_id`, `f_order`, `f_icon`,
                             `f_path`, `f_organization_id`, `f_code`, `f_number`, `f_init`, `f_create_member_id`)
VALUES ('003003001', '工作室管理', '3', 'studio/studio-manage', '1610155695746', '003003', '1', NULL,
        '003,003003,,003003001,', '1', 145, NULL, '0', '1');
INSERT INTO system.`t_role_menu`
VALUES (UUID(), '1', '003003001', '1610155695746');

INSERT INTO system.`t_menu` (`f_id`, `f_name`, `f_level`, `f_uri`, `f_create_time`, `f_parent_id`, `f_order`, `f_icon`,
                             `f_path`, `f_organization_id`, `f_code`, `f_number`, `f_init`, `f_create_member_id`)
VALUES ('003003002', '内容管理', '3', 'studio/content-manage', '1610155695746', '003003', '1', NULL,
        '003,003003,,003003002,', '1', 146, NULL, '0', '1');
INSERT INTO system.`t_role_menu`
VALUES (UUID(), '1', '003003002', '1610155695746');

-- 收藏表增加专家工作室类型区分
ALTER TABLE `system`.`t_collect` MODIFY COLUMN `f_business_type` TINYINT DEFAULT NULL
    COMMENT '业务类型:1课程 2专题 3考试 4班级 5直播 6调研 7知识 8 新直播 9文章 10讨论';
## 收藏表增加是否来自于工作室
ALTER TABLE `system`.`t_collect`
    ADD COLUMN `f_from_studio` tinyint NOT NULL DEFAULT 0 COMMENT '是否专家工作室创建：0否，1是';

ALTER TABLE `system`.`t_comment_info`
    MODIFY `f_business_type` TINYINT DEFAULT NULL COMMENT '业务类型:1:课程,2:专题,3:知识,4:培训,11:专家工作室讨论,12:专家工作室直播';

ALTER TABLE `system`.`t_speech_audit`
    MODIFY `f_source_type` TINYINT DEFAULT NULL COMMENT '来源：1问吧 2知识 3课程 4专题 5班级 6幕课 11专家工作室';


#新增专家工作室审核配置
INSERT INTO `system`.t_speech_set (f_id, f_code, f_name, f_note, f_status, f_order, f_create_time, f_member_id,
                                   f_update_time, f_organization_id)
VALUES ('4', 'studio_live', '专家工作室发布直播', '专家工作室成员发布直播是否需要经过管理员审核', 0, 4, UNIX_TIMESTAMP(now()) * 1000, '1',
        UNIX_TIMESTAMP(now()) * 1000, '1');

INSERT INTO `system`.t_speech_set (f_id, f_code, f_name, f_note, f_status, f_order, f_create_time, f_member_id,
                                   f_update_time, f_organization_id)
VALUES ('5', 'studio_course', '专家工作室发布课程', '专家工作室成员发布课程是否需要经过管理员审核', 0, 5, UNIX_TIMESTAMP(now()) * 1000, '1',
        UNIX_TIMESTAMP(now()) * 1000, '1');

INSERT INTO `system`.t_speech_set (f_id, f_code, f_name, f_note, f_status, f_order, f_create_time, f_member_id,
                                   f_update_time, f_organization_id)
VALUES ('6', 'studio_article', '专家工作室发布文章', '专家工作室成员发布文章是否需要经过管理员审核', 0, 6, UNIX_TIMESTAMP(now()) * 1000, '1',
        UNIX_TIMESTAMP(now()) * 1000, '1');

INSERT INTO `system`.t_speech_set (f_id, f_code, f_name, f_note, f_status, f_order, f_create_time, f_member_id,
                                   f_update_time, f_organization_id)
VALUES ('7', 'studio_discuss', '专家工作室发布讨论', '专家工作室成员发布讨论是否需要经过管理员审核', 0, 7, UNIX_TIMESTAMP(now()) * 1000, '1',
        UNIX_TIMESTAMP(now()) * 1000, '1');

##修改菜单名称 专家->达人
UPDATE system.t_menu
SET f_name='达人管理'
WHERE f_name = '专家管理';

UPDATE system.t_menu
SET f_name='达人资质'
WHERE f_name = '专家资质';