<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ID="{C42A831C-219C-43F3-A7DC-DEC92A9FE07A}" Label="" LastModificationDate="1481958711" Name="PhysicalDataModel_ask_bar" Objects="337" Symbols="24" Target="MySQL 5.0" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="16.5.5.4734"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>C42A831C-219C-43F3-A7DC-DEC92A9FE07A</a:ObjectID>
<a:Name>PhysicalDataModel_ask_bar</a:Name>
<a:Code>PhysicalDataModel_ask_bar</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481858484</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=No
RevIndx=Yes
RevOpts=No
RevViewAsTabl=Yes
RevViewOpts=No
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=No
RevDtbsPerm=No
RevViewIndx=No
RevJidxOpts=No
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas.sql
GenScriptName0=crebas.sql
GenScriptName1=crebas
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=F:\项目资料\zxy9\
GenSingleFile=Yes
GenODBC=Yes
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=1
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=No
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=No
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
EstimationYears=0
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes

[FolderOptions\CheckModel]

[FolderOptions\CheckModel\Package]

[FolderOptions\CheckModel\Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CheckPackageMissTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckPackageMissTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularReference]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ConstraintName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CnstMaxLen]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularDependency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ShortcutUniqCode]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table]

[FolderOptions\CheckModel\Table\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqIndex]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - INDXCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - KEYCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyCollYesYes]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\TableIndexes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table\CheckTablePartitionKey]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableStartDate]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableRefNoLifecycle]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableSourceMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTablePartialColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableKeyColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableNotOnLifecycleTablespace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MYSQL50_Table_Table_storage_type]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column]

[FolderOptions\CheckModel\Table.Column\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DomainDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnMandatory]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyDttpDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyCheckDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncNoKey]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncDttp]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\SerialColumnFK]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnCompExpr]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnOneToOneMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnDataTypeMapping]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnNoMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Auto_increment_key]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Datatype_attributes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index]

[FolderOptions\CheckModel\Table.Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UndefIndexType]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IQIndxHNGUniq]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MYSQL50_Index_Fulltext_indexes_validity]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key]

[FolderOptions\CheckModel\Table.Key\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MultiKeySqnc]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger]

[FolderOptions\CheckModel\Table.Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index]

[FolderOptions\CheckModel\Join Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View]

[FolderOptions\CheckModel\View\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\View.View Index]

[FolderOptions\CheckModel\View.View Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference]

[FolderOptions\CheckModel\Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\Reflexive]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\EmptyColl - RFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\IncompleteJoin]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\JoinOrder]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference]

[FolderOptions\CheckModel\View Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\EmptyColl - VRFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain]

[FolderOptions\CheckModel\Domain\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default]

[FolderOptions\CheckModel\Default\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltValeEmpty]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltSameVale]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User]

[FolderOptions\CheckModel\User\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Group]

[FolderOptions\CheckModel\Group\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Role]

[FolderOptions\CheckModel\Role\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure]

[FolderOptions\CheckModel\Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\ProcBodyEmpty]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\DBMS Trigger]

[FolderOptions\CheckModel\DBMS Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DbmsTriggerEvent]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source]

[FolderOptions\CheckModel\Data Source\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\EmptyColl - MODLSRC]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DtscTargets]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckDataSourceModels]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning]

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning]

[FolderOptions\CheckModel\Vertical Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing]

[FolderOptions\CheckModel\Table Collapsing\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\EmptyColl - TargetTable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact]

[FolderOptions\CheckModel\Fact\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - MEASCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - ALLOLINKCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CubeDupAssociation]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension]

[FolderOptions\CheckModel\Dimension\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - HIERCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDupHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDefHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association]

[FolderOptions\CheckModel\Association\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\EmptyColl - Hierarchy]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute]

[FolderOptions\CheckModel\Dimension.Attribute\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure]

[FolderOptions\CheckModel\Fact.Measure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute]

[FolderOptions\CheckModel\Fact.Fact Attribute\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Fact Attribute\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy]

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym]

[FolderOptions\CheckModel\Synonym\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\EmptyColl - BASEOBJ]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type]

[FolderOptions\CheckModel\Abstract Data Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtInstantiable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtAbstractUsed]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure]

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\AdtProcUniqName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package]

[FolderOptions\CheckModel\Database Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - PROCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - CURCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - VARCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - TYPCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - EXCCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure]

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence]

[FolderOptions\CheckModel\Sequence\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor]

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Variable]

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type]

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception]

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace]

[FolderOptions\CheckModel\Tablespace\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage]

[FolderOptions\CheckModel\Storage\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database]

[FolderOptions\CheckModel\Database\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service]

[FolderOptions\CheckModel\Web Service\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation]

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle]

[FolderOptions\CheckModel\Lifecycle\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecyclePhase]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecycleRetention]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckPartitionRange]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase]

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIQTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDuplicateTbspace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspaceCurrency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseRetention]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIdlePeriod]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDataSource]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseExternalOnFirst]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Replication]

[FolderOptions\CheckModel\Replication\PartialReplication]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule]

[FolderOptions\CheckModel\Business Rule\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\EmptyColl - OBJCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object]

[FolderOptions\CheckModel\Extended Object\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link]

[FolderOptions\CheckModel\Extended Link\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File]

[FolderOptions\CheckModel\File\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckPathExists]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format]

[FolderOptions\CheckModel\Data Format\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckDataFormatNullExpression]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
UseTerm=No
EnableRequirements=No
EnableFullShortcut=Yes
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Xsm]
GenRootElement=Yes
GenComplexType=No
GenAttribute=Yes
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<a:RepositoryFilename>F:\项目资料\zxy9\PhysicalDataModel_askbar.pdm</a:RepositoryFilename>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>122DC5EC-CD41-449D-8640-96054C6C1918</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1481767707</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481767707</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>F4BB561F-1183-4A52-8B36-008E5410396D</a:ObjectID>
<a:Name>PhysicalDiagram_1</a:Name>
<a:Code>PhysicalDiagram_1</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Show Links intersections=No
Activate automatic link routing=No
Grid size=0
Graphic unit=2
Window color=255, 255, 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255, 255, 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Show Icon=No
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;#$%&amp;&#39;)*+,-./:;=&gt;?@\]^_`|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject.TextStyle=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ExtDpdShowStrn=Yes
ExtendedDependency_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.TextStyle=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Package.Stereotype=Yes
Package.Comment=No
Package.IconPicture=No
Package.TextStyle=No
Package_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=No
Table.IconPicture=No
Table.TextStyle=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;\&amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;\&amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=No
View.IconPicture=No
View.TextStyle=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure.TextStyle=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LCNMFont=新宋体,8,N
LCNMFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
KeysFont=新宋体,8,N
KeysFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
TriggersFont=新宋体,8,N
TriggersFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0, 0, 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0, 0, 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0, 0, 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0, 0, 0
IndexesFont=新宋体,8,N
IndexesFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0, 0, 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0, 0, 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0, 0, 0
LABLFont=新宋体,8,N
LABLFont color=0, 0, 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0, 0, 0
CENTERFont=新宋体,8,N
CENTERFont color=0, 0, 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0, 0, 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0, 0, 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>7</a:PaperSource>
<c:Symbols>
<o:ReferenceSymbol Id="o5">
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Rect>((-4099,5312), (5667,5762))</a:Rect>
<a:ListOfPoints>((-4099,5537),(5667,5537))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o6"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o7"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o8"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o9">
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Rect>((-12573,-16739), (6345,-16289))</a:Rect>
<a:ListOfPoints>((-12573,-16514),(6345,-16514))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o10"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o7"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o11"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o12">
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Rect>((13691,4427), (14141,19239))</a:Rect>
<a:ListOfPoints>((13916,19239),(13916,4427))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o13"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o7"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o14"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o15">
<a:ModificationDate>1481779563</a:ModificationDate>
<a:Rect>((51830,-7404), (106866,7806))</a:Rect>
<a:ListOfPoints>((51830,7806),(106866,7806),(106866,-7179),(52190,-7179))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o16"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o17"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o18"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o19">
<a:ModificationDate>1481779563</a:ModificationDate>
<a:Rect>((54060,-53733), (54510,-32431))</a:Rect>
<a:ListOfPoints>((54285,-53733),(54285,-32431))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o20"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o17"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o21"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o22">
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((63989,-3284), (76312,12174))</a:Rect>
<a:ListOfPoints>((76312,12174),(67482,12174),(67482,-3059),(63989,-3059))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o23"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o17"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o24"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o25">
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((56088,20355), (76560,20805))</a:Rect>
<a:ListOfPoints>((76560,20685),(69073,20685),(69073,20580),(56088,20580))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>4</a:AutomaticRoutingState>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o23"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o16"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o26"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o27">
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((28367,17342), (89407,54332))</a:Rect>
<a:ListOfPoints>((89407,17342),(89407,54107),(28367,54107))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o23"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o28"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o29"/>
</c:Object>
</o:ReferenceSymbol>
<o:ReferenceSymbol Id="o30">
<a:ModificationDate>1481779558</a:ModificationDate>
<a:Rect>((28424,24464), (42535,41295))</a:Rect>
<a:ListOfPoints>((28424,41295),(28424,24689),(42535,24689))</a:ListOfPoints>
<a:CornerStyle>2</a:CornerStyle>
<a:ArrowStyle>1</a:ArrowStyle>
<a:LineColor>4194432</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>SOURCE 0 新宋体,8,N
DESTINATION 0 新宋体,8,N</a:FontList>
<a:SymbolContent>Reference.Cardinality 0
Reference.ImplementationType 0
Reference.ChildRole 1
Reference.Stereotype 0
Reference.DisplayName 0
Reference.ForeignKeyConstraintName 0
Reference.JoinExpression 0
Reference.Integrity 0
Reference.ParentRole 1</a:SymbolContent>
<c:SourceSymbol>
<o:TableSymbol Ref="o28"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o16"/>
</c:DestinationSymbol>
<c:Object>
<o:Reference Ref="o31"/>
</c:Object>
</o:ReferenceSymbol>
<o:TableSymbol Id="o7">
<a:ModificationDate>1481768297</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((5418,-18667), (22518,8225))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o32"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o6">
<a:ModificationDate>1481768297</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-21544,-669), (-3351,11617))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o33"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o10">
<a:ModificationDate>1481958711</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-22101,-20232), (-2483,-12914))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o34"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o13">
<a:ModificationDate>1481768297</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((5127,10870), (22519,20234))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o35"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o36">
<a:ModificationDate>1481779911</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((5734,-39600), (21666,-23420))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o37"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o20">
<a:ModificationDate>1481779538</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((43031,-67916), (64955,-48812))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o38"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o23">
<a:ModificationDate>**********</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((74218,8753), (92364,26631))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o39"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o16">
<a:ModificationDate>1481779558</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((41211,7661), (63279,27739))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o40"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o17">
<a:ModificationDate>1481779563</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((42195,-42143), (64076,1255))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o41"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o28">
<a:ModificationDate>1481779538</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((11885,40568), (29533,56704))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>166354</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o42"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o43">
<a:ModificationDate>1481856349</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-14104,-39719), (1384,-23791))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o44"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o45">
<a:ModificationDate>1481856629</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-29859,-39958), (-16102,-23822))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o46"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o47">
<a:ModificationDate>1481858492</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-29805,-53178), (-16048,-43581))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o48"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o49">
<a:ModificationDate>1481856720</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-13804,-48689), (1684,-40891))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o50"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o51">
<a:ModificationDate>1481858444</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-24809,-69253), (-7591,-56057))</a:Rect>
<a:LineColor>12615680</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 新宋体,8,N
DISPNAME 0 新宋体,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 新宋体,8,N
TablePkColumns 0 新宋体,8,U
TableFkColumns 0 新宋体,8,N
Keys 0 新宋体,8,N
Indexes 0 新宋体,8,N
Triggers 0 新宋体,8,N
LABL 0 新宋体,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o52"/>
</c:Object>
</o:TableSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:Users>
<o:User Id="o53">
<a:ObjectID>38B3B1C1-B9F1-410A-9410-D3A8DF49ECC4</a:ObjectID>
<a:Name>ask-bar</a:Name>
<a:Code>ask-bar</a:Code>
<a:CreationDate>1481768052</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481858570</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
</o:User>
</c:Users>
<c:Tables>
<o:Table Id="o32">
<a:ObjectID>FDAD4412-689F-4CC0-A2A1-5B9172C6D9A8</a:ObjectID>
<a:Name>t_expert(专家表)</a:Name>
<a:Code>t_expert</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问吧专家表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;专家表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o54">
<a:ObjectID>EC1DB46B-712F-4449-8A52-7BDE3389910A</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o55">
<a:ObjectID>595BB29F-29F1-428B-ACDD-5D5B0A9EA4BF</a:ObjectID>
<a:Name>f_member_id</a:Name>
<a:Code>f_member_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>用户id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o56">
<a:ObjectID>F0773490-8F1A-4015-AE30-0E89567BB459</a:ObjectID>
<a:Name>f_introduce</a:Name>
<a:Code>f_introduce</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家介绍</a:Comment>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o57">
<a:ObjectID>1BAD167D-1B89-4140-B82D-20E9CC3EED50</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o58">
<a:ObjectID>A8FB6D5E-B0D4-408B-A98A-0B2AB0A5084C</a:ObjectID>
<a:Name>f_hiring_time</a:Name>
<a:Code>f_hiring_time</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>聘用时间，初次为审核通过时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o59">
<a:ObjectID>BE68F553-E100-4C5E-A8C2-BF60BB16FB00</a:ObjectID>
<a:Name>f_audit_status</a:Name>
<a:Code>f_audit_status</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核状态：0 没通过  1通过</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,171={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,5=false
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o60">
<a:ObjectID>7D819BD2-4F8A-455C-BC77-997EB87D2C27</a:ObjectID>
<a:Name>f_care_num</a:Name>
<a:Code>f_care_num</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481769058</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关注他的数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o61">
<a:ObjectID>46EA1600-AD63-45EC-9932-5FAB02106B66</a:ObjectID>
<a:Name>f_share_num</a:Name>
<a:Code>f_share_num</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481769061</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>分享数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o62">
<a:ObjectID>38E30768-ED8F-42A4-A81E-CD30CB9C0FFA</a:ObjectID>
<a:Name>f_answer_num</a:Name>
<a:Code>f_answer_num</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481769064</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>回答数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o63">
<a:ObjectID>BD50341C-6382-4B2F-A004-7FAB48C7F0B7</a:ObjectID>
<a:Name>f_join_quertion_num</a:Name>
<a:Code>f_join_quertion_num</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481769067</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o64">
<a:ObjectID>024CCC50-8B2D-45B9-8DE6-EC3606424D8B</a:ObjectID>
<a:Name>f_topic_num</a:Name>
<a:Code>f_topic_num</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481769072</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关联话题数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o65">
<a:ObjectID>DDB31323-6D86-4FB2-A5CF-0798F4CEE997</a:ObjectID>
<a:Name>f_type</a:Name>
<a:Code>f_type</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481770911</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家类别 0内部专家(默认) 1外部专家</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o66">
<a:ObjectID>D6ACFCC2-7B36-4D1C-981A-0B397137D7DC</a:ObjectID>
<a:Name>f_active_status</a:Name>
<a:Code>f_active_status</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481770911</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>活动状态 0(草稿) 1活动(发布) 2冻结 3解聘 4离职</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o67">
<a:ObjectID>442A997C-94E9-4478-A541-2271C9D96907</a:ObjectID>
<a:Name>f_login_recommend</a:Name>
<a:Code>f_login_recommend</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>首次登陆推荐：0不推荐(默认)  1推荐</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o68">
<a:ObjectID>F3C4E15D-6CD4-4D3E-9BF3-C2E666D6E31D</a:ObjectID>
<a:Name>f_index_recommend</a:Name>
<a:Code>f_index_recommend</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481770911</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>首页推荐：0不推荐(默认) 1推荐</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o69">
<a:ObjectID>0C34E1E0-88A3-4B06-9E6B-E45C61082D19</a:ObjectID>
<a:Name>f_recommend_sort</a:Name>
<a:Code>f_recommend_sort</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>推荐排序</a:Comment>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o70">
<a:ObjectID>82F59C56-BE18-4E83-A6D7-B8FD9282E019</a:ObjectID>
<a:Name>f_name</a:Name>
<a:Code>f_name</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>外部专家名</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o71">
<a:ObjectID>FCD37703-783D-47F5-B460-82612B50032F</a:ObjectID>
<a:Name>f_org</a:Name>
<a:Code>f_org</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>外部专家机构</a:Comment>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o72">
<a:ObjectID>9B25051B-3E92-46F4-98D2-D5ED09DF5FB9</a:ObjectID>
<a:Name>f_domain_id</a:Name>
<a:Code>f_domain_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>所属域</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o73">
<a:ObjectID>C574AC8F-42CD-4F1F-A026-897A7E3B6C42</a:ObjectID>
<a:Name>f_delete_flag</a:Name>
<a:Code>f_delete_flag</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481772239</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态：0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o74">
<a:ObjectID>C8FF7224-E6A8-4CE7-BEA8-452B38AAB62F</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o75">
<a:ObjectID>EF25E12B-4835-4F2D-9010-A0ED4BDF5696</a:ObjectID>
<a:Name>f_dismissal_description</a:Name>
<a:Code>f_dismissal_description</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>解聘理由</a:Comment>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o76">
<a:ObjectID>B1A869F1-87C3-42A9-86CC-100854BA61F3</a:ObjectID>
<a:Name>f_freeze_time</a:Name>
<a:Code>f_freeze_time</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>冻结时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o77">
<a:ObjectID>F5256F05-EB0F-47A2-9201-9149447B5140</a:ObjectID>
<a:Name>f_topic</a:Name>
<a:Code>f_topic</a:Code>
<a:CreationDate>1481778820</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关联话题id，多个逗号隔开</a:Comment>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o78">
<a:ObjectID>7EFF06B9-11F4-4E33-86C3-475555D69EDA</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o54"/>
<o:Column Ref="o74"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o79">
<a:ObjectID>23CCF1E0-86AF-4F91-B6A7-C4AD5B480F30</a:ObjectID>
<a:Name>f_member_id</a:Name>
<a:Code>f_member_id</a:Code>
<a:CreationDate>1481958558</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o80">
<a:ObjectID>ADA81CEC-92BD-496B-9A81-38C775D7885E</a:ObjectID>
<a:CreationDate>1481958558</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Column>
<o:Column Ref="o55"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o81">
<a:ObjectID>104F2D85-D80B-4AB5-A357-9A6D450F5CBF</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481958558</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o82">
<a:ObjectID>338F98EB-014F-48B1-A138-02CC12F2E37F</a:ObjectID>
<a:CreationDate>1481958558</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Column>
<o:Column Ref="o74"/>
</c:Column>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o78"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o33">
<a:ObjectID>25AC3C4B-C981-4F19-AD57-F7D0F6E8A6C2</a:ObjectID>
<a:Name>t_expert_audit(专家审核表)</a:Name>
<a:Code>t_expert_audit</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问吧专家审核表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;专家表审核表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o83">
<a:ObjectID>16272403-0A1C-4D42-B9D4-D3A0ACC7C788</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o84">
<a:ObjectID>0897E366-1815-4587-8B00-47E916DFE992</a:ObjectID>
<a:Name>f_expert_id</a:Name>
<a:Code>f_expert_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>用户id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>47E339A4-5468-400F-A551-26F7FF55410A</a:ObjectID>
<a:Name>f_advantage</a:Name>
<a:Code>f_advantage</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>申请理由(专家优势 )</a:Comment>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>F35B8E8C-207A-435D-A7CC-F2D4A0CDEACE</a:ObjectID>
<a:Name>f_apply_time</a:Name>
<a:Code>f_apply_time</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>申请时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>DFE02C73-8C52-4435-A13E-99A8CAD791D7</a:ObjectID>
<a:Name>f_apply_type</a:Name>
<a:Code>f_apply_type</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481770872</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>申请类别：0身份审核  1更改话题</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>B6117D41-1AB8-46A2-A554-0C451B477B39</a:ObjectID>
<a:Name>f_audit_note</a:Name>
<a:Code>f_audit_note</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核说明</a:Comment>
<a:DataType>text</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>4BFBC38B-E496-49EA-A055-92F8155BD226</a:ObjectID>
<a:Name>f_audit_status</a:Name>
<a:Code>f_audit_status</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481770872</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核状态： 0待审核(默认) 1通过 2拒绝</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o90">
<a:ObjectID>4442C90F-7B00-4F8D-AAD2-18168E0C2252</a:ObjectID>
<a:Name>f_delete_flag</a:Name>
<a:Code>f_delete_flag</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481772308</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态：0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o91">
<a:ObjectID>E46AEB53-1BEF-4371-B1FE-AFD78F689C87</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>9ED1FC2F-33E2-44E8-B96D-0F176D640F7A</a:ObjectID>
<a:Name>f_audit_suggestion</a:Name>
<a:Code>f_audit_suggestion</a:Code>
<a:CreationDate>1481773025</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核意见</a:Comment>
<a:DataType>varchar(500)</a:DataType>
<a:Length>500</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,272={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=
{9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o93">
<a:ObjectID>8DDCAD29-9DBD-4003-BF60-9BCBDB515BC8</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o83"/>
<o:Column Ref="o91"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o93"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o34">
<a:ObjectID>3DC7F105-FEF3-42F6-8E03-5CB2728CD468</a:ObjectID>
<a:Name>t_expert_audit_topic(专家变更擅长话题待审核表)</a:Name>
<a:Code>t_expert_audit_topic</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问吧专家变更擅长话题待审核表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;专家变更擅长话题待审核表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o94">
<a:ObjectID>EE032467-D4B8-450D-9AF6-A1C679AA736F</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o95">
<a:ObjectID>2FF7F35E-4493-4F28-BEA5-C6D89936887D</a:ObjectID>
<a:Name>f_expert_audit_id</a:Name>
<a:Code>f_expert_audit_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家审核id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>5F49AB55-472D-4E0C-A9C4-F4F27EFB6ADE</a:ObjectID>
<a:Name>f_topic_id</a:Name>
<a:Code>f_topic_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>话题id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>43EFC8CD-09BB-4A40-A91D-2F9296155B69</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>FA4F0CDB-CBFC-410B-850D-2E6C4B6C33EF</a:ObjectID>
<a:Name>f_delete_flag</a:Name>
<a:Code>f_delete_flag</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481772324</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态：0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>C266BEC2-6256-433F-B7B4-E70C50E9FCB1</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o100">
<a:ObjectID>FA7C2B67-6881-4C62-9173-B86B6FC50DBB</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o94"/>
<o:Column Ref="o99"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o100"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o35">
<a:ObjectID>88A4FC4A-9EB1-405F-A017-572083E19FE2</a:ObjectID>
<a:Name>t_expert_topic_rel(专家话题关联表)</a:Name>
<a:Code>t_expert_topic_rel</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问吧专家话题关联表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;专家话题关联表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o101">
<a:ObjectID>C0D5338C-9B41-4A18-9480-E7DFC99A0796</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>9E6BAB28-97F3-4887-AAC7-67A1F6891A2E</a:ObjectID>
<a:Name>f_expert_id</a:Name>
<a:Code>f_expert_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>3EF365C1-FE19-4D87-AA7B-2A815025069E</a:ObjectID>
<a:Name>f_topic_id</a:Name>
<a:Code>f_topic_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>话题id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>141B72BF-7D92-49AE-B5E3-6268FCD04D84</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>DE579EE9-20B0-4079-8931-910C147CFF36</a:ObjectID>
<a:Name>f_delete_flag</a:Name>
<a:Code>f_delete_flag</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481772138</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>8AA98A22-3907-492F-B3AC-9738EAEF90CD</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o107">
<a:ObjectID>841B6B40-0480-402C-B31A-B5AA896C06EF</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o101"/>
<o:Column Ref="o106"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o107"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o37">
<a:ObjectID>B8242054-57F8-4B4F-88FF-E3EA893C4DAD</a:ObjectID>
<a:Name>t_expert_qualifications(专家资质表)</a:Name>
<a:Code>t_expert_qualifications</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问吧专家资质表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;专家资质表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o108">
<a:ObjectID>D09FED2A-2CE3-4FC1-982A-3E4B9C17F83E</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o109">
<a:ObjectID>795DF90C-B187-4D3F-B267-2B8902B05BC9</a:ObjectID>
<a:Name>f_qualifications</a:Name>
<a:Code>f_qualifications</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质</a:Comment>
<a:DataType>text</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>E3E1A307-35D4-4ECD-934E-3D0EE6ADA65E</a:ObjectID>
<a:Name>f_enclosure_name</a:Name>
<a:Code>f_enclosure_name</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质附件名称</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>CAC53B5A-73AA-424D-A067-5E4218166722</a:ObjectID>
<a:Name>f_enclosure_url</a:Name>
<a:Code>f_enclosure_url</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质附件源文件地址</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>14042A3C-9EE6-409E-8205-F15EEF149298</a:ObjectID>
<a:Name>f_transfer_view_url</a:Name>
<a:Code>f_transfer_view_url</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质附件预览地址</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>BAF8F394-A72F-4102-8BC2-91A78848AD4F</a:ObjectID>
<a:Name>f_transfer_flag</a:Name>
<a:Code>f_transfer_flag</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质附件转换状态</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>3CAE37FE-2359-4F98-B86A-6FB0481E6BFD</a:ObjectID>
<a:Name>f_enclosure_suffix_img</a:Name>
<a:Code>f_enclosure_suffix_img</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质附件显示图标</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>89180075-CA08-4491-8E7A-07499B778C68</a:ObjectID>
<a:Name>f_enclosure_suffix</a:Name>
<a:Code>f_enclosure_suffix</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质附件文件类型后缀</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>0D773EAF-011E-4454-A5BA-9EAB4318ECD1</a:ObjectID>
<a:Name>f_enclosure_type</a:Name>
<a:Code>f_enclosure_type</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>专家资质附件文件类型 1：文档  2：多媒体 3：Epub电子书</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>917ECE68-1529-4BC0-A790-DE501932E458</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>E1A1AD2D-2A84-432B-BDB4-7EED0631D51C</a:ObjectID>
<a:Name>f_delete_flag</a:Name>
<a:Code>f_delete_flag</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481772357</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态：0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>F211FC9F-304E-4B19-82B0-B09BDD838E6E</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o120">
<a:ObjectID>9FC98D25-227F-458D-8D89-7B11145CA938</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481768383</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768383</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o108"/>
<o:Column Ref="o119"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o120"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o38">
<a:ObjectID>4467E533-2041-4BEF-93E9-C15EC7439728</a:ObjectID>
<a:Name>t_question_attach(附件表)</a:Name>
<a:Code>t_question_attach</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问道问题附件表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;问吧附件表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o121">
<a:ObjectID>B498E208-B2A0-423C-90B9-928C133A5BF2</a:ObjectID>
<a:Name>t_id</a:Name>
<a:Code>t_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>04B105A0-A4E4-4542-AE6B-772997C3A721</a:ObjectID>
<a:Name>t_rel_id</a:Name>
<a:Code>t_rel_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关联的id，问题，评论，回复等的id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>587FE649-9795-459B-B8AF-8B90F6691FA1</a:ObjectID>
<a:Name>t_rel_object_type</a:Name>
<a:Code>t_rel_object_type</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关联对象类型 1问题 2问题补充 3讨论 4回复</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>E1ABA4A4-8843-4E67-B65F-E485AC2853D5</a:ObjectID>
<a:Name>t_name</a:Name>
<a:Code>t_name</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>文件名，上传时的名</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>6906C4ED-4C4E-47D6-8792-D8C8378E85A2</a:ObjectID>
<a:Name>t_original_file</a:Name>
<a:Code>t_original_file</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>上传到服务器地址转换前的名，用于下载</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>3DC9B342-FE71-4E46-A60F-3250FA6D4E53</a:ObjectID>
<a:Name>t_view_name</a:Name>
<a:Code>t_view_name</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>转换后的文件地址，用于预览</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>2005369F-2B06-4D3D-BB33-D0E8DC28D584</a:ObjectID>
<a:Name>t_suffix</a:Name>
<a:Code>t_suffix</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>文档后缀类型</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>4B15E831-AABA-4D5C-B46F-B54CF3AE0EBA</a:ObjectID>
<a:Name>t_suffix_img</a:Name>
<a:Code>t_suffix_img</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>文档后缀类型表示图片的路径</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>5DD68D4D-E55B-458F-8C36-B3DF038B3E5E</a:ObjectID>
<a:Name>t_type</a:Name>
<a:Code>t_type</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>文件类型： 1图片 2文档 3视频音频</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>BDBC952D-05D1-4D73-AB31-85A616225115</a:ObjectID>
<a:Name>t_transfer_flag</a:Name>
<a:Code>t_transfer_flag</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>mq转换状态： 0、转换中，1、转换成功，2、转换失败</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o131">
<a:ObjectID>4AAE1F3C-5939-4564-B84D-77D0F7A7E0B2</a:ObjectID>
<a:Name>f_create_member_id</a:Name>
<a:Code>f_create_member_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o132">
<a:ObjectID>D94911E3-7C90-47FF-9390-AABA558BE958</a:ObjectID>
<a:Name>t_download_num</a:Name>
<a:Code>t_download_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780769</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>下载量</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o133">
<a:ObjectID>CEB858BE-DCDA-4E0C-B0A6-509CDD5CB13D</a:ObjectID>
<a:Name>t_view_num</a:Name>
<a:Code>t_view_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780772</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>浏览量</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>A6959735-4DC8-40E5-A241-FB97B0AB7E85</a:ObjectID>
<a:Name>t_create_time</a:Name>
<a:Code>t_create_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>D3893CD5-5D1C-4F02-80E8-97702E1467A1</a:ObjectID>
<a:Name>t_delete_flag</a:Name>
<a:Code>t_delete_flag</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780776</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态 0未删除(默认)  1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>71A229A2-587C-4A67-817D-17B723137676</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>1BF2D4C9-2766-4E89-9405-4EEE4F27F3E9</a:ObjectID>
<a:Name>t_thumbnail</a:Name>
<a:Code>t_thumbnail</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>缩略图</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>BA8B22BF-8E89-4976-BD7A-8C3701A88799</a:ObjectID>
<a:Name>t_size</a:Name>
<a:Code>t_size</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>文件大小</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o139">
<a:ObjectID>8F709266-2170-4B46-8A35-2F179152C585</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o121"/>
<o:Column Ref="o136"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o139"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o39">
<a:ObjectID>022CE7C1-11F0-45DB-8CB6-F92A5457A9FB</a:ObjectID>
<a:Name>t_question_reviewed(审核记录表)</a:Name>
<a:Code>t_question_reviewed</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问道问题审核表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;申请审核记录表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o140">
<a:ObjectID>0473094B-3392-4936-82F6-CDA02E90771B</a:ObjectID>
<a:Name>t_id</a:Name>
<a:Code>t_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>3E554A70-0F7A-4FD3-9AF9-8228603E1133</a:ObjectID>
<a:Name>t_question_id</a:Name>
<a:Code>t_question_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问题id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>1A771B7A-9BDF-488A-9D40-4AB54A6CB623</a:ObjectID>
<a:Name>t_audit_type</a:Name>
<a:Code>t_audit_type</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核类型：1提问审核 2讨论审核 3回复审核 4提问举报审核 5讨论举报审核 6回复举报审核7补充内容审核8讨论区评论审核9讨论区回复审核10讨论区评论举报审核11讨论区回复举报审核</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(2)</a:DataType>
<a:Length>2</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>0E880C19-C118-454E-A3D3-A5C85D148F61</a:ObjectID>
<a:Name>t_object_id</a:Name>
<a:Code>t_object_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核对象id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>FCE779D5-FE75-47A0-9FC8-92806CA46225</a:ObjectID>
<a:Name>t_content</a:Name>
<a:Code>t_content</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核内容</a:Comment>
<a:DataType>varchar(6000)</a:DataType>
<a:Length>6000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>81E9B7C1-9166-411A-AA7A-FECFCC245944</a:ObjectID>
<a:Name>t_create_user_id</a:Name>
<a:Code>t_create_user_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>提交人</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>F26BCC1D-AF62-4CC8-BB02-B04B71832E4B</a:ObjectID>
<a:Name>t_create_time</a:Name>
<a:Code>t_create_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>FF480054-46AD-4B62-99C8-88406BC68F87</a:ObjectID>
<a:Name>t_audit_user_id</a:Name>
<a:Code>t_audit_user_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核人</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>529A5048-3559-4008-BAB0-4502063338B2</a:ObjectID>
<a:Name>t_audit_time</a:Name>
<a:Code>t_audit_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o149">
<a:ObjectID>5102889D-F147-4123-A817-3984D7564F8A</a:ObjectID>
<a:Name>t_audit_status</a:Name>
<a:Code>t_audit_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780903</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核状态：0待审核(默认)  1通过 2拒绝</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o150">
<a:ObjectID>B66C969C-7AD8-4985-9DA1-7C71130F2690</a:ObjectID>
<a:Name>t_audit_note</a:Name>
<a:Code>t_audit_note</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核说明</a:Comment>
<a:DataType>varchar(1000)</a:DataType>
<a:Length>1000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>40A88B9B-B816-4DB3-BFB9-5A67007AF89E</a:ObjectID>
<a:Name>t_delete_flag</a:Name>
<a:Code>t_delete_flag</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780912</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态：0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>07380128-8E54-418C-BCDE-5EED01DB33B9</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o153">
<a:ObjectID>1C8FE777-151F-4CAA-89B9-4D9849DC292F</a:ObjectID>
<a:Name>t_accuse_num</a:Name>
<a:Code>t_accuse_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780937</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>举报次数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o154">
<a:ObjectID>6464E1E6-D14F-41A7-AABC-F45E5504D7A3</a:ObjectID>
<a:Name>t_be_user_id</a:Name>
<a:Code>t_be_user_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>被举报人id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>9CDD002E-ABAA-4DEA-8CE7-85B2CA716B11</a:ObjectID>
<a:Name>t_source_type</a:Name>
<a:Code>t_source_type</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>来源：1问吧 2知识 3课程 4学习</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>51BB8333-C6F7-4126-9359-A4D38EBD0889</a:ObjectID>
<a:Name>t_need_audit</a:Name>
<a:Code>t_need_audit</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780956</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>是否需要审核 0不需要（默认） 1需要</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o157">
<a:ObjectID>94522F44-C3DD-4EE1-B5F5-4B3958F03549</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o140"/>
<o:Column Ref="o152"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o157"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o40">
<a:ObjectID>984A94E6-1A50-4FEF-A2E5-91B5C20E10EF</a:ObjectID>
<a:Name>t_question_discuss(讨论表)</a:Name>
<a:Code>t_question_discuss</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问道问题讨论表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;讨论表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o158">
<a:ObjectID>61A17A47-F9E1-43AA-B80F-2F74792ABF2C</a:ObjectID>
<a:Name>t_id</a:Name>
<a:Code>t_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>857B2153-057E-40B1-8534-185225236507</a:ObjectID>
<a:Name>t_question_id</a:Name>
<a:Code>t_question_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问题id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>34220D77-B18E-4BED-AFE1-5A10F8982EF8</a:ObjectID>
<a:Name>t_content</a:Name>
<a:Code>t_content</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>回复内容</a:Comment>
<a:DataType>longtext</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>ED7BC7FB-E4D3-4477-A60C-B291B6D43A81</a:ObjectID>
<a:Name>t_json_attach</a:Name>
<a:Code>t_json_attach</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>附件冗余字段</a:Comment>
<a:DataType>longtext</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>BF23D6C2-23A8-4446-B9B9-843F0CB9202B</a:ObjectID>
<a:Name>t_reply_num</a:Name>
<a:Code>t_reply_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780835</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>回复数 默认0</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>615EA187-796E-4B64-BA33-A133D3D8964C</a:ObjectID>
<a:Name>t_praise_num</a:Name>
<a:Code>t_praise_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780837</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>点赞数 默认0</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>E3239CB3-24F4-4566-973C-8C4D848C2E00</a:ObjectID>
<a:Name>t_audit_status</a:Name>
<a:Code>t_audit_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核状态 0未审核(默认)   1通过  2拒绝</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o165">
<a:ObjectID>37F39D31-E2D8-4C2F-8288-3CBF0EFED563</a:ObjectID>
<a:Name>t_essence_status</a:Name>
<a:Code>t_essence_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>是否加精 0否(默认) 1是</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o166">
<a:ObjectID>70208340-89C5-4C4A-A682-A970404FE3CE</a:ObjectID>
<a:Name>t_top_status</a:Name>
<a:Code>t_top_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>顶置状态 0未项置(默认) 1已顶置</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>396952D8-886B-4894-B234-2D74A3F0445E</a:ObjectID>
<a:Name>t_top_time</a:Name>
<a:Code>t_top_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>顶置时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>F4C63FFB-AF72-4561-8E6B-12DC53C755F7</a:ObjectID>
<a:Name>f_create_member_id</a:Name>
<a:Code>f_create_member_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建人id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>E98096E6-9D8E-4CA6-949A-EE4BF7F11128</a:ObjectID>
<a:Name>t_create_time</a:Name>
<a:Code>t_create_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>DB2B0316-48CC-4A32-8066-D77CBE2D3EB7</a:ObjectID>
<a:Name>t_delete_flag</a:Name>
<a:Code>t_delete_flag</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780860</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态 0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>7A3F6CF9-9C43-4C4F-AD05-5DA85826FE63</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o172">
<a:ObjectID>9653A9DD-A857-4984-BC23-D56451CB2F0F</a:ObjectID>
<a:Name>t_content_txt</a:Name>
<a:Code>t_content_txt</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(6000)</a:DataType>
<a:Length>6000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o173">
<a:ObjectID>BC60E5FB-3FC5-44B8-82EB-A63E3396B317</a:ObjectID>
<a:Name>t_accuse_status</a:Name>
<a:Code>t_accuse_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780869</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>举报状态：0未举报默认 1被举报</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>B966103D-C612-4290-A965-7A75BAEC69F3</a:ObjectID>
<a:Name>t_read_status</a:Name>
<a:Code>t_read_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780874</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>阅读状态：0未阅读(默认) 1已阅读</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o175">
<a:ObjectID>48CDFF32-9B64-41B6-8E09-33C0CE759012</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o158"/>
<o:Column Ref="o171"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o175"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o41">
<a:ObjectID>8111ED9E-694C-4D4E-8FC4-7153F3F4074B</a:ObjectID>
<a:Name>t_question(问题表)</a:Name>
<a:Code>t_question</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问道问题表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;问题详细表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o176">
<a:ObjectID>137195C3-E473-4C75-8EAE-32942A80682F</a:ObjectID>
<a:Name>t_id</a:Name>
<a:Code>t_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>1E90828B-7E52-4041-A2B3-FD8C67D31947</a:ObjectID>
<a:Name>t_title</a:Name>
<a:Code>t_title</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>标题</a:Comment>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>F2AD8110-7803-4A76-B29A-C698CB3C0E21</a:ObjectID>
<a:Name>t_type</a:Name>
<a:Code>t_type</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问题类型：1问题 2分享</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>F2E7DBAC-BFD0-4E39-A944-47F6D33887E9</a:ObjectID>
<a:Name>f_flag</a:Name>
<a:Code>f_flag</a:Code>
<a:CreationDate>1481783165</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481783331</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>状态：1活动2关闭</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>8BDFB39C-F517-4B1F-8B7A-A5F3C70BF629</a:ObjectID>
<a:Name>t_topic</a:Name>
<a:Code>t_topic</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关联话题id，多个逗号隔开</a:Comment>
<a:DataType>varchar(2000)</a:DataType>
<a:Length>2000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>2F89A4B9-0CE9-47D1-AA6D-5FC9C578B62E</a:ObjectID>
<a:Name>t_content</a:Name>
<a:Code>t_content</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>内容</a:Comment>
<a:DataType>longtext</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>17ADA2E3-3C4A-430B-84E9-DB43A8CC00CC</a:ObjectID>
<a:Name>t_json_img</a:Name>
<a:Code>t_json_img</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>内容图片</a:Comment>
<a:DataType>longtext</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>B12D7682-8D0B-49A7-9D0E-ABACFFEA63C2</a:ObjectID>
<a:Name>t_content_txt</a:Name>
<a:Code>t_content_txt</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(6000)</a:DataType>
<a:Length>6000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o184">
<a:ObjectID>1E1A0B2F-83AF-456B-A0CE-A223B57B7B91</a:ObjectID>
<a:Name>t_json_attach</a:Name>
<a:Code>t_json_attach</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>内容附件</a:Comment>
<a:DataType>longtext</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o185">
<a:ObjectID>A2DF9F66-B603-44B5-9020-630D05179BEA</a:ObjectID>
<a:Name>f_create_member_id</a:Name>
<a:Code>f_create_member_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建人id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>EE3D2FAC-E401-478D-823E-09653425B682</a:ObjectID>
<a:Name>t_create_time</a:Name>
<a:Code>t_create_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>FF725997-820B-4145-81D0-0A2378D574A0</a:ObjectID>
<a:Name>t_last_modify_time</a:Name>
<a:Code>t_last_modify_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>修改时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>FF4E4BB2-FE0C-4EBA-B837-284877EC16C5</a:ObjectID>
<a:Name>t_browse_num</a:Name>
<a:Code>t_browse_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780636</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>浏览量 默认0</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o189">
<a:ObjectID>91DE1A63-6586-43BA-A653-2143D58590E1</a:ObjectID>
<a:Name>t_discuss_num</a:Name>
<a:Code>t_discuss_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780638</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>讨论数 默认0</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>8B729BE9-BBF4-4788-B3AE-0B041FFBF696</a:ObjectID>
<a:Name>t_forward_num</a:Name>
<a:Code>t_forward_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780642</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>转发数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>6BBEE5E4-DA0D-4186-91E8-9B581227A727</a:ObjectID>
<a:Name>t_praise_num</a:Name>
<a:Code>t_praise_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780644</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>点赞数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o192">
<a:ObjectID>0184103A-F262-43E8-BB19-C95EAA5FDF65</a:ObjectID>
<a:Name>t_collect_num</a:Name>
<a:Code>t_collect_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780647</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>收藏数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>6A5347F0-8BFB-4FD7-A107-3211A75EEF41</a:ObjectID>
<a:Name>t_care_num</a:Name>
<a:Code>t_care_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780649</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关注数</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>63A55E8F-8173-40E6-973F-5BE2CE37CC5B</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>7B7CC282-AF79-4535-A45F-EAB7DC817FD5</a:ObjectID>
<a:Name>t_accuse_status</a:Name>
<a:Code>t_accuse_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780694</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>举报状态：0未举报默认 1被举报</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o196">
<a:ObjectID>43A00D66-C68C-4681-B9DB-0279F5087DEE</a:ObjectID>
<a:Name>t_share_has_cover</a:Name>
<a:Code>t_share_has_cover</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780707</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>分享是否有封面,0:无,1:有</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o197">
<a:ObjectID>B28E7817-2166-4135-9896-0C07B80B57AD</a:ObjectID>
<a:Name>t_share_object_id</a:Name>
<a:Code>t_share_object_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>分享对象的id</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>0968A626-057B-4121-BFD4-890BCC454CCD</a:ObjectID>
<a:Name>t_share_type</a:Name>
<a:Code>t_share_type</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>分享类型:1:课程分享2:学习路径3:知识分享4:班级分享5:调研分享6:微课大赛分享7:考试分享8:专题分享9:直播分享</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(2)</a:DataType>
<a:Length>2</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>2132358C-51F5-4CA2-8411-C8A6140BD0C5</a:ObjectID>
<a:Name>t_share_title</a:Name>
<a:Code>t_share_title</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>分享标题</a:Comment>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>FFFD5DD3-5406-4024-B51F-644C9F8C4987</a:ObjectID>
<a:Name>t_essence_status</a:Name>
<a:Code>t_essence_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780661</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>是否加精   0非精品（默认） 1精品</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o201">
<a:ObjectID>67DD4429-C474-4321-89E9-6C085A4B152B</a:ObjectID>
<a:Name>t_close_status</a:Name>
<a:Code>t_close_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780663</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>关闭状态 0未关闭(默认)  1已关闭</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o202">
<a:ObjectID>F8175FC0-F169-4C3A-AA0F-522788B2CF13</a:ObjectID>
<a:Name>t_top_status</a:Name>
<a:Code>t_top_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780667</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>顶置状态 0未项置(默认) 1已顶置</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>73E421F9-ED8A-4F36-A0F8-60CE3DB5BC6E</a:ObjectID>
<a:Name>t_top_time</a:Name>
<a:Code>t_top_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>顶置时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>95FB02EA-EBD3-4DAE-961F-87AE98169074</a:ObjectID>
<a:Name>t_delete_flag</a:Name>
<a:Code>t_delete_flag</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态 0未删除(默认)  1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>varchar(1)</a:DataType>
<a:Length>1</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o205">
<a:ObjectID>B6387D54-E4F7-41D6-86BA-E1BE97686146</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o176"/>
<o:Column Ref="o194"/>
</c:Key.Columns>
</o:Key>
<o:Key Id="o206">
<a:ObjectID>E6D0352B-5ED4-45F7-87BD-A7EB8D04C849</a:ObjectID>
<a:Name>idx_bar_question_iccdaattdc</a:Name>
<a:Code>idx_bar_question_iccdaattdc</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481784111</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>idx_bar_question_iccdaattdc</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o176"/>
<o:Column Ref="o185"/>
<o:Column Ref="o194"/>
<o:Column Ref="o204"/>
<o:Column Ref="o195"/>
<o:Column Ref="o178"/>
<o:Column Ref="o203"/>
<o:Column Ref="o202"/>
<o:Column Ref="o189"/>
<o:Column Ref="o186"/>
</c:Key.Columns>
</o:Key>
<o:Key Id="o207">
<a:ObjectID>2D289643-1F17-4F81-AB6F-A0C63605ABED</a:ObjectID>
<a:Name>idx_bar_question_cdac</a:Name>
<a:Code>idx_bar_question_cdac</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>idx_bar_question_cdac</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o194"/>
<o:Column Ref="o204"/>
<o:Column Ref="o195"/>
<o:Column Ref="o193"/>
</c:Key.Columns>
</o:Key>
<o:Key Id="o208">
<a:ObjectID>79FBED20-2041-4EFE-AC21-D37CA2DA608A</a:ObjectID>
<a:Name>idx_bar_qestuon_cdcec</a:Name>
<a:Code>idx_bar_qestuon_cdcec</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481784111</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>idx_bar_qestuon_cdcec</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o194"/>
<o:Column Ref="o204"/>
<o:Column Ref="o186"/>
<o:Column Ref="o200"/>
<o:Column Ref="o201"/>
</c:Key.Columns>
</o:Key>
<o:Key Id="o209">
<a:ObjectID>3F49D5FD-9CA0-4B9B-9FD1-09A7605A7191</a:ObjectID>
<a:Name>idx_bar_question_icdaa</a:Name>
<a:Code>idx_bar_question_icdaa</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481784111</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>idx_bar_question_icdaa</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o176"/>
<o:Column Ref="o194"/>
<o:Column Ref="o204"/>
<o:Column Ref="o195"/>
<o:Column Ref="o186"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Indexes>
<o:Index Id="o210">
<a:ObjectID>EA3D813E-4206-4C07-8BF8-CEBFC6AB8BE0</a:ObjectID>
<a:Name>idx_bar_qestuon_cdcec</a:Name>
<a:Code>idx_bar_qestuon_cdcec</a:Code>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o211">
<a:ObjectID>4333B747-FA2D-4B99-8E00-28A75F78837F</a:ObjectID>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:IndexColumn.Expression>&lt;IndexExpression_0&gt;</a:IndexColumn.Expression>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o212">
<a:ObjectID>4B4DD037-1357-4913-A67C-AE1988DDD873</a:ObjectID>
<a:Name>idx_bar_question_cdac</a:Name>
<a:Code>idx_bar_question_cdac</a:Code>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o213">
<a:ObjectID>6D95DC33-6CA8-4202-9DB9-0A2A4D9902D3</a:ObjectID>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:IndexColumn.Expression>&lt;IndexExpression_0&gt;</a:IndexColumn.Expression>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o214">
<a:ObjectID>23E45051-C120-4455-AD78-3A0F4E16E971</a:ObjectID>
<a:Name>idx_bar_question_iccdaattdc</a:Name>
<a:Code>idx_bar_question_iccdaattdc</a:Code>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o215">
<a:ObjectID>01DFE995-747E-4C74-9FC2-4B9FB6176433</a:ObjectID>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:IndexColumn.Expression>&lt;IndexExpression_0&gt;</a:IndexColumn.Expression>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
<o:Index Id="o216">
<a:ObjectID>6DC69F03-7E15-4F84-81D4-9C48E244D8FA</a:ObjectID>
<a:Name>idx_bar_question_icdaa</a:Name>
<a:Code>idx_bar_question_icdaa</a:Code>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:IndexColumns>
<o:IndexColumn Id="o217">
<a:ObjectID>4626E61E-03DD-46AA-8930-7A4EF084ACB1</a:ObjectID>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:IndexColumn.Expression>&lt;IndexExpression_0&gt;</a:IndexColumn.Expression>
</o:IndexColumn>
</c:IndexColumns>
</o:Index>
</c:Indexes>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o205"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o42">
<a:ObjectID>401AB8F6-90E6-44BD-A7D7-018FB25FF8FC</a:ObjectID>
<a:Name>t_question_reply(回复表)</a:Name>
<a:Code>t_question_reply</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问道问题回复表</a:Comment>
<a:PhysicalOptions>ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT=&#39;回复表&#39;</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o218">
<a:ObjectID>B3ED5E4A-9921-40B2-AA73-B1AFE6FB6B5E</a:ObjectID>
<a:Name>t_id</a:Name>
<a:Code>t_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o219">
<a:ObjectID>4F5428FD-E2F3-48EA-B0FF-D54D53C3C8E2</a:ObjectID>
<a:Name>t_question_id</a:Name>
<a:Code>t_question_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>问题id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o220">
<a:ObjectID>FFF94612-3E2C-4EC7-82FC-6010E2541764</a:ObjectID>
<a:Name>t_content</a:Name>
<a:Code>t_content</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>回复内容</a:Comment>
<a:DataType>longtext</a:DataType>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o221">
<a:ObjectID>9842BB64-EABC-4D1E-A7B5-84B606C2AD9D</a:ObjectID>
<a:Name>t_discuss_id</a:Name>
<a:Code>t_discuss_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>讨论id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o222">
<a:ObjectID>DAED72F4-DD89-4E4E-95BC-578055DCA3CC</a:ObjectID>
<a:Name>t_to_user_id</a:Name>
<a:Code>t_to_user_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>被回复的人id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o223">
<a:ObjectID>53BEB72B-D28C-49CC-B345-949A5924A1A6</a:ObjectID>
<a:Name>t_praise_num</a:Name>
<a:Code>t_praise_num</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780578</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>点赞数 默认0</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(11)</a:DataType>
<a:Length>11</a:Length>
</o:Column>
<o:Column Id="o224">
<a:ObjectID>87DA6391-16FF-4469-848C-BC48AA0E3E46</a:ObjectID>
<a:Name>t_audit_status</a:Name>
<a:Code>t_audit_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780578</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>审核状态 0未审核(默认)   1通过  2拒绝</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o225">
<a:ObjectID>3DCA9792-B51C-425B-B1FE-C43B23515659</a:ObjectID>
<a:Name>f_create_member_id</a:Name>
<a:Code>f_create_member_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建人id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o226">
<a:ObjectID>BFC0DABC-3570-476B-8354-6DB6F6249718</a:ObjectID>
<a:Name>t_create_time</a:Name>
<a:Code>t_create_time</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o227">
<a:ObjectID>4640FFFE-6C68-4482-982B-FCFFDF26B69C</a:ObjectID>
<a:Name>t_delete_flag</a:Name>
<a:Code>t_delete_flag</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481782648</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除状态 0未删除(默认) 1已删除</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,102={9480E6AA-83DD-4AA7-9C91-E48F709C29D9},CharSet,0=
{B314652C-DD43-4F81-8524-6F97A1BAACBA},Collate,0=

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o228">
<a:ObjectID>BB2BB049-B007-4D1D-B600-01F58A331383</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织id</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:AutoMigrated>1</a:AutoMigrated>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,170={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true
{4A2BD2F3-4A8A-4421-8A48-A8029BDA28E8},Unsigned,5=false
{4EC131E7-C7D4-4E04-B6F6-A6BF9CCF838C},ZeroFill,5=false

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o229">
<a:ObjectID>DA80A140-69F7-472A-BF32-7C19BDCB68E8</a:ObjectID>
<a:Name>t_accuse_status</a:Name>
<a:Code>t_accuse_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780578</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>举报状态：0未举报默认 1被举报</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o230">
<a:ObjectID>49D07BA8-4911-4E9A-A512-E446C0C573D4</a:ObjectID>
<a:Name>t_read_status</a:Name>
<a:Code>t_read_status</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481780578</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>阅读状态：0未阅读(默认) 1已阅读</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o231">
<a:ObjectID>8047A710-207C-4928-B0C1-903C87EDC102</a:ObjectID>
<a:Name>Key_1</a:Name>
<a:Code>Key_1</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Key.Columns>
<o:Column Ref="o218"/>
<o:Column Ref="o228"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o231"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o44">
<a:ObjectID>0BB4C7F7-E2B6-4835-9EB7-33624B13A33B</a:ObjectID>
<a:Name>t_member</a:Name>
<a:Code>t_member</a:Code>
<a:CreationDate>1481856343</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>用户表</a:Comment>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o232">
<a:ObjectID>68677D39-6376-42A4-B29B-AB8B6FBADFA7</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481856343</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856343</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o233">
<a:ObjectID>28A1E29E-7897-4F58-9F2A-F6942BDADFAB</a:ObjectID>
<a:Name>f_name</a:Name>
<a:Code>f_name</a:Code>
<a:CreationDate>1481856343</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856343</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>人员名称</a:Comment>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o234">
<a:ObjectID>08991D74-3DED-42C5-89ED-901C3A86766F</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481856343</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856343</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织ID</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>ACFD4DC4-3C6E-44A5-84E3-3578286CE35C</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481856343</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856343</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o236">
<a:ObjectID>3C35FE5E-0CB4-41D3-A877-208D16EAE10B</a:ObjectID>
<a:Name>f_full_name</a:Name>
<a:Code>f_full_name</a:Code>
<a:CreationDate>1481856343</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856343</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>姓名</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o237">
<a:ObjectID>81C7BFC2-4437-4FBE-8014-45B2E0F54E6E</a:ObjectID>
<a:Name>f_head_portrait</a:Name>
<a:Code>f_head_portrait</a:Code>
<a:CreationDate>1481878830</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>用户头象</a:Comment>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o238">
<a:ObjectID>CE18ECAF-0E2F-4098-B1AC-2D3BBE6DC430</a:ObjectID>
<a:Name>PK_t_member</a:Name>
<a:Code>PK_t_member</a:Code>
<a:CreationDate>1481856343</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856343</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>PK_t_member</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o232"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o238"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o46">
<a:ObjectID>F2C965D4-BC82-4E46-BB94-CBC4B3BEE715</a:ObjectID>
<a:Name>t_organization_detail</a:Name>
<a:Code>t_organization_detail</a:Code>
<a:CreationDate>1481856624</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o239">
<a:ObjectID>E461EC60-00EA-4EB1-A6A7-AC7D611B2364</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481856624</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856624</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o240">
<a:ObjectID>2E000FB4-6F7D-4A36-928E-ABDB44253C78</a:ObjectID>
<a:Name>f_root</a:Name>
<a:Code>f_root</a:Code>
<a:CreationDate>1481856624</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856624</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>父节点</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o241">
<a:ObjectID>64C828E4-6417-4554-9794-3D5CD16E8D03</a:ObjectID>
<a:Name>f_sub</a:Name>
<a:Code>f_sub</a:Code>
<a:CreationDate>1481856624</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856624</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>子节点</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o242">
<a:ObjectID>DB22FD6E-C530-4DEB-8753-643928EA63D8</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481856624</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856624</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o243">
<a:ObjectID>0D527420-9CED-4863-99A5-577D2FBA8002</a:ObjectID>
<a:Name>PK_t_organization_detail</a:Name>
<a:Code>PK_t_organization_detail</a:Code>
<a:CreationDate>1481856624</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856624</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>PK_t_organization_detail</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o239"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o243"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o48">
<a:ObjectID>8547A960-10B2-4497-B3B1-20EF4D98A709</a:ObjectID>
<a:Name>t_organization</a:Name>
<a:Code>t_organization</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o244">
<a:ObjectID>C44D067D-154E-4414-B817-AAE8578547AD</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>6B474AB4-57E3-4472-A416-25CC36955E10</a:ObjectID>
<a:Name>f_name</a:Name>
<a:Code>f_name</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织名称</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>3B37A5E5-1275-4C88-B41A-22C83A68E6FA</a:ObjectID>
<a:Name>f_parent_id</a:Name>
<a:Code>f_parent_id</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>上级组织</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o247">
<a:ObjectID>0FEA925A-439D-416A-8A49-E95A0E1860E8</a:ObjectID>
<a:Name>f_level</a:Name>
<a:Code>f_level</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>级别</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o248">
<a:ObjectID>194AD954-1C4F-4B67-AD66-D3C5983C633A</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o249">
<a:ObjectID>8FEB5EED-5E1F-40D9-8FB2-11C73BB65358</a:ObjectID>
<a:Name>f_code</a:Name>
<a:Code>f_code</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>编码</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o250">
<a:ObjectID>2E608307-45B0-47AD-B2B6-CB4C3AC19616</a:ObjectID>
<a:Name>f_status</a:Name>
<a:Code>f_status</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>状态</a:Comment>
<a:DataType>int(2)</a:DataType>
<a:Length>2</a:Length>
</o:Column>
<o:Column Id="o251">
<a:ObjectID>66F77421-BB4D-43FF-AF79-9E1458F7FBFC</a:ObjectID>
<a:Name>f_company_id</a:Name>
<a:Code>f_company_id</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>所属机构</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o252">
<a:ObjectID>280EC77B-82B8-44DC-9F89-70BBBABFCA90</a:ObjectID>
<a:Name>f_order</a:Name>
<a:Code>f_order</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>顺序</a:Comment>
<a:DataType>int(5)</a:DataType>
<a:Length>5</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o253">
<a:ObjectID>7AA42881-A3AA-4FD0-928B-8656F497A773</a:ObjectID>
<a:Name>PK_t_organization</a:Name>
<a:Code>PK_t_organization</a:Code>
<a:CreationDate>1481856674</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856674</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>PK_t_organization</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o244"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o253"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o50">
<a:ObjectID>DDCCC40E-CC91-468D-9483-E3AC71BAC2FD</a:ObjectID>
<a:Name>t_grant_detail</a:Name>
<a:Code>t_grant_detail</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o254">
<a:ObjectID>8E8421A4-CF8D-4CA4-B8DC-485A828624CB</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o255">
<a:ObjectID>090DDFF4-D6E3-4148-A2E8-7F7FA22580F3</a:ObjectID>
<a:Name>f_grant_id</a:Name>
<a:Code>f_grant_id</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>授权ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o256">
<a:ObjectID>D1EC0825-7A26-401E-9721-09CE9980BC83</a:ObjectID>
<a:Name>f_member_id</a:Name>
<a:Code>f_member_id</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>人员ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o257">
<a:ObjectID>5137D889-0053-496E-894D-5476289F80BE</a:ObjectID>
<a:Name>f_organization_id</a:Name>
<a:Code>f_organization_id</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>组织ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o258">
<a:ObjectID>CB28C838-D7EC-4791-B143-42FC7CD5B680</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o259">
<a:ObjectID>A4D5C26F-327A-4CFC-ABA4-4D09639176EC</a:ObjectID>
<a:Name>f_uri</a:Name>
<a:Code>f_uri</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>uri</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o260">
<a:ObjectID>7551107D-624D-4C79-B5FC-96C279DD6F28</a:ObjectID>
<a:Name>f_operator_types</a:Name>
<a:Code>f_operator_types</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>操作类型</a:Comment>
<a:DataType>varchar(45)</a:DataType>
<a:Length>45</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o261">
<a:ObjectID>3D7B93F0-8307-40FB-AE85-C6660D1779E4</a:ObjectID>
<a:Name>PK_t_grant_detail</a:Name>
<a:Code>PK_t_grant_detail</a:Code>
<a:CreationDate>1481856717</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481856717</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>PK_t_grant_detail</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o254"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o261"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o52">
<a:ObjectID>88CA11CD-B86E-4AB2-BEF1-CDAB3BFE0FCF</a:ObjectID>
<a:Name>t_topic</a:Name>
<a:Code>t_topic</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o262">
<a:ObjectID>98F9C0AC-23A2-4CAF-A16E-A4F33CAD1B34</a:ObjectID>
<a:Name>f_id</a:Name>
<a:Code>f_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:Column.Mandatory>1</a:Column.Mandatory>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o263">
<a:ObjectID>73705ECF-1991-4F37-8BF0-F38FCB81394A</a:ObjectID>
<a:Name>f_name</a:Name>
<a:Code>f_name</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>话题名称</a:Comment>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o264">
<a:ObjectID>3688718C-028A-452D-A56A-D17BE54264BD</a:ObjectID>
<a:Name>f_cover_url</a:Name>
<a:Code>f_cover_url</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>封面图片路径</a:Comment>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o265">
<a:ObjectID>08A3379D-036C-4242-B18B-21C42F4B0A0B</a:ObjectID>
<a:Name>f_type_id</a:Name>
<a:Code>f_type_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>话题类别ID</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o266">
<a:ObjectID>52FDD98D-54A2-4038-83F6-7C50F115C2FE</a:ObjectID>
<a:Name>f_state</a:Name>
<a:Code>f_state</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>话题状态（启用1，禁用0）</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o267">
<a:ObjectID>4A5CAC36-58D6-4317-BFFD-D45766940974</a:ObjectID>
<a:Name>f_description</a:Name>
<a:Code>f_description</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>话题描述</a:Comment>
<a:DataType>varchar(3000)</a:DataType>
<a:Length>3000</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o268">
<a:ObjectID>90D3DE0D-B110-4472-A39D-CA26BB9932D8</a:ObjectID>
<a:Name>f_source</a:Name>
<a:Code>f_source</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>来源（1管理员 2用户）</a:Comment>
<a:DataType>int(2)</a:DataType>
<a:Length>2</a:Length>
</o:Column>
<o:Column Id="o269">
<a:ObjectID>04878222-D440-41C4-9F29-4DF619681931</a:ObjectID>
<a:Name>f_publish_member_id</a:Name>
<a:Code>f_publish_member_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>发布人</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o270">
<a:ObjectID>EEEEEF93-130C-49CC-B3D4-96CE7058E713</a:ObjectID>
<a:Name>f_publish_time</a:Name>
<a:Code>f_publish_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>发布时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o271">
<a:ObjectID>5BF38925-C96C-46A5-8BEB-E0823A7B34C2</a:ObjectID>
<a:Name>f_recommend</a:Name>
<a:Code>f_recommend</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>初次登录推荐（推荐1，不推荐0）</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
<o:Column Id="o272">
<a:ObjectID>1E8BE205-0984-4074-A811-5F180277485C</a:ObjectID>
<a:Name>f_create_member_id</a:Name>
<a:Code>f_create_member_id</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>varchar(40)</a:DataType>
<a:Length>40</a:Length>
<a:ExtendedAttributesText>{F4F16ECD-F2F1-4006-AF6F-638D5C65F35E},MYSQL50,56={9C949EAB-FF87-446D-938C-8F03A4ABDC8E},National,4=true

</a:ExtendedAttributesText>
</o:Column>
<o:Column Id="o273">
<a:ObjectID>1B793071-0E41-4EE6-934B-2EBA097CD579</a:ObjectID>
<a:Name>f_create_time</a:Name>
<a:Code>f_create_time</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>bigint(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o274">
<a:ObjectID>02759835-39D5-4A5B-A65D-0E414C26C511</a:ObjectID>
<a:Name>f_delete_flag</a:Name>
<a:Code>f_delete_flag</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Comment>删除标记（0未删除，1已删除）</a:Comment>
<a:DataType>int(1)</a:DataType>
<a:Length>1</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o275">
<a:ObjectID>9F3BC13D-5AD2-4707-8FD4-8EB6B41CF6AA</a:ObjectID>
<a:Name>PK_t_topic</a:Name>
<a:Code>PK_t_topic</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:ConstraintName>PK_t_topic</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o262"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:Owner>
<o:User Ref="o53"/>
</c:Owner>
<c:PrimaryKey>
<o:Key Ref="o275"/>
</c:PrimaryKey>
</o:Table>
</c:Tables>
<c:References>
<o:Reference Id="o8">
<a:ObjectID>A0D312FF-4181-48FE-B660-80B904AC6D2B</a:ObjectID>
<a:Name>Reference_16</a:Name>
<a:Code>Reference_16</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o32"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o33"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o78"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o276">
<a:ObjectID>06231A14-FE14-44ED-B650-5F8E02D969C1</a:ObjectID>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o54"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o83"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o277">
<a:ObjectID>DE4DE1A7-D2AD-4C7B-B9A1-EA0FCE6E1783</a:ObjectID>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o74"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o91"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o11">
<a:ObjectID>B9CD075F-E370-489A-B83A-D78A5C531587</a:ObjectID>
<a:Name>Reference_17</a:Name>
<a:Code>Reference_17</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o32"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o34"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o78"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o278">
<a:ObjectID>996AB87C-8358-475D-8471-963806661F9E</a:ObjectID>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o54"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o94"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o279">
<a:ObjectID>0D0003EB-133D-40BE-AACB-F82BC5FC018D</a:ObjectID>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o74"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o99"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o14">
<a:ObjectID>58FBB931-CCD2-4A11-B8E7-2F957E38A6DC</a:ObjectID>
<a:Name>Reference_25</a:Name>
<a:Code>Reference_25</a:Code>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o32"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o35"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o78"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o280">
<a:ObjectID>56DD667C-03E3-4D00-B354-7B64F9253DA5</a:ObjectID>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o54"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o101"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o281">
<a:ObjectID>F15F4D24-6F0B-4CF9-9D3D-7CDC1C6DD2FC</a:ObjectID>
<a:CreationDate>1481768297</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481768297</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o74"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o106"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o18">
<a:ObjectID>713143BB-7DA6-4831-8413-170F3480B7CA</a:ObjectID>
<a:Name>Reference_1</a:Name>
<a:Code>Reference_1</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o41"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o40"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o205"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o282">
<a:ObjectID>3FC3ED9D-F2C1-459B-A63A-53B8AC172864</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o176"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o158"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o283">
<a:ObjectID>FEF0290E-DE03-4C2E-8DE0-8DA446670F6D</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o194"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o171"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o21">
<a:ObjectID>8CAB7DCC-A21E-49BC-80BF-8F0841C7D910</a:ObjectID>
<a:Name>Reference_2</a:Name>
<a:Code>Reference_2</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o41"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o38"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o205"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o284">
<a:ObjectID>41583541-03EF-4057-BD6E-8A275866D055</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o176"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o121"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o285">
<a:ObjectID>9475F211-7AC4-472A-B973-86B3C59BF3BF</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o194"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o136"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o24">
<a:ObjectID>1197BA16-269C-4DFB-9BA8-A83E6729D614</a:ObjectID>
<a:Name>Reference_7</a:Name>
<a:Code>Reference_7</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481878876</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o41"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o39"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o205"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o286">
<a:ObjectID>A46A390A-8D63-4978-ABB2-4AF8308429F6</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o176"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o140"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o287">
<a:ObjectID>3FC3C438-10E2-41EA-9621-AB4622735BB8</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o194"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o152"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o26">
<a:ObjectID>B5BF2247-E401-42C2-919E-BDF6E0E06419</a:ObjectID>
<a:Name>Reference_8</a:Name>
<a:Code>Reference_8</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o40"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o39"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o175"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o288">
<a:ObjectID>1E8F2D5E-692E-41B5-80E0-88A4B42EA611</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o158"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o140"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o289">
<a:ObjectID>423029ED-5985-4CBC-B20D-74CFB560AA68</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o171"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o152"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o29">
<a:ObjectID>9237E98B-43E5-468E-A629-26B91EB42B87</a:ObjectID>
<a:Name>Reference_9</a:Name>
<a:Code>Reference_9</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o42"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o39"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o231"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o290">
<a:ObjectID>C1B97BB1-1BE7-4BB0-AFAE-ECBC67151F0A</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o218"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o140"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o291">
<a:ObjectID>34216B38-0AF5-4AF6-B956-F783B75391C3</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o228"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o152"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
<o:Reference Id="o31">
<a:ObjectID>C6148218-3900-4D9B-B7DE-85C0FA5228A3</a:ObjectID>
<a:Name>Reference_10</a:Name>
<a:Code>Reference_10</a:Code>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481958711</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:Cardinality>0..*</a:Cardinality>
<c:ParentTable>
<o:Table Ref="o40"/>
</c:ParentTable>
<c:ChildTable>
<o:Table Ref="o42"/>
</c:ChildTable>
<c:ParentKey>
<o:Key Ref="o175"/>
</c:ParentKey>
<c:Joins>
<o:ReferenceJoin Id="o292">
<a:ObjectID>47202258-0651-4755-8013-FAEDFC2C861C</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o158"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o218"/>
</c:Object2>
</o:ReferenceJoin>
<o:ReferenceJoin Id="o293">
<a:ObjectID>5D200C44-FE79-4D9F-958D-388294F6E962</a:ObjectID>
<a:CreationDate>1481779532</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481779532</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Object1>
<o:Column Ref="o171"/>
</c:Object1>
<c:Object2>
<o:Column Ref="o228"/>
</c:Object2>
</o:ReferenceJoin>
</c:Joins>
</o:Reference>
</c:References>
<c:DefaultGroups>
<o:Group Id="o294">
<a:ObjectID>67D9FD73-6FE6-47BF-9C1E-2B18E8B97341</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>1481767707</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1481858484</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<c:Group.Users>
<o:User Ref="o53"/>
</c:Group.Users>
</o:Group>
</c:DefaultGroups>
<c:TargetModels>
<o:TargetModel Id="o295">
<a:ObjectID>67C556FD-ACE3-4C1B-A23D-A6898A5676E2</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>1481767707</a:CreationDate>
<a:Creator>zhanghua</a:Creator>
<a:ModificationDate>1242731549</a:ModificationDate>
<a:Modifier>zhanghua</a:Modifier>
<a:TargetModelURL>file:///%[XDB]%/mysql50.xdb</a:TargetModelURL>
<a:TargetModelID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<a:TargetModelLastModificationDate>1242731549</a:TargetModelLastModificationDate>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>