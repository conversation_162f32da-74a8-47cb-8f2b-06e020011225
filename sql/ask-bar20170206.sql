/*
Navicat MySQL Data Transfer

Source Server         : mw9.zhixueyun.com
Source Server Version : 50716
Source Host           : mw9.zhixueyun.com:10102
Source Database       : ask-bar

Target Server Type    : MYSQL
Target Server Version : 50716
File Encoding         : 65001

Date: 2017-02-06 13:36:44
*/

SET FOREIGN_KEY_CHECKS=0;

-- ----------------------------
-- Table structure for `t_accuse_record`
-- ----------------------------
DROP TABLE IF EXISTS `t_accuse_record`;
CREATE TABLE `t_accuse_record` (
  `f_id` varchar(40) NOT NULL,
  `f_type` varchar(40) DEFAULT NULL COMMENT '举报分类id：1违反法律 2内容不准确 3垃圾信息 4其他',
  `f_accuse_note` varchar(2000) DEFAULT NULL COMMENT '举报说明',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_audit_status` int(1) DEFAULT NULL COMMENT '审核状态',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_audit_record_id` varchar(40) DEFAULT NULL COMMENT '审核记录id',
  `f_object_type` int(1) DEFAULT NULL COMMENT '举报对象类型 ：1问题 2讨论 3回复 4讨论区评论 5讨论区回复',
  `f_object_id` varchar(40) DEFAULT NULL COMMENT '举报对象id',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织id',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='举报记录表';

-- ----------------------------
-- Records of t_accuse_record
-- ----------------------------

-- ----------------------------
-- Table structure for `t_collect`
-- ----------------------------
DROP TABLE IF EXISTS `t_collect`;
CREATE TABLE `t_collect` (
  `f_id` varchar(40) NOT NULL,
  `f_question_id` varchar(40) DEFAULT NULL COMMENT '问题id',
  `f_object_id` varchar(40) DEFAULT NULL COMMENT '收藏对象id',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织id',
  PRIMARY KEY (`f_id`),
  KEY `FK_Reference_8` (`f_id`,`f_question_id`),
  CONSTRAINT `FK_Reference_8` FOREIGN KEY (`f_id`, `f_question_id`) REFERENCES `t_question_discuss` (`f_id`, `f_question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收藏表';

-- ----------------------------
-- Records of t_collect
-- ----------------------------

-- ----------------------------
-- Table structure for `t_concern`
-- ----------------------------
DROP TABLE IF EXISTS `t_concern`;
CREATE TABLE `t_concern` (
  `f_id` varchar(40) NOT NULL,
  `f_concern_id` varchar(40) DEFAULT NULL COMMENT '关注的',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织id',
  `f_concern_type` int(1) DEFAULT NULL COMMENT '关注类型  1.专家、2.问题、3.分享、4.话题',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关注表';

-- ----------------------------
-- Records of t_concern
-- ----------------------------

-- ----------------------------
-- Table structure for `t_enjoy`
-- ----------------------------
DROP TABLE IF EXISTS `t_enjoy`;
CREATE TABLE `t_enjoy` (
  `f_id` varchar(40) NOT NULL,
  `f_topic_type` varchar(2000) DEFAULT NULL COMMENT '话题类别id，逗号分隔',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：N未删除(默认) Y已删除',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织id',
  `f_question_id` varchar(40) NOT NULL COMMENT '问题ID',
  `f_enjoy_id` varchar(40) DEFAULT NULL COMMENT '分享者id',
  `f_enjoy_time` bigint(20) DEFAULT NULL COMMENT '分享时间',
  PRIMARY KEY (`f_id`,`f_question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分享记录表';

-- ----------------------------
-- Records of t_enjoy
-- ----------------------------

-- ----------------------------
-- Table structure for `t_expert`
-- ----------------------------
DROP TABLE IF EXISTS `t_expert`;
CREATE TABLE `t_expert` (
  `f_id` varchar(40) NOT NULL,
  `f_member_id` varchar(40) DEFAULT NULL COMMENT '用户id',
  `f_type` int(1) DEFAULT '0' COMMENT '专家类别 0内部专家(默认) 1外部专家',
  `f_topic` varchar(2000) DEFAULT NULL COMMENT '关联话题id，多个逗号隔开',
  `f_hiring_time` bigint(20) DEFAULT NULL COMMENT '聘用时间，初次为审核通过时间',
  `f_answer_num` int(11) DEFAULT '0' COMMENT '回答数',
  `f_care_num` int(11) DEFAULT '0' COMMENT '关注他的数',
  `f_share_num` int(11) DEFAULT '0' COMMENT '分享数',
  `f_active_status` int(1) DEFAULT '0' COMMENT '活动状态 0(草稿) 1活动(发布) 2冻结 3解聘',
  `f_recommend` int(1) DEFAULT '0' COMMENT '是否推荐：0不推荐(默认)  1推荐',
  `f_recommend_sort` int(11) DEFAULT NULL COMMENT '推荐排序',
  `f_introduce` varchar(2000) DEFAULT NULL COMMENT '专家介绍',
  `f_name` varchar(100) DEFAULT NULL COMMENT '外部专家名',
  `f_org` varchar(500) DEFAULT NULL COMMENT '外部专家机构',
  `f_head_portrait` varchar(200) DEFAULT NULL COMMENT '专家头像',
  `f_dismissal_description` varchar(500) DEFAULT NULL COMMENT '解聘理由',
  `f_freeze_time` bigint(20) DEFAULT NULL COMMENT '冻结时间',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_audit_status` int(1) DEFAULT '0' COMMENT '审核状态：0待审核(默认) 1通过 2拒绝',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问吧专家表';

-- ----------------------------
-- Records of t_expert
-- ----------------------------
INSERT INTO `t_expert` VALUES ('4942e0ee-41c9-48a5-be82-7dd5755b259d', '1e01864c-ae4d-4075-a01e-5642cfbbaf42', '0', 'd48872d1-dcf7-4b9c-990a-1165addc2468,4,3', '1484126203219', '0', '0', '0', '1', '0', null, '123', null, null, null, null, null, '1484126203219', '1', '0');
INSERT INTO `t_expert` VALUES ('615b3106-59ef-441a-b98b-76a243e67833', 'cedd501c-9e7b-4104-81cb-fd12537f3be0', '0', 'd48872d1-dcf7-4b9c-990a-1165addc2468,4,3', '1484129095080', '0', '0', '0', '1', '0', null, ' 啊啊啊', null, null, null, null, null, '1484129095080', '1', null);
INSERT INTO `t_expert` VALUES ('833e70ad-cfc6-4081-9fdd-77c422e6335e', '1', '0', '1', '1484468242783', '0', '0', '0', null, '0', null, '这个专家太好', null, null, null, null, null, '1484468242783', null, null);
INSERT INTO `t_expert` VALUES ('8b749cd6-f6dc-4f36-b505-d7291c65e13a', '1', '0', '4,3,2', '1484129158702', '0', '0', '0', '2', '0', null, ' 啊啊啊', null, null, null, null, '1484129405458', '1484129158702', '1', null);
INSERT INTO `t_expert` VALUES ('f4edc0dc-4cdf-48cf-a204-7d62ca58d29d', '1', '0', '2,3', '1484468641513', '0', '0', '0', null, '0', null, '没有', null, null, null, null, null, '1484468641513', null, null);

-- ----------------------------
-- Table structure for `t_expert_audit`
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_audit`;
CREATE TABLE `t_expert_audit` (
  `f_id` varchar(40) NOT NULL,
  `f_expert_id` varchar(40) DEFAULT NULL COMMENT '用户id',
  `f_advantage` varchar(2000) DEFAULT NULL COMMENT '申请理由(专家优势 )',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '申请时间',
  `f_apply_type` int(1) DEFAULT '0' COMMENT '申请类别：0身份审核  1更改话题   2激活专家',
  `f_old_topic` varchar(2000) DEFAULT NULL COMMENT '原擅长话题id，多个逗号隔开',
  `f_new_topic` varchar(2000) DEFAULT NULL COMMENT '申请话题id，多个逗号隔开',
  `f_audit_note` varchar(2000) DEFAULT NULL COMMENT '审核说明',
  `f_audit_status` int(1) DEFAULT '0' COMMENT '审核状态： 0待审核(默认) 1通过 2拒绝',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  PRIMARY KEY (`f_id`),
  KEY `FK_Reference_16` (`f_expert_id`),
  CONSTRAINT `FK_Reference_16` FOREIGN KEY (`f_expert_id`) REFERENCES `t_expert` (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问吧专家审核表';

-- ----------------------------
-- Records of t_expert_audit
-- ----------------------------
INSERT INTO `t_expert_audit` VALUES ('1', '4942e0ee-41c9-48a5-be82-7dd5755b259d', '啦啦啦啦啦啦', '1484126203219', '0', 'd48872d1-dcf7-4b9c-990a-1165addc2468,4,3', 'd48872d1-dcf7-4b9c-990a-1165addc2468,4,3', '123', '1', '0');
INSERT INTO `t_expert_audit` VALUES ('2', '615b3106-59ef-441a-b98b-76a243e67833', '嘟嘟嘟嘟嘟嘟', '1484126203219', '1', 'd48872d1-dcf7-4b9c-990a-1165addc2468,4,3', '1,2,3', '2541235', '0', '0');

-- ----------------------------
-- Table structure for `t_expert_audit_topic`
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_audit_topic`;
CREATE TABLE `t_expert_audit_topic` (
  `f_id` varchar(40) NOT NULL,
  `f_expert_audit_id` varchar(40) DEFAULT NULL COMMENT '专家审核id',
  `f_topic_id` varchar(40) DEFAULT NULL COMMENT '话题id',
  `f_create_time` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`f_id`),
  KEY `FK_Reference_17` (`f_expert_audit_id`),
  CONSTRAINT `FK_Reference_17` FOREIGN KEY (`f_expert_audit_id`) REFERENCES `t_expert_audit` (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问吧专家变更擅长话题待审核表';

-- ----------------------------
-- Records of t_expert_audit_topic
-- ----------------------------
INSERT INTO `t_expert_audit_topic` VALUES ('1', '2', '1', '1484129095080');
INSERT INTO `t_expert_audit_topic` VALUES ('2', '2', '2', '1484129095080');
INSERT INTO `t_expert_audit_topic` VALUES ('3', '2', '3', '1484129095080');

-- ----------------------------
-- Table structure for `t_expert_qualifications`
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_qualifications`;
CREATE TABLE `t_expert_qualifications` (
  `f_id` varchar(40) NOT NULL,
  `f_qualifications` varchar(2000) DEFAULT NULL COMMENT '专家资质',
  `f_enclosure_name` varchar(100) DEFAULT NULL COMMENT '专家资质附件名称',
  `f_enclosure_url` varchar(100) DEFAULT NULL COMMENT '专家资质附件源文件地址',
  `f_transfer_view_url` varchar(100) DEFAULT NULL COMMENT '专家资质附件预览地址',
  `f_transfer_flag` int(1) DEFAULT NULL COMMENT '专家资质附件转换状态',
  `f_enclosure_suffix_img` varchar(100) DEFAULT NULL COMMENT '专家资质附件显示图标',
  `f_enclosure_suffix` varchar(100) DEFAULT NULL COMMENT '专家资质附件文件类型后缀',
  `f_enclosure_type` int(1) DEFAULT NULL COMMENT '专家资质附件文件类型 1：文档  2：多媒体 3：Epub电子书',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) NOT NULL COMMENT '组织id',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问吧专家资质表';

-- ----------------------------
-- Records of t_expert_qualifications
-- ----------------------------
INSERT INTO `t_expert_qualifications` VALUES ('45f78ac0-4efc-44c0-85ac-15c3fab4288f', 'g<img src=\"/api/v1/system/file/download/1a0f169b-fd2e-4746-aa2a-505bcafe612b\" alt=\"\" />小猫，<img src=\"/api/v1/system/file/download/ef980b87-3027-437c-97b8-f39b363a0820\" alt=\"\" />', '12.2会议纪要.txt', '2b8ce7cf-a4e1-4397-b085-4bb08675719a', null, null, null, 'text/plain', null, '1484121124173', null, '312');

-- ----------------------------
-- Table structure for `t_expert_topic_rel`
-- ----------------------------
DROP TABLE IF EXISTS `t_expert_topic_rel`;
CREATE TABLE `t_expert_topic_rel` (
  `f_id` varchar(40) NOT NULL DEFAULT '',
  `f_expert_id` varchar(40) DEFAULT NULL COMMENT '专家id',
  `f_topic_id` varchar(40) DEFAULT NULL COMMENT '话题id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`),
  KEY `FK_Reference_25` (`f_expert_id`),
  CONSTRAINT `FK_Reference_25` FOREIGN KEY (`f_expert_id`) REFERENCES `t_expert` (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问吧专家话题关联表';

-- ----------------------------
-- Records of t_expert_topic_rel
-- ----------------------------
INSERT INTO `t_expert_topic_rel` VALUES ('0e69a71f-aaf7-4271-bbed-16dc6753d6d1', '4942e0ee-41c9-48a5-be82-7dd5755b259d', 'd48872d1-dcf7-4b9c-990a-1165addc2468', '1484126203384');
INSERT INTO `t_expert_topic_rel` VALUES ('220a2a91-bd12-462e-afef-a8b8d95e2a81', 'f4edc0dc-4cdf-48cf-a204-7d62ca58d29d', '2', '1484468641802');
INSERT INTO `t_expert_topic_rel` VALUES ('402c660c-0843-4e8a-8942-8a084fc56e37', '4942e0ee-41c9-48a5-be82-7dd5755b259d', '4', '1484126203546');
INSERT INTO `t_expert_topic_rel` VALUES ('7426cf6c-92e2-4f56-bea9-d9eed47c7e88', '615b3106-59ef-441a-b98b-76a243e67833', '3', '1484129095477');
INSERT INTO `t_expert_topic_rel` VALUES ('75a619b3-43ba-490c-910d-496bd45ffab0', '833e70ad-cfc6-4081-9fdd-77c422e6335e', '1', '1484468242948');
INSERT INTO `t_expert_topic_rel` VALUES ('a448606a-365f-4b28-89c9-2a84c31744ff', '8b749cd6-f6dc-4f36-b505-d7291c65e13a', '4', '1484129158839');
INSERT INTO `t_expert_topic_rel` VALUES ('ace181f4-84c5-4a26-8af0-8d7bb596cc83', '8b749cd6-f6dc-4f36-b505-d7291c65e13a', '2', '1484129159110');
INSERT INTO `t_expert_topic_rel` VALUES ('ae838302-4580-4bd8-85cb-f742ebf320b7', 'f4edc0dc-4cdf-48cf-a204-7d62ca58d29d', '3', '1484468641966');
INSERT INTO `t_expert_topic_rel` VALUES ('b48b6165-0b23-4d7d-8105-4f96ade5f08f', '8b749cd6-f6dc-4f36-b505-d7291c65e13a', '3', '1484129158979');
INSERT INTO `t_expert_topic_rel` VALUES ('b978f21c-13b5-4d9a-9908-95e67fbbafd9', '615b3106-59ef-441a-b98b-76a243e67833', '4', '1484129095347');
INSERT INTO `t_expert_topic_rel` VALUES ('dc2e5a86-6a7f-4b6e-b2e9-4b32b7baf8b7', '615b3106-59ef-441a-b98b-76a243e67833', 'd48872d1-dcf7-4b9c-990a-1165addc2468', '1484129095217');
INSERT INTO `t_expert_topic_rel` VALUES ('eb4e34e6-7664-49aa-8b95-d6356065be60', '4942e0ee-41c9-48a5-be82-7dd5755b259d', '3', '1484126203710');

-- ----------------------------
-- Table structure for `t_grant_detail`
-- ----------------------------
DROP TABLE IF EXISTS `t_grant_detail`;
CREATE TABLE `t_grant_detail` (
  `f_id` varchar(40) NOT NULL COMMENT 'ID',
  `f_grant_id` varchar(40) DEFAULT NULL COMMENT '授权ID',
  `f_member_id` varchar(40) NOT NULL COMMENT '人员ID',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织ID',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_uri` varchar(40) DEFAULT NULL COMMENT 'uri',
  `f_operator_types` varchar(45) DEFAULT NULL COMMENT '操作类型',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_grant_detail
-- ----------------------------
INSERT INTO `t_grant_detail` VALUES ('1000000000', '10000000', '1', '1', '1482407653477', 'course-study/course-info', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000001', '10000000', '1', '1', '1482407653477', 'course-study/subject-info', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000010', '10000000', '1', '1', '1482407653477', 'system/home-config', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000011', '10000000', '1', '1', '1482407653477', 'operation/advertisement', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000012', '10000000', '1', '1', '1482407653477', 'operation/news', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000013', '10000000', '1', '1', '1482407653477', 'operation/recommend-course', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000014', '10000000', '1', '1', '1482407653477', 'operation/activity-recommend', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000015', '10000000', '1', '1', '1482407653477', 'operation/recommend-subject', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000016', '10000000', '1', '1', '1482407653477', 'operation/speech-audi', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000017', '10000000', '1', '1', '1482407653477', 'system/topic', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000018', '10000000', '1', '1', '1482407653477', 'system/topic-approval', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000019', '10000000', '1', '1', '1482407653477', 'operation/integral', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000002', '10000000', '1', '1', '1482407653477', 'course-study/study-push', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000020', '10000000', '1', '1', '1482407653477', 'operation/message-template', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000021', '10000000', '1', '1', '1482407653477', 'operation/certificate', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000022', '10000000', '1', '1', '1482407653477', 'operation/sensitive', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000023', '10000000', '1', '1', '1482407653477', 'system/msg', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000024', '10000000', '1', '1', '1482407653477', 'operation/share', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000025', '10000000', '1', '1', '1482407653477', 'operation/announcement', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000026', '10000000', '1', '1', '1482407653477', 'operation/speech-set', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000027', '10000000', '1', '1', '1482407653477', 'human/member', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000028', '10000000', '1', '1', '1482407653477', 'human/position', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000029', '10000000', '1', '1', '1482407653477', 'human/job', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000003', '10000000', '1', '1', '1482407653477', 'course-study/course-category', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000030', '10000000', '1', '1', '1482407653477', 'human/job-type', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000031', '10000000', '1', '1', '1482407653477', 'human/tag', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000032', '10000000', '1', '1', '1482407653477', 'human/nationality', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000033', '10000000', '1', '1', '1482407653477', 'human/ethnicity', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000034', '10000000', '1', '1', '1482407653477', 'human/city', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000035', '10000000', '1', '1', '1482407653477', 'human/politicalization', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000036', '10000000', '1', '1', '1482407653477', 'human/education', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000037', '10000000', '1', '1', '1482407653477', 'human/english-level', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000038', '10000000', '1', '1', '1482407653477', 'human/computer-level', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000039', '10000000', '1', '1', '1482407653477', 'human/organization', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000004', '10000000', '1', '1', '1482407653477', 'exam/exam', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000040', '10000000', '1', '1', '1482407653477', 'system/menu', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000041', '10000000', '1', '1', '1482407653477', 'system/role', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000042', '10000000', '1', '1', '1482407653477', 'system/grant', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000043', '10000000', '1', '1', '1482407653477', 'system/admin-member', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000044', '10000000', '1', '1', '1482407653477', 'system/rule', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000045', '10000000', '1', '1', '1482407653477', 'system/data-permission', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('10000000046', '10000000', '1', '1', '1482407653477', 'human/extention', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000005', '10000000', '1', '1', '1482407653477', 'exam/paper', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000006', '10000000', '1', '1', '1482407653477', 'exam/question', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000007', '10000000', '1', '1', '1482407653477', 'exam/question-category', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000008', '10000000', '1', '1', '1482407653477', 'ask-bar/question', '0,1,2,3,4');
INSERT INTO `t_grant_detail` VALUES ('1000000009', '10000000', '1', '1', '1482407653477', 'ask-bar/expert', '0,1,2,3,4');

-- ----------------------------
-- Table structure for `t_integral`
-- ----------------------------
DROP TABLE IF EXISTS `t_integral`;
CREATE TABLE `t_integral` (
  `f_id` varchar(40) NOT NULL,
  `f_integral` decimal(10,2) DEFAULT NULL COMMENT '积分',
  `f_object_type` varchar(2) DEFAULT NULL COMMENT '1提问 2讨论 3回复 4举报 5被举报 6关注专家 7问题讨论数 8问题收藏数 9问题浏览数 10讨论点赞数 11设为精品 12加精 13成功申请专家',
  `f_object_id` varchar(40) DEFAULT NULL COMMENT '目标表ID',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织id',
  `f_member_id` varchar(40) DEFAULT NULL COMMENT '积分获得者ID',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='积分详情表';

-- ----------------------------
-- Records of t_integral
-- ----------------------------

-- ----------------------------
-- Table structure for `t_member`
-- ----------------------------
DROP TABLE IF EXISTS `t_member`;
CREATE TABLE `t_member` (
  `f_id` varchar(40) NOT NULL COMMENT 'ID',
  `f_name` varchar(20) DEFAULT NULL COMMENT '人员名称',
  `f_organization_id` varchar(45) DEFAULT NULL COMMENT '组织ID',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_full_name` varchar(45) DEFAULT NULL COMMENT '姓名',
  `f_head_portrait` varchar(200) DEFAULT NULL COMMENT '用户头象',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';

-- ----------------------------
-- Records of t_member
-- ----------------------------
INSERT INTO `t_member` VALUES ('1', 'admin', '1', '1471689143605', '超级管理员', null);
INSERT INTO `t_member` VALUES ('1e01864c-ae4d-4075-a01e-5642cfbbaf42', 'chengzhi', '4ee81f28-a79d-4960-8778-3499e8209412', '1484045269177', '程志', null);
INSERT INTO `t_member` VALUES ('cedd501c-9e7b-4104-81cb-fd12537f3be0', 'masong11', '1', '1484099284886', 'ms', null);

-- ----------------------------
-- Table structure for `t_organization`
-- ----------------------------
DROP TABLE IF EXISTS `t_organization`;
CREATE TABLE `t_organization` (
  `f_id` varchar(40) NOT NULL COMMENT 'ID',
  `f_name` varchar(45) DEFAULT NULL COMMENT '组织名称',
  `f_parent_id` varchar(45) DEFAULT NULL COMMENT '上级组织',
  `f_level` int(1) DEFAULT NULL COMMENT '级别',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_code` varchar(45) DEFAULT NULL COMMENT '编码',
  `f_status` int(2) DEFAULT NULL COMMENT '状态',
  `f_company_id` varchar(45) DEFAULT NULL COMMENT '所属机构',
  `f_order` int(5) DEFAULT NULL COMMENT '顺序',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_organization
-- ----------------------------
INSERT INTO `t_organization` VALUES ('1', '知学云', null, '2', '1471689143605', 'zxy', '1', null, '1');

-- ----------------------------
-- Table structure for `t_organization_detail`
-- ----------------------------
DROP TABLE IF EXISTS `t_organization_detail`;
CREATE TABLE `t_organization_detail` (
  `f_id` varchar(40) NOT NULL COMMENT 'ID',
  `f_root` varchar(45) DEFAULT NULL COMMENT '父节点',
  `f_sub` varchar(40) DEFAULT NULL COMMENT '子节点',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_organization_detail
-- ----------------------------
INSERT INTO `t_organization_detail` VALUES ('10000', '1', '1', '1471689378657');

-- ----------------------------
-- Table structure for `t_praise`
-- ----------------------------
DROP TABLE IF EXISTS `t_praise`;
CREATE TABLE `t_praise` (
  `id` varchar(40) NOT NULL,
  `f_object_id` varchar(40) DEFAULT NULL COMMENT '被点赞的id',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织id',
  `f_object_type` int(1) DEFAULT NULL COMMENT '点赞对象类型 1:讨论，2：回复，3：问题',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='点赞表';

-- ----------------------------
-- Records of t_praise
-- ----------------------------

-- ----------------------------
-- Table structure for `t_question`
-- ----------------------------
DROP TABLE IF EXISTS `t_question`;
CREATE TABLE `t_question` (
  `f_id` varchar(40) NOT NULL,
  `f_title` varchar(200) DEFAULT NULL COMMENT '标题',
  `f_type` int(1) DEFAULT NULL COMMENT '问题类型：1问题 2分享 3班级讨论',
  `f_flag` int(1) DEFAULT NULL COMMENT '状态：1活动2关闭',
  `f_topic` varchar(2000) DEFAULT NULL COMMENT '关联话题id，多个逗号隔开',
  `f_content` varchar(2000) DEFAULT NULL COMMENT '内容',
  `f_json_img` varchar(2000) DEFAULT NULL COMMENT '内容图片',
  `f_content_txt` varchar(6000) DEFAULT NULL,
  `f_json_attach` varchar(2000) DEFAULT NULL COMMENT '内容附件',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_last_modify_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
  `f_browse_num` int(11) DEFAULT '0' COMMENT '浏览量 默认0',
  `f_discuss_num` int(11) DEFAULT '0' COMMENT '讨论数 默认0',
  `f_forward_num` int(11) DEFAULT '0' COMMENT '转发数',
  `f_praise_num` int(11) DEFAULT '0' COMMENT '点赞数',
  `f_collect_num` int(11) DEFAULT '0' COMMENT '收藏数',
  `f_care_num` int(11) DEFAULT '0' COMMENT '关注数',
  `f_organization_id` varchar(40) NOT NULL COMMENT '组织id',
  `f_accuse_status` int(1) DEFAULT '0' COMMENT '举报状态：0未举报默认 1被举报',
  `f_share_has_cover` int(1) DEFAULT '0' COMMENT '分享是否有封面,0:无,1:有',
  `f_share_object_id` varchar(100) DEFAULT NULL COMMENT '分享对象的id',
  `f_share_type` varchar(2) DEFAULT '0' COMMENT '分享类型:1:课程分享2:学习路径3:知识分享4:班级分享5:调研分享6:微课大赛分享7:考试分享8:专题分享9:直播分享',
  `f_share_title` varchar(200) DEFAULT NULL COMMENT '分享标题',
  `f_essence_status` int(1) DEFAULT '0' COMMENT '是否加精   0非精品（默认） 1精品',
  `f_close_status` int(1) DEFAULT '0' COMMENT '关闭状态 0激活(默认)  1已关闭 ',
  `f_top_status` int(1) DEFAULT '0' COMMENT '顶置状态 0未项置(默认) 1已顶置',
  `f_top_time` bigint(20) DEFAULT NULL COMMENT '顶置时间',
  `f_class_id` varchar(40) DEFAULT NULL COMMENT '班级ID',
  `f_delete_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0未删除(默认)  1已删除',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问道问题表';

-- ----------------------------
-- Records of t_question
-- ----------------------------
INSERT INTO `t_question` VALUES ('30082db2-a2e0-4bca-a9b1-694bf0401136', '33333', '1', '1', null, '3333', null, null, null, '1', '1484281939609', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '1');
INSERT INTO `t_question` VALUES ('33d72c94-99b4-443e-b5a8-b41124145f1b', '1', '1', '1', null, '3333', null, null, null, '1', '1484281704805', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '1');
INSERT INTO `t_question` VALUES ('51a28d1f-95e3-4dfa-ad91-1bdc29e1742a', '1', '1', '1', null, '3333', null, null, null, '1', '1484282234031', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '0', null, null, null, '1');
INSERT INTO `t_question` VALUES ('977e5ded-a6f9-4dc7-a180-34e58a9b2abd', '你有问题', '1', '1', null, '大家讨论下问题的来源是什么', null, null, null, '1', '1484030043800', null, '10', '10', '10', '10', null, '10', '1', null, null, null, null, null, '1', '1', null, null, null, '0');
INSERT INTO `t_question` VALUES ('a3fcbd54-cfaa-472b-bb3e-53d6fa5d9f10', '33333', '1', '1', null, '3333', null, null, null, '1', '1484281849598', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '0', '1', null, null, null, '1');
INSERT INTO `t_question` VALUES ('aa3d6de5-2726-4670-8132-f11104c2f0e8', '33333', '1', '1', null, '3333', null, null, null, '1', '1484282024596', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '0');
INSERT INTO `t_question` VALUES ('b1f5c4eb-3464-4044-8743-437e0bbf8c08', '33333', '1', '1', null, '3333', null, null, null, '1', '1484282438559', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '0');
INSERT INTO `t_question` VALUES ('c54b132a-f747-4afd-bb5b-8472e54c7d8c', '33333', '1', '1', null, '3333', null, null, null, '1', '1484284413356', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '0');
INSERT INTO `t_question` VALUES ('db815545-aa5d-486b-8881-7e38910f9698', '33333', '1', '1', null, '3333', null, null, null, '1', '1484282268145', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '0');
INSERT INTO `t_question` VALUES ('e0545a7d-4639-48c6-991b-20618d422d93', '33333', '1', '1', null, '3333', null, null, null, '1', '1484281885802', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '0');
INSERT INTO `t_question` VALUES ('fa2023c0-8f3f-474a-8d2a-c533f8880944', '33333', '1', '1', null, '3333', null, null, null, '1', '1484284500327', null, '1', '1', '1', '1', null, '1', '1', null, null, null, null, null, '1', '1', null, null, null, '0');

-- ----------------------------
-- Table structure for `t_question_attach`
-- ----------------------------
DROP TABLE IF EXISTS `t_question_attach`;
CREATE TABLE `t_question_attach` (
  `f_id` varchar(40) NOT NULL,
  `f_rel_id` varchar(40) DEFAULT NULL COMMENT '关联的id，问题，评论，回复等的id',
  `f_rel_object_type` int(1) DEFAULT NULL COMMENT '关联对象类型 1问题 2问题补充 3讨论 4回复',
  `f_name` varchar(100) DEFAULT NULL COMMENT '文件名，上传时的名',
  `f_original_file` varchar(100) DEFAULT NULL COMMENT '上传到服务器地址转换前的名，用于下载',
  `f_view_name` varchar(100) DEFAULT NULL COMMENT '转换后的文件地址，用于预览',
  `f_suffix` varchar(100) DEFAULT NULL COMMENT '文档后缀类型',
  `f_suffix_img` varchar(100) DEFAULT NULL COMMENT '文档后缀类型表示图片的路径',
  `f_type` int(1) DEFAULT NULL COMMENT '文件类型： 1图片 2文档 3视频音频',
  `f_transfer_flag` int(1) DEFAULT NULL COMMENT 'mq转换状态： 0、转换中，1、转换成功，2、转换失败',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人',
  `f_download_num` int(11) DEFAULT '0' COMMENT '下载量',
  `f_view_num` int(11) DEFAULT '0' COMMENT '浏览量',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态 0未删除(默认)  1已删除',
  `f_organization_id` varchar(40) NOT NULL COMMENT '组织id',
  `f_thumbnail` varchar(100) DEFAULT NULL COMMENT '缩略图',
  `f_size` varchar(100) DEFAULT NULL COMMENT '文件大小',
  PRIMARY KEY (`f_id`),
  CONSTRAINT `FK_Reference_2` FOREIGN KEY (`f_id`) REFERENCES `t_question` (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问道问题附件表';

-- ----------------------------
-- Records of t_question_attach
-- ----------------------------

-- ----------------------------
-- Table structure for `t_question_discuss`
-- ----------------------------
DROP TABLE IF EXISTS `t_question_discuss`;
CREATE TABLE `t_question_discuss` (
  `f_id` varchar(40) NOT NULL,
  `f_question_id` varchar(40) NOT NULL COMMENT '问题id',
  `f_content` varchar(2000) DEFAULT NULL COMMENT '回复内容',
  `f_json_attach` varchar(2000) DEFAULT NULL COMMENT '附件冗余字段',
  `f_reply_num` int(11) DEFAULT '0' COMMENT '回复数 默认0',
  `f_praise_num` int(11) DEFAULT '0' COMMENT '点赞数 默认0',
  `f_audit_status` varchar(1) DEFAULT '0' COMMENT '审核状态 0未审核(默认)   1通过  2拒绝',
  `f_essence_status` varchar(1) DEFAULT '0' COMMENT '是否加精 0否(默认) 1是',
  `f_top_status` varchar(1) DEFAULT '0' COMMENT '顶置状态 0未项置(默认) 1已顶置',
  `f_top_time` bigint(20) DEFAULT NULL COMMENT '顶置时间',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态 0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) NOT NULL COMMENT '组织id',
  `f_content_txt` varchar(6000) DEFAULT NULL,
  `f_accuse_status` int(1) DEFAULT '0' COMMENT '举报状态：0未举报默认 1被举报',
  `f_read_status` int(1) DEFAULT '0' COMMENT '阅读状态：0未阅读(默认) 1已阅读',
  PRIMARY KEY (`f_id`,`f_question_id`),
  KEY `FK_Reference_1` (`f_question_id`),
  CONSTRAINT `FK_Reference_1` FOREIGN KEY (`f_question_id`) REFERENCES `t_question` (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问道问题讨论表';

-- ----------------------------
-- Records of t_question_discuss
-- ----------------------------
INSERT INTO `t_question_discuss` VALUES ('01098589-12e2-48e2-a0e9-e014a77a2b83', '33d72c94-99b4-443e-b5a8-b41124145f1b', '你好', null, null, '0', null, '0', '0', null, '1', '1484623630556', '0', '1', null, '0', '0');
INSERT INTO `t_question_discuss` VALUES ('2e0ba1c2-15ef-4a83-a44f-01fc3cbf1c59', '30082db2-a2e0-4bca-a9b1-694bf0401136', 'sdfdsfdsf', null, null, '0', null, '1', '1', '1484290227581', '1', '1484289538473', '1', '1', null, '0', '0');
INSERT INTO `t_question_discuss` VALUES ('3b4b8317-788e-4b31-bc6f-1cb3e93c6378', '30082db2-a2e0-4bca-a9b1-694bf0401136', 'sdfdsfds', null, null, '0', null, '0', '0', null, '1', '1484289544134', '0', '1', null, '0', '0');
INSERT INTO `t_question_discuss` VALUES ('4734c336-87e0-4e57-b736-df9557751f77', '30082db2-a2e0-4bca-a9b1-694bf0401136', 'sadsadsad', null, null, '0', null, '1', '1', '1484621054481', '1', '1484289509087', '0', '1', null, '0', '0');
INSERT INTO `t_question_discuss` VALUES ('536555bc-1053-4684-b461-acd6269e249b', '977e5ded-a6f9-4dc7-a180-34e58a9b2abd', '周末怎么过啊', null, null, '0', null, '0', '0', null, '1', '1484030118138', '0', '1', null, '0', '0');
INSERT INTO `t_question_discuss` VALUES ('878a5fd5-9f6d-44d6-aa2b-fdad32a054aa', '977e5ded-a6f9-4dc7-a180-34e58a9b2abd', '今天的计划是什么', null, null, '0', null, '0', '0', null, '1', '1484030101324', '0', '1', null, '0', '0');
INSERT INTO `t_question_discuss` VALUES ('9c8a3ab0-7ab1-4ec4-9262-0b6a83adbf5b', '30082db2-a2e0-4bca-a9b1-694bf0401136', '213', null, null, '0', null, '0', '0', null, '1', '1484290197054', '0', '1', null, '0', '0');

-- ----------------------------
-- Table structure for `t_question_reply`
-- ----------------------------
DROP TABLE IF EXISTS `t_question_reply`;
CREATE TABLE `t_question_reply` (
  `f_id` varchar(40) NOT NULL,
  `f_question_id` varchar(40) NOT NULL COMMENT '问题id',
  `f_content` varchar(2000) DEFAULT NULL COMMENT '回复内容',
  `f_discuss_id` varchar(40) DEFAULT NULL COMMENT '讨论id',
  `f_to_user_id` varchar(40) DEFAULT NULL COMMENT '被回复的人id',
  `f_praise_num` int(11) DEFAULT '0' COMMENT '点赞数 默认0',
  `f_audit_status` int(1) DEFAULT '0' COMMENT '审核状态 0未审核(默认)   1通过  2拒绝',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态 0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) NOT NULL COMMENT '组织id',
  `f_accuse_status` int(1) DEFAULT '0' COMMENT '举报状态：0未举报默认 1被举报',
  `f_read_status` int(1) DEFAULT '0' COMMENT '阅读状态：0未阅读(默认) 1已阅读',
  PRIMARY KEY (`f_id`,`f_question_id`),
  KEY `FK_Reference_10` (`f_discuss_id`,`f_question_id`),
  CONSTRAINT `FK_Reference_10` FOREIGN KEY (`f_discuss_id`, `f_question_id`) REFERENCES `t_question_discuss` (`f_id`, `f_question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问道问题回复表';

-- ----------------------------
-- Records of t_question_reply
-- ----------------------------
INSERT INTO `t_question_reply` VALUES ('042eab6c-0e1f-46f5-b5a1-5cb1a179b734', '30082db2-a2e0-4bca-a9b1-694bf0401136', 'wqe', '4734c336-87e0-4e57-b736-df9557751f77', '1', '0', null, '1', '1484290426166', '0', '1', '0', '0');
INSERT INTO `t_question_reply` VALUES ('6a399843-6248-471c-8876-0e12045523a0', '30082db2-a2e0-4bca-a9b1-694bf0401136', 'sdfdsfdsf', '4734c336-87e0-4e57-b736-df9557751f77', '1', '0', null, '1', '1484289531610', '1', '1', '0', '0');
INSERT INTO `t_question_reply` VALUES ('7bd98121-a979-4cb8-9e33-72c3ec3ce792', '30082db2-a2e0-4bca-a9b1-694bf0401136', 'zvddsdsgggsg', '4734c336-87e0-4e57-b736-df9557751f77', '1', '0', null, '1', '1484292300764', '0', '1', '0', '0');
INSERT INTO `t_question_reply` VALUES ('8dbafa00-d387-4584-8951-e86951b0839f', '977e5ded-a6f9-4dc7-a180-34e58a9b2abd', '朋友一起喝酒', '536555bc-1053-4684-b461-acd6269e249b', '1', '0', null, '1', '1484030169703', '0', '1', '0', '0');
INSERT INTO `t_question_reply` VALUES ('b444f010-6417-4bc0-8f54-69a958a90d5c', '30082db2-a2e0-4bca-a9b1-694bf0401136', '123', '4734c336-87e0-4e57-b736-df9557751f77', '1', '0', null, '1', '1484290350307', '0', '1', '0', '0');
INSERT INTO `t_question_reply` VALUES ('ba0f04d7-9f09-4c67-b7f2-cc6fa2576481', '977e5ded-a6f9-4dc7-a180-34e58a9b2abd', '睡觉', '536555bc-1053-4684-b461-acd6269e249b', '1', '0', null, '1', '1484030132483', '0', '1', '0', '0');
INSERT INTO `t_question_reply` VALUES ('c5ceffcc-bf07-4d3a-8bb8-e2fce24035e4', '977e5ded-a6f9-4dc7-a180-34e58a9b2abd', '我也是啊', '536555bc-1053-4684-b461-acd6269e249b', '1', '0', null, '1', '1484030141640', '0', '1', '0', '0');
INSERT INTO `t_question_reply` VALUES ('e9944567-88d1-453e-9b66-b3d4d92c1dd9', '30082db2-a2e0-4bca-a9b1-694bf0401136', 'sdfsdfsdf', '4734c336-87e0-4e57-b736-df9557751f77', '1', '0', null, '1', '1484289524103', '1', '1', '0', '0');

-- ----------------------------
-- Table structure for `t_question_reviewed`
-- ----------------------------
DROP TABLE IF EXISTS `t_audit_record`;
CREATE TABLE `t_audit_record` (
  `f_id` varchar(40) NOT NULL,
  `f_question_id` varchar(40) NOT NULL COMMENT '问题id',
  `f_audit_type` varchar(2) DEFAULT '0' COMMENT '审核类型：1提问审核 2讨论审核 3回复审核 4提问举报审核 5讨论举报审核 6回复举报审核7补充内容审核8讨论区评论审核9讨论区回复审核10讨论区评论举报审核11讨论区回复举报审核12分享审核',
  `f_object_id` varchar(40) DEFAULT NULL COMMENT '审核对象id',
  `f_content` varchar(6000) DEFAULT NULL COMMENT '审核内容',
  `f_create_user_id` varchar(40) DEFAULT NULL COMMENT '提交人',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '审核时间',
  `f_audit_user_id` varchar(40) DEFAULT NULL COMMENT '审核人',
  `f_audit_time` bigint(20) DEFAULT NULL COMMENT '审核时间',
  `f_audit_status` int(1) DEFAULT '0' COMMENT '审核状态：0待审核(默认)  1通过 2拒绝',
  `f_audit_note` varchar(1000) DEFAULT NULL COMMENT '审核说明',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) NOT NULL COMMENT '组织id',
  `f_accuse_num` int(11) DEFAULT '0' COMMENT '举报次数',
  `f_be_user_id` varchar(40) DEFAULT NULL COMMENT '被举报人id',
  `f_source_type` varchar(40) DEFAULT NULL COMMENT '来源：1问吧 2知识 3课程 4学习',
  `f_need_audit` int(1) DEFAULT '0' COMMENT '是否需要审核 0不需要（默认） 1需要',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问道审核记录表';

-- ----------------------------
-- Records of t_audit_record
-- ----------------------------
INSERT INTO `t_audit_record` VALUES ('1', '977e5ded-a6f9-4dc7-a180-34e58a9b2abd', '0', null, null, null, null, '1', '1484122201441', '0', 'sdafsdhfg', '0', '1', '0', null, null, '0');

DROP TABLE IF EXISTS `t_pending_audit`;
CREATE TABLE `t_pending_audit` (
  `f_id` varchar(40) NOT NULL,
  `f_question_id` varchar(40) NOT NULL COMMENT '问题id',
  `f_audit_type` varchar(2) DEFAULT '0' COMMENT '审核类型：1提问审核 2讨论审核 3回复审核 4提问举报审核 5讨论举报审核 6回复举报审核7补充内容审核8讨论区评论审核9讨论区回复审核10讨论区评论举报审核11讨论区回复举报审核12分享审核',
  `f_object_id` varchar(40) DEFAULT NULL COMMENT '审核对象id',
  `f_content` varchar(6000) DEFAULT NULL COMMENT '审核内容',
  `f_create_user_id` varchar(40) DEFAULT NULL COMMENT '提交人',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '审核时间',
  `f_audit_user_id` varchar(40) DEFAULT NULL COMMENT '审核人',
  `f_audit_time` bigint(20) DEFAULT NULL COMMENT '审核时间',
  `f_audit_status` int(1) DEFAULT '0' COMMENT '审核状态：0待审核(默认)  1通过 2拒绝',
  `f_audit_note` varchar(1000) DEFAULT NULL COMMENT '审核说明',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) NOT NULL COMMENT '组织id',
  `f_accuse_num` int(11) DEFAULT '0' COMMENT '举报次数',
  `f_be_user_id` varchar(40) DEFAULT NULL COMMENT '被举报人id',
  `f_source_type` varchar(40) DEFAULT NULL COMMENT '来源：1问吧 2知识 3课程 4学习',
  `f_need_audit` int(1) DEFAULT '0' COMMENT '是否需要审核 0不需要（默认） 1需要',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='问道待审核表';

-- ----------------------------
-- Table structure for `t_topic`
-- ----------------------------
DROP TABLE IF EXISTS `t_topic`;
CREATE TABLE `t_topic` (
  `f_id` varchar(40) NOT NULL COMMENT '主键',
  `f_name` varchar(20) DEFAULT NULL COMMENT '话题名称',
  `f_cover_url` varchar(200) DEFAULT NULL COMMENT '封面图片路径',
  `f_type_id` varchar(40) DEFAULT NULL COMMENT '话题类别ID',
  `f_state` int(1) DEFAULT NULL COMMENT '话题状态（启用1，禁用0）',
  `f_description` varchar(3000) DEFAULT NULL COMMENT '话题描述',
  `f_source` int(2) DEFAULT NULL COMMENT '来源（1管理员 2用户）',
  `f_publish_member_id` varchar(40) DEFAULT NULL COMMENT '发布人',
  `f_publish_time` bigint(20) DEFAULT NULL COMMENT '发布时间',
  `f_recommend` int(1) DEFAULT NULL COMMENT '初次登录推荐（推荐1，不推荐0）',
  `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT NULL COMMENT '删除标记（0未删除，1已删除）',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Records of t_topic
-- ----------------------------
INSERT INTO `t_topic` VALUES ('1', '时尚', null, null, null, null, null, null, null, null, null, null, null);
INSERT INTO `t_topic` VALUES ('2', '男人装', null, null, null, null, null, null, null, null, null, null, null);
INSERT INTO `t_topic` VALUES ('3', '历史', null, null, null, null, null, null, null, null, null, null, null);
INSERT INTO `t_topic` VALUES ('4', '宫崎骏', null, null, null, null, null, null, null, null, null, null, null);
INSERT INTO `t_topic` VALUES ('d48872d1-dcf7-4b9c-990a-1165addc2468', '呵呵', null, null, null, null, null, null, null, null, null, null, null);

-- ----------------------------
-- Table structure for `t_topic_manager`
-- ----------------------------
DROP TABLE IF EXISTS `t_topic_manager`;
CREATE TABLE `t_topic_manager` (
  `f_id` varchar(40) NOT NULL,
  `f_topic_id` varchar(40) DEFAULT NULL COMMENT '话题ID',
  `f_member_id` varchar(40) DEFAULT NULL COMMENT '用户ID',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_delete_flag` int(1) DEFAULT NULL COMMENT '删除标记（0未删除，1已删除）',
  PRIMARY KEY (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='话题管理员表';

-- ----------------------------
-- Records of t_topic_manager
-- ----------------------------

-- ----------------------------
-- Table structure for `t_trends`
-- ----------------------------
DROP TABLE IF EXISTS `t_trends`;
CREATE TABLE `t_trends` (
  `f_id` varchar(40) NOT NULL,
  `f_trends_type` varchar(2) DEFAULT NULL COMMENT '动态类型：1提问 2分享 3讨论 4加精 5置顶',
  `f_question_id` varchar(40) NOT NULL COMMENT '问题id',
  `f_discuss_id` varchar(40) DEFAULT NULL COMMENT '讨论id，如果是参与讨论的动态才有',
  `f_create_user_id` varchar(40) DEFAULT NULL COMMENT '创建人id',
  `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `f_title` varchar(200) DEFAULT NULL COMMENT '标题',
  `f_content` varchar(900) DEFAULT NULL COMMENT '内容，只截取部分内容用于动态列表显示',
  `f_delete_flag` int(1) DEFAULT '0' COMMENT '删除状态：0未删除(默认) 1已删除',
  `f_organization_id` varchar(40) DEFAULT NULL COMMENT '组织id',
  `f_rel_topic` varchar(200) DEFAULT NULL COMMENT '问题关联话题',
  `f_thumbnail` varchar(100) DEFAULT NULL COMMENT '缩略图',
  `f_original` varchar(100) DEFAULT NULL COMMENT '原图',
  `f_share_type` varchar(2) DEFAULT '0' COMMENT '冗余字段，与问题表值同步:如果是其它地方分享到问题,则有分享类型,否则类型为0,1:课程分享2:学习路径3:知识分享4:班级分享5:调研分享6:微课大赛分享7:考试分享8:专题分享9:直播分享',
  `f_share_object_id` varchar(100) DEFAULT NULL COMMENT '分享对象的id,没有就不填',
  `f_share_title` varchar(200) DEFAULT NULL COMMENT '分享标题',
  PRIMARY KEY (`f_id`,`f_question_id`),
  KEY `FK_Reference_7` (`f_question_id`),
  CONSTRAINT `FK_Reference_7` FOREIGN KEY (`f_question_id`) REFERENCES `t_question` (`f_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='动态表';

-- ----------------------------
-- Records of t_trends
-- ----------------------------
