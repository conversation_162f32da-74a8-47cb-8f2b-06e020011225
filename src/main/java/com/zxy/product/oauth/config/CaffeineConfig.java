package com.zxy.product.oauth.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description: 本地缓存Caffeine配置
 */
@Configuration
public class CaffeineConfig {

    @Bean
    public Cache<String, Object> caffeine_10M(){
        return Caffeine.newBuilder()
                .maximumSize(1000) // 设置最大容量
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .build();
    }

    @Bean
    public Cache<String, Object> caffeine_16H(){
        return Caffeine.newBuilder()
                .maximumSize(100) // 设置最大容量
                .expireAfterWrite(16, TimeUnit.HOURS)
                .build();
    }
}
