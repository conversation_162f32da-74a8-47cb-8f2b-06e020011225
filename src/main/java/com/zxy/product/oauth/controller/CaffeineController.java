package com.zxy.product.oauth.controller;

import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description: 本地缓存Caffeine人工管理
 */
@Controller
@RequestMapping("/caffeine")
public class CaffeineController {

    private Cache<String, Object> caffeine_10M;
    private Cache<String, Object> caffeine_16H;

    @Autowired
    public void setCaffeine_10M(Cache<String, Object> caffeine_10M) {
        this.caffeine_10M = caffeine_10M;
    }

    @Autowired
    public void setCaffeine_16H(Cache<String, Object> caffeine_16H) {
        this.caffeine_16H = caffeine_16H;
    }

    @RequestMapping(value = "/invalidate", method = RequestMethod.GET)
    @Param(name = "key", required = true)
    //@Permitted TODO 上线时需要放开，暂时便于开发调测
    @JSON("*.*")
    public Map<String,Object> invalidate(RequestContext context){
        String key = context.getString("key");
        caffeine_10M.invalidate(key);
        caffeine_16H.invalidate(key);
        return ImmutableMap.of("result", "true");
    }

    @RequestMapping(value = "/invalidate-all", method = RequestMethod.GET)
    //@Permitted TODO 上线时需要放开，暂时便于开发调测
    @JSON("*.*")
    public Map<String,Object> invalidateAll(){
        caffeine_10M.invalidateAll();
        caffeine_16H.invalidateAll();
        return ImmutableMap.of("result", "true");
    }

    @RequestMapping(value = "/estimated-size", method = RequestMethod.GET)
    //@Permitted TODO 上线时需要放开，暂时便于开发调测
    @JSON("*.*")
    public Map<String,Long> estimatedSize(){
        Map<String, Long> map = new HashMap<>();
        map.put("caffeine_10M", caffeine_10M.estimatedSize());
        map.put("caffeine_16H", caffeine_16H.estimatedSize());
        return map;
    }

    @RequestMapping(value = "/get-by-key", method = RequestMethod.GET)
    @Param(name = "key", required = true)
    //@Permitted TODO 上线时需要放开，暂时便于开发调测
    @JSON("*.*")
    public Map<String, Object> getByKey(RequestContext context){
        String key = context.getString("key");

        Map<String, Object> map = new HashMap<>();
        map.put("caffeine_10M", caffeine_10M.getIfPresent(key));
        map.put("caffeine_16H", caffeine_16H.getIfPresent(key));
        return map;
    }

    @RequestMapping(value = "/get-keys", method = RequestMethod.GET)
    //@Permitted TODO 上线时需要放开，暂时便于开发调测
    @JSON("*.*")
    public Map<String,Set<String>> getKeys(){
        Map<String, Set<String>> map = new HashMap<>();
        map.put("caffeine_10M", caffeine_10M.asMap().keySet());
        map.put("caffeine_16H", caffeine_16H.asMap().keySet());
        return map;
    }
}
