package com.zxy.product.log.listener;

import com.google.common.collect.Maps;
import com.zxy.common.base.message.Message;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.log.content.MessageTypeContent;
import com.zxy.product.log.entity.ResourceVisit;
import com.zxy.product.log.entity.UserBehavior;
import com.zxy.product.log.util.DateUtil;
import com.zxy.product.system.content.MessageHeaderContent;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Record3;
import org.jooq.Table;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zxy.product.log.jooq.Tables.RESOURCE_VISIT;
import static com.zxy.product.log.jooq.Tables.USER_BEHAVIOR;

/**
 * @Classname GenerateResourceVisitListener
 * @Description 统计资源浏览量
 * @Date 2021/11/23 1:49 下午
 * @Created by ykn
 */
@Component
public class GenerateResourceVisitListener extends AbstractMessageListener {

    public static final int SUBLIST_SIZE = 50000;
    public static final String SUBLIST_TABLE_NAME = "UserBehaviors";
    private final Logger log = LoggerFactory.getLogger(GenerateResourceVisitListener.class);

    public static final String SUBLIST_CONTENT_ID = "contentId";
    public static final String SUBLIST_CONTENT_NAME = "contentName";
    public static final String SUBLIST_CONTENT_TYPE = "contentType";
    public static final String SUBLIST_COUNT = "count";

    private Integer day;

    private Long lastTime;

    private Long firstTimestampOfPreviousDay;

    private Long lastTimestampOfPreviousDay;

    private Map<String, ResourceVisit> insertMaps;

    private CommonDao<ResourceVisit> resourceVisitDao;

    private CommonDao<UserBehavior> userBehaviorDao;

    @Autowired
    public void setResourceVisitDao(CommonDao<ResourceVisit> resourceVisitDao) {
        this.resourceVisitDao = resourceVisitDao;
    }

    @Autowired
    public void setUserBehaviorDao(CommonDao<UserBehavior> userBehaviorDao) {
        this.userBehaviorDao = userBehaviorDao;
    }

    @Override
    protected void onMessage(Message message) {
        String systemTime = message.getHeader(MessageHeaderContent.SYSTEM_TIME);
        long executeTime = Long.parseLong(systemTime);
        log.info("定时任务统计资源浏览量 时间参数:{},开始时间:{}",executeTime,DateUtil.format(Date.from(Instant.now()),DateUtil.DATE_TIME_SEPARATOR_PATTERN));
        initialise(executeTime);
        statisticUserBehavior();
        batchInsertResourceVisits();
        log.info("定时任务统计资源浏览量 结束时间:{}", DateUtil.format(Date.from(Instant.now()), DateUtil.DATE_TIME_SEPARATOR_PATTERN));
        deleteExpiredResourceVisits(executeTime);
        log.info("定时任务统计资源浏览量 删除过期数据完成");
    }

    private void initialise(long executeTime) {
        Instant instant = Instant.ofEpochMilli(executeTime).atZone(ZoneOffset.systemDefault()).minusDays(1).toInstant();
        this.day = Integer.valueOf(DateUtil.format(Date.from(instant), DateUtil.DATE_PATTERN_NEW));
        this.firstTimestampOfPreviousDay = DateUtil.firstTimestampPreviousDay(executeTime);
        this.lastTimestampOfPreviousDay = DateUtil.lastTimestampPreviousDay(executeTime);
        this.lastTime = this.firstTimestampOfPreviousDay;
        this.insertMaps = Maps.newHashMap();
    }

    private void statisticUserBehavior() {
        int recordsSize = getRecordsSize();
        for (int i = 0; i < recordsSize; ) {
            List<ResourceVisit> rvs = generateResourceVisits();
            calculateResourceVisits(rvs);
            i += SUBLIST_SIZE;
            updateLastTime(i);
        }
    }


    private void calculateResourceVisits(List<ResourceVisit> rvs) {
        rvs.forEach(r -> {
            String contentId = r.getContentId();
            ResourceVisit resourceVisit = this.insertMaps.putIfAbsent(contentId, r);
            if (Objects.nonNull(resourceVisit)) {
                resourceVisit.setVisit(resourceVisit.getVisit() + r.getVisit());
            }
        });
    }


    private List<ResourceVisit> generateResourceVisits() {
        return resourceVisitDao.execute(e -> {

            Table<Record3<String, String, String>> sublist = getSublist(e);

            return e.select(Fields.start()
                            .add(sublist.field(SUBLIST_CONTENT_ID))
                            .add(sublist.field(SUBLIST_CONTENT_NAME))
                            .add(sublist.field(SUBLIST_CONTENT_TYPE))
                            .add(DSL.count().as(SUBLIST_COUNT))
                            .end())
                    .from(sublist)
                    .groupBy(DSL.field(SUBLIST_CONTENT_ID))
                    .fetch(this::generateResourceVisit);
        });
    }

    private ResourceVisit generateResourceVisit(Record r) {
        ResourceVisit rv = new ResourceVisit();
        rv.forInsert();
        rv.setContentId(r.get(SUBLIST_CONTENT_ID, String.class));
        rv.setContentName(r.get(SUBLIST_CONTENT_NAME, String.class));
        rv.setContentType(r.get(SUBLIST_CONTENT_TYPE, Integer.class));
        rv.setVisit(r.get(SUBLIST_COUNT, Integer.class));
        rv.setDay(this.day);
        return rv;
    }

    private Table<Record3<String, String, String>> getSublist(DSLContext e) {
        return e.select(USER_BEHAVIOR.CONTENT_ID.as(SUBLIST_CONTENT_ID),
                        USER_BEHAVIOR.CONTENT_NAME.as(SUBLIST_CONTENT_NAME),
                        USER_BEHAVIOR.CONTENT_TYPE.as(SUBLIST_CONTENT_TYPE))
                .from(USER_BEHAVIOR)
                .where(USER_BEHAVIOR.CREATE_TIME.between(lastTime, this.lastTimestampOfPreviousDay))
                .and(USER_BEHAVIOR.CONTENT_TYPE.in(UserBehavior.SUBJECT, UserBehavior.COURSE))
                .orderBy(USER_BEHAVIOR.CREATE_TIME.asc())
                .limit(SUBLIST_SIZE)
                .asTable(SUBLIST_TABLE_NAME);
    }

    private void updateLastTime(Integer offset) {
        lastTime = userBehaviorDao.execute(e ->
                e.select(USER_BEHAVIOR.CREATE_TIME)
                        .from(USER_BEHAVIOR)
                        .where(USER_BEHAVIOR.CREATE_TIME.between(firstTimestampOfPreviousDay, lastTimestampOfPreviousDay))
                        .and(USER_BEHAVIOR.CONTENT_TYPE.in(UserBehavior.SUBJECT, UserBehavior.COURSE))
                        .orderBy(USER_BEHAVIOR.CREATE_TIME.asc())
                        .limit(offset, 1)
                        .fetchOne(USER_BEHAVIOR.CREATE_TIME)
        );
    }

    private Integer getRecordsSize() {
        return userBehaviorDao.count(
                USER_BEHAVIOR.CREATE_TIME.between(firstTimestampOfPreviousDay, lastTimestampOfPreviousDay)
                , USER_BEHAVIOR.CONTENT_TYPE.in(UserBehavior.SUBJECT, UserBehavior.COURSE));
    }


    private void batchInsertResourceVisits() {
        resourceVisitDao.insert(insertMaps.values());
    }

    private void deleteExpiredResourceVisits(long executeTime) {
        Instant instant = Instant.ofEpochMilli(executeTime).atZone(ZoneOffset.systemDefault()).minusDays(30).toInstant();
        Integer timeNode = Integer.valueOf(DateUtil.format(Date.from(instant), DateUtil.DATE_PATTERN_NEW));
        resourceVisitDao.delete(RESOURCE_VISIT.DAY.lt(timeNode));
    }


    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.GENERATE_RV_TASK_MESSAGE
        };
    }
}
