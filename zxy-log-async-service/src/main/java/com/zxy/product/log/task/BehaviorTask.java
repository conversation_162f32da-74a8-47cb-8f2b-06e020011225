package com.zxy.product.log.task;

import com.zxy.common.cache.redis.Redis;
import com.zxy.product.log.api.OnlineInfoService;
import com.zxy.product.log.api.UserBehaviorClickService;
import com.zxy.product.log.util.DateUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.StringJoiner;
import java.util.UUID;

@Component
@EnableAsync
public class BehaviorTask {
    @Resource
    private UserBehaviorClickService userBehaviorClickService;

    @Resource
    private OnlineInfoService onlineInfoService;
    @Resource
    private Redis redis;
    /**
     * 计算 智能推荐的一些指标
     */
    @Async
    @Scheduled(cron = "0 30 0 * * ?")
    public void intelligentIndex() {
        if (!lock("intelligentIndex","intelligentIndex")){
            return;
        }
        long beginTimestampYesterday = DateUtil.firstTimestampYesterday();
        long endTimestampYesterday = DateUtil.lastTimestampPreviousDay(System.currentTimeMillis());
        StringJoiner insertSql = new StringJoiner(",", "INSERT INTO `t_intelligent_index` (`f_id`, `f_key`, `f_value`, `f_create_time`, `f_statistics_time`) VALUES ", " ;");

        // 1.PC首页推荐昨天的点击量
        String sql = String.format("SELECT COUNT(*) AS value FROM  t_user_behavior WHERE client_type = 0 and f_type=11 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        String value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.One.getKeyName(), beginTimestampYesterday));

        // 2.PC发现页推荐昨天的点击量
        sql = String.format("SELECT COUNT(*) AS value FROM  t_user_behavior WHERE client_type = 0 and f_type=10 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Two.getKeyName(), beginTimestampYesterday));

        // 3.APP首页推荐昨天的点击量
        sql = String.format("SELECT COUNT(*) AS value FROM  t_user_behavior WHERE client_type = 1 and f_type=11 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Three.getKeyName(), beginTimestampYesterday));


        // 4. APP发现页推荐昨天的点击量
        sql = String.format("SELECT COUNT(*) AS value FROM  t_user_behavior WHERE client_type = 1 and f_type=10 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Four.getKeyName(), beginTimestampYesterday));

        // 7. PC首页推荐昨天的进入量
        sql = String.format("SELECT COUNT(*) AS value FROM  t_user_behavior WHERE client_type = 0 and f_type=13 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Seven.getKeyName(), beginTimestampYesterday));


        // 8. APP发现页推荐昨天的进入量
        sql = String.format("SELECT COUNT(DISTINCT f_user_id) AS value FROM  t_user_behavior WHERE client_type = 1 and f_type=13 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Eight.getKeyName(), beginTimestampYesterday));


        // 9. PC首页昨天的点击人数
        sql = String.format("SELECT COUNT(DISTINCT f_user_id) AS value FROM  t_user_behavior WHERE client_type = 0 and f_type=14 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Nine.getKeyName(), beginTimestampYesterday));


        // 10. APP首页昨天的点击人数
        sql = String.format("SELECT COUNT(DISTINCT f_user_id) AS value FROM  t_user_behavior WHERE client_type = 1 and f_type=14 AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Ten.getKeyName(), beginTimestampYesterday));


        // 11. 每天参与推荐的人数
        sql = String.format("SELECT COUNT(DISTINCT f_user_id) AS value FROM  t_user_behavior WHERE f_type IN(13,14) AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Eleven.getKeyName(), beginTimestampYesterday));


        // 12. PC每天参与推荐的人数
        sql = String.format("SELECT COUNT(DISTINCT f_user_id) AS value FROM  t_user_behavior WHERE client_type = 0 and f_type IN(13,14) AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Twelve.getKeyName(), beginTimestampYesterday));


        // 13. APP每天参与推荐的人数
        sql = String.format("SELECT COUNT(DISTINCT f_user_id) AS value FROM  t_user_behavior WHERE client_type = 1 and f_type IN(13,14) AND f_status = 0  AND f_create_time > %s AND f_create_time < %s; ", beginTimestampYesterday, endTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        insertSql.add(initInsertSQL(value, Key.Thirteen.getKeyName(), beginTimestampYesterday));

        userBehaviorClickService.insert(insertSql.toString());
    }

    public String initInsertSQL(String value, String key, long beginTimestampYesterday) {
        return String.format("('%s', '%s', '%s', %s, %s)", UUID.randomUUID(), key, value, System.currentTimeMillis(),beginTimestampYesterday);
    }

    @Async
    @Scheduled(cron = "0 30 1 * * ?")
    public void recommendIndex() {
        if (!lock("recommendIndex","recommendIndex")){
            return;
        }
        // 2.点击情况 IPC&APP首页总点击量/(APP首页推荐曝光量+PC首页推荐曝光量）
        // 指标 9,10/5,6
        long beginTimestampYesterday = DateUtil.firstTimestampYesterday();
        // 3.点击情况 【PC进入发现页人数（即点击“查看更多”的人数）+PC和APP首页点击人数+APP发现页人数（进入页面就行）】【去重后】/PC和APP的总登承用户数【去重后】
        // 指标 11/去重登录人数
        String sql = String.format("SELECT SUM(f_value) AS value FROM  t_intelligent_index WHERE f_key in('%s') and f_statistics_time = %s;", Key.Eleven.getKeyName(), beginTimestampYesterday);
        String value = userBehaviorClickService.get(sql);
        BigDecimal divide = new BigDecimal(value).divide(new BigDecimal(onlineInfoService.getTodayLoginMemberCount()), 2, RoundingMode.HALF_UP);
        StringJoiner insertSql = new StringJoiner(",", "INSERT INTO `t_intelligent_index` (`f_id`, `f_key`, `f_value`, `f_create_time`, `f_statistics_time`) VALUES ", " ;").add(initInsertSQL(divide.toString(), Key.Sixteen.getKeyName(), beginTimestampYesterday));
        userBehaviorClickService.insert(insertSql.toString());

        // 4.点击情况 |【pC进入发现页人次+PC 和 APP首页点击人次＋APP发現页人次（进入页面就行）】/PC&APP的总登录人次
        // 指标 7-10/登录人数
        sql = String.format("SELECT SUM(f_value) AS value FROM  t_intelligent_index WHERE f_key in('%s','%s','%s','%s') and f_statistics_time = %s;", Key.Seven.getKeyName(), Key.Eight.getKeyName(), Key.Nine.getKeyName(), Key.Ten.getKeyName(), beginTimestampYesterday);
        value = userBehaviorClickService.get(sql);
        divide = new BigDecimal(value).divide(new BigDecimal(onlineInfoService.getTodayLoginCount()), 2, RoundingMode.HALF_UP);
        insertSql = new StringJoiner(",", "INSERT INTO `t_intelligent_index` (`f_id`, `f_key`, `f_value`, `f_create_time`, `f_statistics_time`) VALUES ", " ;").add(initInsertSQL(divide.toString(), Key.Seventeen.getKeyName(), beginTimestampYesterday));
        userBehaviorClickService.insert(insertSql.toString());

    }

    @Async
    @Scheduled(cron = "30 1 1 * * ?")
    public void recommendIndexMonth() {
        if (!lock("recommendIndexMonth","recommendIndexMonth")){
            return;
        }
        // 1.点击量
        // 某个月PC和APP首页和发现页的总点击量/（该月PC&APP首页和发現页总点击量一上个月PC&APP首页和发現页的总点击量）
        long begin = getsTheStartTimestampOfTheFirstDayOfThePreviousMonth(1);
        long endTime = getTheStartTimestampForTheLastDayOfThePreviousMonth(1);
        String sql = String.format("SELECT IFNULL(SUM(f_value),0) AS value FROM  t_intelligent_index WHERE f_key in('%s','%s','%s','%s') and f_statistics_time >= %s and f_statistics_time<= %s ;", Key.One.getKeyName(), Key.Two.getKeyName(), Key.Three.getKeyName(), Key.Four.getKeyName(), begin, endTime);
        String value = userBehaviorClickService.get(sql);

        begin = getsTheStartTimestampOfTheFirstDayOfThePreviousMonth(2);
        endTime = getTheStartTimestampForTheLastDayOfThePreviousMonth(2);
        sql = String.format("SELECT IFNULL(SUM(f_value),0) AS value FROM  t_intelligent_index WHERE f_key in('%s','%s','%s','%s') and f_statistics_time >= %s and f_statistics_time<= %s ;", Key.One.getKeyName(), Key.Two.getKeyName(), Key.Three.getKeyName(), Key.Four.getKeyName(), begin, endTime);
        String value2 = userBehaviorClickService.get(sql);
        BigDecimal divide = (int) Double.parseDouble(value2) == 0 ? new BigDecimal("0") : new BigDecimal(value).divide(new BigDecimal(value2), 2, RoundingMode.HALF_UP);
        StringJoiner insertSql = new StringJoiner(",", "INSERT INTO `t_intelligent_index` (`f_id`, `f_key`, `f_value`, `f_create_time`, `f_statistics_time`) VALUES ", " ;");
        insertSql.add(initInsertSQL(divide.toString(), Key.Fourteen.getKeyName(), begin));
        userBehaviorClickService.insert(insertSql.toString());
    }


    public boolean lock(String lockKey,String lockValue) {
        return redis.process(jedis -> {
            Long result = jedis.setnx(lockKey, lockValue);
            if (result == 1) {
                jedis.expire(lockKey, 60 * 60 * 2);
                return true;
            }
            return false;
        });
    }

    /**
     * 获取上某个月第一天的开始时间戳
     */
    private static long getsTheStartTimestampOfTheFirstDayOfThePreviousMonth(int before) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -Math.abs(before));
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取上个月最后一天的开始时间戳
     */
    private static long getTheStartTimestampForTheLastDayOfThePreviousMonth(int before) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -Math.abs(before) + 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }
}

enum Key {
    One("1", "PC首页推荐昨天的点击量"),
    Two("2", "PC发现页推荐昨天的点击量"),
    Three("3", "APP首页推荐昨天的点击量"),
    Four("4", "APP发现页推荐昨天的点击量"),
    Seven("7", "PC首页推荐昨天的进入量"),
    Eight("8", "APP发现页推荐昨天的进入量"),
    Nine("9", "PC首页昨天的点击量"),
    Ten("10", "APP首页昨天的点击量"),
    Eleven("11", "每天参与推荐的人数"),
    Twelve("12", "PC每天参与推荐的人数"),
    Thirteen("13", "APP每天参与推荐的人数"),
    Fourteen("14", "推荐点击增长率"),
    Fifteen("15", "首页的点击量/首页的曝光量"),
    Sixteen("16", "参与推荐的人数/总登录用户數"),
    Seventeen("17", "参与推荐的人次/总登录量");

    private final String keyName;

    Key(String keyName, String keyDesc) {
        this.keyName = keyName;
    }

    public String getKeyName() {
        return keyName;
    }

}
