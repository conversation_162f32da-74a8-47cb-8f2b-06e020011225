package com.zxy.product.log.listener;

import java.util.*;

import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.product.log.api.OnlineStatisticsService;
import com.zxy.product.log.content.GlobalConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.mongodb.WriteResult;
import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.human.content.MessageHeaderContent;
import com.zxy.product.human.content.MessageTypeContent;
import eu.bitwalker.useragentutils.UserAgent;

@Component
public class UserOnlineInfoListener extends AbstractMessageListener {
	
    private static final Logger LOGGER = LoggerFactory.getLogger(UserOnlineInfoListener.class);
	private MongoTemplate mongoTemplate;
    private static Map<String, String> terminalMap = new HashMap<>(6);
    private OnlineStatisticsService onlineStatisticsService;
	// private Cache cache;

	/**
     * Mongodb中存储用户在线状态信息的集合名
     */
    private static final String USER_ONLINE_INFO_COLL = "user_online_info";

    /**
     * 缺省过期时间的时长，单位：秒
     */
    private static final int DEF_EXPIRED_INTERVAL_TIME = 3600;
    
    static {
        terminalMap.put("Chrome", "chrome");
        terminalMap.put("Firefox", "火狐");
        terminalMap.put("Internet Explorer 11", "IE 11");
        terminalMap.put("Internet Explorer 10", "IE 10");
        terminalMap.put("Internet Explorer 9", "IE 9");
        terminalMap.put("Internet Explorer 8", "IE 8");
    }

	@Autowired
	public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

	@Autowired
	public void setOnlineStatisticsService(OnlineStatisticsService onlineStatisticsService) {
		this.onlineStatisticsService = onlineStatisticsService;
	}

//	@Autowired
//	public void setCacheService(CacheService cacheService) {
//		this.cache = cacheService.create("online-info-task");
//	}

	@Override
	protected void onMessage(Message message) {
        LOGGER.info("user online status message: {}", message.toString());
        try {
            switch(message.getType()) {
        	case MessageTypeContent.OAUTH_LOGIN_PC:
        		handleMsgForPCLogin(message);
        		break;
        	case MessageTypeContent.OAUTH_LOGIN_APP:
        		handleMsgForAppLogin(message);
        		break;
        	case MessageTypeContent.OAUTH_LOGOUT:
        		handleMsgForLogout(message);
        		break;
        	case MessageTypeContent.OAUTH_REFRESH_EXPIRED_TIME:
        		handleMsgForExpiredTimeRefresh(message);
        		break;
        	default:
        		break;
            }
        }catch(Exception ex) {
        	LOGGER.error("user online status message handle failed! msg=" + message, ex);
        }
	}

	@Override
	public int[] getTypes() {
        return new int[] {
                MessageTypeContent.OAUTH_LOGIN_PC,
                MessageTypeContent.OAUTH_LOGIN_APP,
                MessageTypeContent.OAUTH_LOGOUT,
                MessageTypeContent.OAUTH_REFRESH_EXPIRED_TIME
        };
	}
	
	/**
	 * 处理APP登录类型的消息
	 * @param message
	 */
	private void handleMsgForAppLogin(Message message) {
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        String companyId = GlobalConstant.ROOT_ORGANIZATION_ID;
        String ipAddr = message.getHeader(MessageHeaderContent.IP);
        Query query = getQueryPart(memberId);
		int curTime = (int) (System.currentTimeMillis() / 1000L);
        int expirationTime = getExpirationTime();
		// 手机系统
		String phoneSystem = message.getHeader(MessageHeaderContent.SYSTEM);
		// 手机型号
		String phoneModel = message.getHeader(MessageHeaderContent.TERMINAL);
		Update update = new Update();
		update.set("company_id", companyId);
		update.set("phone_system", phoneSystem);
		update.set("phone_model", phoneModel);
		update.set("app_ip", ipAddr);
		update.set("app_login_time", curTime);
		update.set("app_expiration_time", expirationTime);
		WriteResult result = mongoTemplate.upsert(query, update, USER_ONLINE_INFO_COLL);
    	LOGGER.info("MangoResult: {}" + result.toString());
	}
	
	/**
	 * 处理PC登录类型的消息
	 * @param message
	 */
	private void handleMsgForPCLogin(Message message) {
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        String companyId = GlobalConstant.ROOT_ORGANIZATION_ID;
        String ipAddr = message.getHeader(MessageHeaderContent.IP);
        String userAgent = message.getHeader(MessageHeaderContent.USER_AGENT);
        Query query = getQueryPart(memberId);
		int curTime = (int) (System.currentTimeMillis() / 1000L);
        int expirationTime = getExpirationTime();
		// PC操作系统
        String pcSystem = UserAgent.parseUserAgentString(userAgent).getOperatingSystem().getName();
        // PC浏览器
        String pcBrowser = convertTerminal(UserAgent.parseUserAgentString(userAgent).getBrowser().getName());
		Update update = new Update();
		update.set("company_id", companyId);
		update.set("pc_system", pcSystem);
		update.set("pc_browser", pcBrowser);
		update.set("pc_ip", ipAddr);
		update.set("pc_login_time", curTime);
		update.set("pc_expiration_time", expirationTime);
		WriteResult result = mongoTemplate.upsert(query, update, USER_ONLINE_INFO_COLL);
    	LOGGER.info("MangoResult: {}" + result.toString());
	}
	
	/**
	 * 处理APP或PC退出类型的消息
	 * @param message
	 */
	private void handleMsgForLogout(Message message) {
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        // 终端类型(PC/APP)
        int type = Integer.parseInt(message.getHeader(MessageHeaderContent.TYPE));
        Query query = getQueryPart(memberId);
		Update update = new Update();
		if (type == GlobalConstant.TERMINAL_TYPE_PC) {
			update.set("pc_expiration_time", 0);
		}else {
    		update.set("app_expiration_time", 0);
		}
		WriteResult result = mongoTemplate.updateFirst(query, update, USER_ONLINE_INFO_COLL);
    	LOGGER.info("MangoResult: {}" + result.toString());
	}

	/**
	 * 处理过期时间刷新消息
	 * @param message
	 */
	private void handleMsgForExpiredTimeRefresh(Message message) {
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
		int type = Integer.parseInt(message.getHeader(MessageHeaderContent.TYPE));
        int expirationTime = getExpirationTime();
        Query query = getQueryPart(memberId);
		Update update = new Update();
		if (GlobalConstant.TERMINAL_TYPE_PC == type) {
			update.set("pc_expiration_time", expirationTime);
		} else {
			update.set("app_expiration_time", expirationTime);
		}
		WriteResult result = mongoTemplate.updateFirst(query, update, USER_ONLINE_INFO_COLL);
    	LOGGER.info("MangoResult: {}" + result.toString());
	}

	/**
	 * 每小时统计在线人数
	 */
	@Scheduled(cron = "0 0 0/1 * * ?" )
	private void onlineStatisticsTask(){
		// 作业任务可能存在多节点，redis作分布式任务锁
//		String lockState = cache.get("lock", String.class);
//		if ("1".equals(lockState)) {
//			return;
//		}
//		cache.set("lock", "1", 600);
		LOGGER.error("onlineStatisticsTask Start.");
		onlineStatisticsService.insert(System.currentTimeMillis());
		LOGGER.error("onlineStatisticsTask finished.");
	}

    private String convertTerminal(String terminal) {
        String convertTerminal = "其他";
        for(Map.Entry<String, String> entry: terminalMap.entrySet()) {
            if (terminal.startsWith(entry.getKey())) {
                convertTerminal = entry.getValue();
                break;
            }
        }
        return convertTerminal;
    }
    
    /**
     * 获得一个目标过期时间, 单位：秒
     * @return
     */
    private int getExpirationTime() {
    	return (int)(System.currentTimeMillis() / 1000L) + DEF_EXPIRED_INTERVAL_TIME;
    }

    private Query getQueryPart(String memberId) {
		Query query = new Query(Criteria.where("member_id").is(memberId));
		return query;
    }

}
