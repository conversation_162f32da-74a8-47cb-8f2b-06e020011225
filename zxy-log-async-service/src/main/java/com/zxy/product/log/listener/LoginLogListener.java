package com.zxy.product.log.listener;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.base.Strings;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.human.content.MessageHeaderContent;
import com.zxy.product.human.content.MessageTypeContent;
import com.zxy.product.log.entity.LoginLog;
import com.zxy.product.log.entity.UserActive;
import com.zxy.product.log.util.DateUtil;
import eu.bitwalker.useragentutils.UserAgent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.zxy.product.log.jooq.Tables.USER_ACTIVE;

/**
 * Created by cheng<PERSON> on 17/8/10.
 */
@Component
public class LoginLogListener extends AbstractMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginLogListener.class);

    private CommonDao<LoginLog> loginLogDao;

    private CommonDao<UserActive> userActiveDao;

    private static Map<String, String> terminalMap = new HashMap<>(6);

    private Cache cache;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("zxy-log","user-active");
    }

    static {
        terminalMap.put("Chrome", "chrome");
        terminalMap.put("Firefox", "火狐");
        terminalMap.put("Internet Explorer 11", "IE 11");
        terminalMap.put("Internet Explorer 10", "IE 10");
        terminalMap.put("Internet Explorer 9", "IE 9");
        terminalMap.put("Internet Explorer 8", "IE 8");

    }

    @Autowired
    public void setLoginLogDao(CommonDao<LoginLog> loginLogDao) {
        this.loginLogDao = loginLogDao;
    }

    @Autowired
    public void setUserActiveDao(CommonDao<UserActive> userActiveDao) {
        this.userActiveDao = userActiveDao;
    }

    @Override
    protected void onMessage(Message message) {
        LOGGER.info("login log message: {}", message.toString());
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        String organizationId = message.getHeader(MessageHeaderContent.ORGANIZATION_ID);
        String ipAddr = message.getHeader(MessageHeaderContent.IP);
        String userAgent = message.getHeader(MessageHeaderContent.USER_AGENT);
        int type = Integer.parseInt(message.getHeader(MessageHeaderContent.TYPE));
        long timestamp = Long.parseLong(message.getHeader( MessageHeaderContent.DATE_TIME));
        int passwordType = message.getHeader(MessageHeaderContent.PASSWORD_TYPE) == null ? 0 : Integer.parseInt(message.getHeader(MessageHeaderContent.PASSWORD_TYPE));
        int source = message.getHeader(MessageHeaderContent.SOURCE) == null ? 0 : Integer.parseInt(message.getHeader(MessageHeaderContent.SOURCE));
        String terminal;
        String system;
        switch(message.getType()) {
            case MessageTypeContent.OAUTH_LOGIN_PC:
                terminal = convertTerminal(UserAgent.parseUserAgentString(userAgent).getBrowser().getName());
                system = UserAgent.parseUserAgentString(userAgent).getOperatingSystem().getName();
                insertLoginLog(memberId,ipAddr,userAgent,terminal,type,system,organizationId,timestamp,passwordType,source);
                break;
            case MessageTypeContent.OAUTH_LOGIN_APP:
                terminal = message.getHeader(MessageHeaderContent.TERMINAL);
                system = message.getHeader(MessageHeaderContent.SYSTEM);
                insertLoginLog(memberId,ipAddr,userAgent,terminal,type,system,organizationId,timestamp,passwordType,source);
                break;
            case MessageTypeContent.OAUTH_LOGIN_WX:
                terminal = message.getHeader(MessageHeaderContent.TERMINAL);
                system = message.getHeader(MessageHeaderContent.SYSTEM);
                insertLoginLog(memberId,ipAddr,userAgent,terminal,type,system,organizationId,timestamp,passwordType,source);
                break;
            default:
                break;
        }
    }

    // 插入登录日志
    private void insertLoginLog(String memberId, String ipAddr, String userAgent, String terminal, int terminalType, String system, String organizationId,long timestamp, int passwordType, int source) {
        LoginLog loginLog = new LoginLog();
        loginLog.forInsert();

        loginLog.setMemberId(memberId);
        loginLog.setOrganizationId(organizationId);
        loginLog.setCreateTime(timestamp);
        loginLog.setIpAddr(ipAddr);
        loginLog.setStatus(1);
        loginLog.setSystem(system);
        loginLog.setTerminal(terminal);
        loginLog.setTerminalType(terminalType);
        loginLog.setUserAgent(userAgent);
        loginLog.setPasswordType(passwordType);
        loginLog.setSource(source);
        loginLogDao.insert(loginLog);

        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DF_STANDARD);
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(timestamp));
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int week = DateUtil.dayForWeek(sdf.format(new Date(timestamp)));

        // 使用redis做watchdog
        String key = memberId + "#" + year + "#" + month + "#" + day;
        if (!Strings.isNullOrEmpty(cache.get(key, String.class))) {
           return;
        }
        cache.set(key, "1", 10*60);

        List<UserActive> userActiveList = userActiveDao.execute(d -> d.select(USER_ACTIVE.fields())
                .from(USER_ACTIVE)
                .where(USER_ACTIVE.MEMBER_ID.eq(memberId))
                .and(USER_ACTIVE.YEAR_INFO.eq(year))
                .and(USER_ACTIVE.MONTH_INFO.eq(month))
                .and(USER_ACTIVE.DAY_INFO.eq(day))
                .fetchInto(UserActive.class)
        );

        if (CollectionUtils.isEmpty(userActiveList)){
            UserActive userActive = new UserActive();
            userActive.forInsert();
            userActive.setMemberId(memberId);
            userActive.setLoginTimes(1);
            userActive.setCreateTime(timestamp);
            userActive.setYearInfo(year);
            userActive.setMonthInfo(month);
            userActive.setDayInfo(day);
            userActive.setWeekInfo(week);
            userActive.setQuarterInfo(month % 3 == 0 ? month / 3 : month / 3 + 1);
            userActiveDao.insert(userActive);
        }else {
            UserActive userActive = userActiveList.get(0);
            userActive.setLoginTimes(userActive.getLoginTimes()+1);
            userActiveDao.update(userActive);
        }
        cache.clear(key);
        LOGGER.info("UserActive info : {}", userActiveList);
    }

    private String convertTerminal(String terminal) {
        String convertTerminal = "其他";
        for(Map.Entry<String, String> entry: terminalMap.entrySet()) {
            if (terminal.startsWith(entry.getKey())) {
                convertTerminal = entry.getValue();
                break;
            }
        }
        return convertTerminal;
    }

    @Override
    public int[] getTypes() {
        return new int[] {
                MessageTypeContent.OAUTH_LOGIN_PC,
                MessageTypeContent.OAUTH_LOGIN_APP,
                MessageTypeContent.OAUTH_LOGIN_WX
        };
    }
}
