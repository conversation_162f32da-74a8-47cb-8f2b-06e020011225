package com.zxy.product.log.listener.display;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.content.MessageTypeContent;
import com.zxy.product.log.entity.LoginLog;
import com.zxy.product.log.entity.LoginRecord;
import com.zxy.product.log.util.DateUtil;
import com.zxy.product.system.content.MessageHeaderContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.zxy.product.log.content.CacheKeyConstant.DISPLAY_MODULE;
import static com.zxy.product.log.content.CacheKeyConstant.ZXY_LOG;
import static com.zxy.product.log.jooq.Tables.LOGIN_LOG;
import static com.zxy.product.log.jooq.Tables.MEMBER;

/**
 * <AUTHOR> zhouyong
 */
@Component
public class LoginRecordListener extends AbstractMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginRecordListener.class);

    private static final String LOGIN_COUNT = String.join("#", ZXY_LOG, DISPLAY_MODULE, CacheKeyConstant.LOGIN_COUNT);

    private Redis redis;

    private CommonDao<LoginLog> loginLogDao;

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setLoginLogDao(CommonDao<LoginLog> loginLogDao) {
        this.loginLogDao = loginLogDao;
    }

    @Override
    protected void onMessage(Message message) {
        String currentTime = message.getHeader(MessageHeaderContent.SYSTEM_TIME);
        LOGGER.info("==++++++++++>登录数据统计<++++++++++-> START TIME : {}", DateUtil.format(new Date(Long.parseLong(currentTime)), DateUtil.DATE_TIME_SEPARATOR_PATTERN));
        LoginRecord record = new LoginRecord();
        loginLogDao.execute(ctx ->
            ctx.select(LOGIN_LOG.ID.count().as("visit"),LOGIN_LOG.MEMBER_ID.countDistinct().as("number"))
                .from(LOGIN_LOG).leftJoin(MEMBER).on(LOGIN_LOG.MEMBER_ID.eq(MEMBER.ID))
                .where(MEMBER.STATUS.eq(1).and(LOGIN_LOG.CREATE_TIME.between(DateUtil.beginTimestampCurrentYear(),DateUtil.lastTimestampYesterday())))
                .fetchOne(r -> {
                    record.setTotalVisit(Optional.ofNullable(r.get(0, Long.class)).orElse(0L));
                    record.setNumber(Optional.ofNullable(r.get(1, Long.class)).orElse(0L));
                    return null;
                })
        );
        Map<Integer, Long> terminalCount = new HashMap<>();
        loginLogDao.execute(ctx ->
            ctx.select(Fields.start().add(LOGIN_LOG.TERMINAL_TYPE).add(LOGIN_LOG.ID.count().as("count")).end())
                .from(LOGIN_LOG).leftJoin(MEMBER).on(LOGIN_LOG.MEMBER_ID.eq(MEMBER.ID))
                .where(MEMBER.STATUS.eq(1).and(LOGIN_LOG.CREATE_TIME.between(DateUtil.beginTimestampCurrentYear(),DateUtil.lastTimestampYesterday())))
                .groupBy(LOGIN_LOG.TERMINAL_TYPE)
                .fetch(r -> {
                    terminalCount.put(r.get(0, Integer.class), r.get(1,Long.class));
                    return null;
                })
        );
        if (terminalCount.size() > 0) {
            terminalCount.forEach((key,value) -> {
                if (key == 1) { // pc
                    record.setPcRatio(String.join("",new BigDecimal(String.valueOf(value.doubleValue()/record.getTotalVisit())).setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString(),"%"));
                } else if (key == 2) { // app
                    record.setAppRatio(String.join("",new BigDecimal(String.valueOf(value.doubleValue()/record.getTotalVisit())).setScale(2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString(),"%"));
                }
            });
        }
        LOGGER.info("==++++++++++>登录数据统计<++++++++++-> DATA : {}", record);
        redis.process(jedis -> {
            try {
                jedis.set(LOGIN_COUNT, new ObjectMapper().writeValueAsString(record));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return null;
        });
        LOGGER.info("==++++++++++>登录数据统计<++++++++++-> END   TIME : {}", DateUtil.format(new Date(), DateUtil.DATE_TIME_SEPARATOR_PATTERN));
    }

    @Override
    public int[] getTypes() {
        return new int[]{MessageTypeContent.LOGIN_COUNT_TASK_MESSAGE};
    }
}
