package com.zxy.product.log.config;

import com.google.common.collect.Lists;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientOptions.Builder;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoDbFactory;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Configuration
public class MongoConfig {

    @Bean
    public MongoDbFactory mongoDbFactory(Environment env) throws Exception {
    	String host = env.getProperty("spring.data.mongodb.host","127.0.0.1");
		String port = env.getProperty("spring.data.mongodb.port",String.class,"27017");
    	String dbName = env.getProperty("spring.data.mongodb.dbname","test");
    	int connectTimeout = env.getProperty("spring.data.mongodb.connectTimeout", Integer.class, 60000);
    	int socketTimeout = env.getProperty("spring.data.mongodb.socketTimeout", Integer.class, 120000);
    	int connectionsPerHost = env.getProperty("spring.data.mongodb.connectionsPerHost", Integer.class, 100);
    	int maxConnectionIdleTime = env.getProperty("spring.data.mongodb.maxConnectionIdleTime", Integer.class, 120000);
    	int maxConnectionLifeTime = env.getProperty("spring.data.mongodb.maxConnectionLifeTime", Integer.class, 120000);
    	boolean socketKeepAlive = env.getProperty("spring.data.mongodb.socketKeepAlive", Boolean.class, true);
    	String username = env.getProperty("spring.data.mongodb.username","test");
    	String password = env.getProperty("spring.data.mongodb.password","test");
    	Builder builder = MongoClientOptions.builder();
    	builder.connectTimeout(connectTimeout);
    	builder.socketTimeout(socketTimeout);
    	builder.maxConnectionIdleTime(maxConnectionIdleTime);
    	builder.maxConnectionLifeTime(maxConnectionLifeTime);
    	builder.socketKeepAlive(socketKeepAlive);
    	builder.connectionsPerHost(connectionsPerHost);
    	
    	MongoClientOptions options = builder.build();
    	//ServerAddress serverAddress = new ServerAddress(host,port);

		List<ServerAddress> serverAddressList = Lists.newArrayList();
		List<String> hosts = Arrays.asList(host.split(","));
		List<String> ports = Arrays.asList(port.split(","));
		for (int i = 0; i < hosts.size(); i++) {
			serverAddressList.add(new ServerAddress(hosts.get(i),Integer.parseInt(ports.get(i))));
		}



		MongoCredential credentials = MongoCredential.createCredential(username, dbName, password.toCharArray());
    	List<MongoCredential> credentialsList = new ArrayList<MongoCredential>();
    	credentialsList.add(credentials);
    	MongoClient client = new MongoClient(serverAddressList, credentialsList, options);
        return new SimpleMongoDbFactory(client, dbName);
    }

    @Bean
    public MongoTemplate mongoTemplate(MongoDbFactory mongoDbFactory) throws Exception {
        @SuppressWarnings("deprecation")
        MappingMongoConverter converter = new MappingMongoConverter(mongoDbFactory, new MongoMappingContext());
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return new MongoTemplate(mongoDbFactory, converter);
    }

}
