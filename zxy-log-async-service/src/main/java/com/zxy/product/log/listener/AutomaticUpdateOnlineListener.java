package com.zxy.product.log.listener;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.cache.redis.RedisCacheService;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.serialize.Serializer;
import com.zxy.product.log.content.ErrorCode;
import com.zxy.product.log.content.GlobalConstant;
import com.zxy.product.log.content.MessageTypeContent;
import com.zxy.product.log.util.DateUtil;
import com.zxy.product.system.api.operation.MessageSendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import java.time.Instant;
import java.util.Date;
import java.util.Optional;

/**
 * @Classname automaticUpdateOnlineListener
 * @Description 定时任务更新在线人数
 * @Date 2022/04/07
 * @Created by futzh
 */
@Component
public class AutomaticUpdateOnlineListener extends AbstractMessageListener {
    private final Logger log = LoggerFactory.getLogger(AutomaticUpdateOnlineListener.class);
    private Cache cache;
    private Redis redis;
    private MongoTemplate mongoTemplate;
    private MessageSendService messageSendService;
    private static final String ONLINE_COUNT = "online-count";
    private static final String ONLINE_COUNT_PC = "online-count-pc";
    private static final String ONLINE_COUNT_APP = "online-count-app";
    private static final String ONLINE_COUNT_TIME = "online-count-time";
    private static final String ONLINE_COUNT_LOCK = "online-count-lock";

    /**
     * Mongodb中存储用户在线状态信息的集合名
     */
    private static final String USER_ONLINE_INFO_COLL = "user_online_info";
    public static final String APPLICATION_NAME = "zxy-log-web-server";
    public static final Integer EXPIRE_TIME = 60 ;

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setMessageSendService(MessageSendService messageSendService) {
        this.messageSendService = messageSendService;
    }

    @Autowired
    public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }
    @Autowired
    public void setOauthRedisCacheService(Redis redis, Serializer ser) {
        RedisCacheService redisCacheService = new RedisCacheService(APPLICATION_NAME);
        redisCacheService.setRedis(redis);
        redisCacheService.setSerializer(ser);
        this.cache = redisCacheService.create("online-info");
    }

    @Override
    protected void onMessage(Message message) {
        try{
            log.info("定时任务更新在线人数 开始时间:{}", DateUtil.format(Date.from(Instant.now()), DateUtil.DATE_TIME_SEPARATOR_PATTERN));
            Long lock = redis.process(x -> x.setnx("zxy-log-web-server#online-info#" + ONLINE_COUNT_LOCK, java.util.UUID.randomUUID().toString()));
            redis.process(x -> x.expire("zxy-log-web-server#online-info#" + ONLINE_COUNT_LOCK, EXPIRE_TIME));
            if(lock == 1){
                cache.set(ONLINE_COUNT, getOnlineCount(Optional.of(GlobalConstant.TERMINAL_TYPE_ALL)));
                cache.set(ONLINE_COUNT_PC, getOnlineCount(Optional.of(GlobalConstant.TERMINAL_TYPE_PC)));
                cache.set(ONLINE_COUNT_APP, getOnlineCount(Optional.of(GlobalConstant.TERMINAL_TYPE_APP)));
                cache.set(ONLINE_COUNT_TIME, System.currentTimeMillis());
                log.info("定时任务更新在线人数 结束时间:{}", DateUtil.format(Date.from(Instant.now()), DateUtil.DATE_TIME_SEPARATOR_PATTERN));
                redis.process(x -> x.del("zxy-log-web-server#online-info#" + ONLINE_COUNT_LOCK));
            }
        }catch (Exception e){
            cache.set(ONLINE_COUNT, 98765);
            redis.process(x -> x.del("zxy-log-web-server#online-info#" + ONLINE_COUNT_LOCK));
            log.error("定时任务更新在线人数失败，失败原因为："+e.getMessage());
        }
    }

    /**
     * 通过mongo查询在线登录人数
     * @return
     */
    public int getOnlineCount(Optional<Integer> terminalType) {

        Integer terminalTypeCode = null;

        //1.在线登录统计类型判断
        if (terminalType.isPresent()) {
            terminalTypeCode = terminalType.get();
        }else {
            log.error("在线人数统计类型获取错误");
            throw new UnprocessableException(ErrorCode.OnlineTypeError);
        }

        //2.查询参数封装
        int curTime = (int) (System.currentTimeMillis() / 1000L);
        Criteria criteriaApp = Criteria.where("app_expiration_time").gt(curTime);
        Criteria criteriaPc = Criteria.where("pc_expiration_time").gt(curTime);
        Criteria criteriaCondition = new Criteria();

        if(Integer.valueOf(GlobalConstant.TERMINAL_TYPE_PC).equals(terminalTypeCode)){

            //pc
            criteriaCondition.andOperator(criteriaPc);
        }else if(Integer.valueOf(GlobalConstant.TERMINAL_TYPE_APP).equals(terminalTypeCode)){

            //app
            criteriaCondition.andOperator(criteriaApp);
        }else {

            //pc+app
            criteriaCondition = criteriaCondition.orOperator(criteriaApp, criteriaPc);
        }

        //3.查询
        Query query = new Query(criteriaCondition);
        int count = (int) mongoTemplate.count(query, USER_ONLINE_INFO_COLL);
        log.info("当前在线人数:{},类型:{}", count,terminalTypeCode);
        return count;
    }
    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.UPDATE_ONLINENUM_TASK_MESSAGE
        };
    }
}
