package com.zxy.product.log.util;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * <AUTHOR> zhouyong
 */
public class DateUtil {

    private DateUtil(){}

    public static final String DATE_PATTERN_NEW = "yyyyMMdd";

    public static final String DATE_TIME_SEPARATOR_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String DF_STANDARD = "yyyy-MM-dd";

    public static final String DATE_PATTERN_YM = "yyyyMM";
    /**
     * @return : 获取昨天最后时间戳
     */
    public static long lastTimestampYesterday() {
        return LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MAX).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * @return : 获取7天前时间戳
     */
    public static long lastTimestamp7Day() {
        return LocalDateTime.of(LocalDate.now().minusDays(7), LocalTime.MIN).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }
    /**
     * @param timestamp 时间戳
     * @return : 获取此时间戳前一天最后时间戳
     */
    public static long lastTimestampPreviousDay(long timestamp) {
        LocalDate localDate = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.systemDefault()).toLocalDate();
        return LocalDateTime.of(localDate.minusDays(1), LocalTime.MAX).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * @return : 获取昨天最初时间戳
     */
    public static long firstTimestampYesterday(){
        return LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * @param timestamp 时间戳
     * @return : 获取此时间戳前一天最后时间戳
     */
     public static long firstTimestampPreviousDay(long timestamp) {
        LocalDate localDate = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.systemDefault()).toLocalDate();
        return LocalDateTime.of(localDate.minusDays(1), LocalTime.MIN).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }


    /**
     * @return : 获取今年开始时间戳
     */
    public static long beginTimestampCurrentYear() {
        return LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfYear()), LocalTime.MIN).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * @return : 获取7天前开始时间
     */
    public static long before7Timestamp() {
        //Date offset = offset(new Date(), Calendar.DAY_OF_MONTH, -7);
        //return offset.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().atTime(LocalTime.MIN).toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        Calendar instance = Calendar.getInstance();
        instance.setTime(offset(new Date(), Calendar.DAY_OF_MONTH, -7));
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.MILLISECOND, 0);
        return instance.getTime().getTime();
    }

    public static String format(Date date, String pattern) {
        Instant instant = date.toInstant();
        LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(dateTimeFormatter);
    }

    /**
     * @param date      : 时间
     * @param dateField ：便宜量单位 Calendar.DAY_OF_MONTH  {@link Calendar}
     * @param offset    ：时间偏移量
     * @return          ：Date
     */
    public static Date offset(Date date,int dateField,int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(dateField, offset);
        return calendar.getTime();
    }

    /**
     * @param time : yyyymmdd
     * @return  : 1970.1.1到现在的天数
     */
    public static Long format2EpochDay(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN_NEW);
        LocalDate localDate = LocalDate.parse(time, formatter);
        return localDate.atStartOfDay(ZoneId.systemDefault()).toLocalDate().toEpochDay();
    }

    /**
     * @param epochDay : 1970.1.1到现在的天数
     * @return : yyyymmdd
     */
    public static Long dateFormat(Long epochDay) {
        LocalDate date = LocalDate.ofEpochDay(epochDay).atStartOfDay(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN_NEW);
        return Long.valueOf(date.format(formatter));
    }

    /**
     * 计算某一天是周几
     *
     * @param pTime
     * @return 格式为2018-01-01
     * 如果错误就返回0
     */
    public static int dayForWeek(String pTime) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date tmpDate = format.parse(pTime);
            Calendar cal = Calendar.getInstance();
            int[] weekDays = {7, 1, 2, 3, 4, 5, 6};
            try {
                cal.setTime(tmpDate);
            } catch (Exception e) {
                e.printStackTrace();
            }
            int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。
            if (w < 0) {
                w = 0;
            }
            return weekDays[w];
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    public static Integer getYear(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
        Date date = new Date(time);
        return Integer.valueOf(sdf.format(date).substring(0,4));
    }

    public static Integer getMonth(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date(time);
        return Integer.valueOf(sdf.format(date).substring(0,6));
    }

    public static Integer getDay(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date(time);
        return Integer.valueOf(sdf.format(date));
    }

    //获取截止至昨天之前的5周的开始时间（周一）和结束时间（周日）
    public static SortedMap<Integer,Integer> getBefore5WeekMap(){
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 获取本周的开始日期（周一）
        LocalDate startOfWeek = yesterday.with(DayOfWeek.MONDAY);

        // 获取本周的结束日期（周日）
        LocalDate endOfWeek = yesterday.with(DayOfWeek.SUNDAY);

        // 获取前5周的开始时间和结束时间
        LocalDate previousWeekStart = startOfWeek.minusWeeks(5);
        LocalDate previousWeekEnd = endOfWeek.minusWeeks(5);
        SortedMap<Integer,Integer> result = new TreeMap<>();
        // 获取近五周每周的开始和结束时间
        while (previousWeekStart.isBefore(startOfWeek)) {
            previousWeekStart = previousWeekStart.plus(7, ChronoUnit.DAYS);
            previousWeekEnd = previousWeekEnd.plus(7, ChronoUnit.DAYS);

            result.put(Integer.valueOf(previousWeekStart.format(DateTimeFormatter.ofPattern("yyyyMMdd"))),Integer.valueOf(previousWeekEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"))));
        }
        return result;
    }

    //获取以昨天作为起始时间的11月份之前的月份
    public static Integer get11MonthBefore(){
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 获取11个月前的日期
        LocalDate elevenMonthsBefore = yesterday.minusMonths(11);
        Integer yearAndMonth = Integer.valueOf(elevenMonthsBefore.format(DateTimeFormatter.ofPattern(DATE_PATTERN_YM)));
        return yearAndMonth;
    }

    //获取以昨天时间的年份月份
    public static Integer getYesterdayYearAndMonth(){
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        Integer yesterdayYearAndMonth = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern(DATE_PATTERN_YM)));
        return yesterdayYearAndMonth;
    }

    //获取以昨天作为起始时间的11月份之前的月份集合
    public static Set<Integer> get11MonthBeforeList(){
        Set<Integer> result = new HashSet<>();
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        Integer yearAndMonth = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("yyyyMM")));
        result.add(yearAndMonth);
        LocalDate elevenMonthsBefore = yesterday.minusMonths(11);

        // 获取11个月前的日期
        while (elevenMonthsBefore.isBefore(yesterday)){
            yearAndMonth = Integer.valueOf(elevenMonthsBefore.format(DateTimeFormatter.ofPattern("yyyyMM")));
            elevenMonthsBefore = elevenMonthsBefore.plus(1, ChronoUnit.MONTHS);
            result.add(yearAndMonth);
        }
        return result;
    }

    //获取以昨天作为起始时间的11月份之前的月份集合
    public static Set<String> get11MonthBeforeStrList(){
        Set<String> result = new HashSet<>();
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String yearAndMonth = yesterday.format(DateTimeFormatter.ofPattern("yyyyMM"));
        result.add(yearAndMonth);
        LocalDate elevenMonthsBefore = yesterday.minusMonths(11);

        // 获取11个月前的日期
        while (elevenMonthsBefore.isBefore(yesterday)){
            yearAndMonth = elevenMonthsBefore.format(DateTimeFormatter.ofPattern("yyyyMM"));
            elevenMonthsBefore = elevenMonthsBefore.plus(1, ChronoUnit.MONTHS);
            result.add(yearAndMonth);
        }
        return result;
    }
    //获取以昨天作为起始时间的11月份之前的月份集合
    public static Map<Integer,Integer> get11MonthBeforeMap(){
        Map<Integer,Integer> result = new HashMap<>();
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        Integer year = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("yyyy")));
        Integer month = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("mm")));
        result.put(year,month);
        LocalDate elevenMonthsBefore = yesterday.minusMonths(11);

        // 获取11个月前的日期
        while (elevenMonthsBefore.isBefore(yesterday)){
            year =  Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("yyyy")));
            month = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("mm")));
            elevenMonthsBefore = elevenMonthsBefore.plus(1, ChronoUnit.MONTHS);
            result.put(year,month);
        }
        return result;
    }
}

