package com.zxy.product.log.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.log.entity.AuditLog;
import eu.bitwalker.useragentutils.UserAgent;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.AsyncRestTemplate;

import java.net.URI;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Component
public class AuditLogListener extends AbstractMessageListener implements EnvironmentAware{

    private Logger LOGGER = LoggerFactory.getLogger(AuditLogListener.class);

    private final String TEMPLATE = "{0}于{1}{2}";
    private final String TEMPLATE_FIND = "{0}于{1}查询了{2}";

    private static final int type = 88802;
    private static final String ORGANIZATION_ID = "organization_id";
    private static final String MODULE = "module";
    private static final String SUB_MODULE = "sub-module";
    private static final String ACTION = "action";
    private static final String FIRST_ACTION = "first-action";
    private static final String SECOND_ACTION = "second-action";
    private static final String MEMBER_FULL_NAME = "member_full_name";
    private static final String CURRENT_DATE = "current-date";
    private static final String DESC = "desc";
    private static final String DESC_REQUEST = "desc-request";
    private static final String DESC_REST_TEMPLATE_KEYS = "desc-rest-template-keys";
    private static final String DESC_REST_TEMPLATE_IDS = "desc-rest-template-ids";
    private static final String DESC_REST_TEMPLATE_JSONS = "desc-rest-template-jsons";
    private static final String USER_AGENT = "user-agent";
    private static final String IP = "ip";
    private static final String SPLIT_FLAG = "==";

    private  String[] memberFullNameRoot;
    private Environment env;
    private CommonDao<AuditLog> auditLogDao;
    private AsyncRestTemplate asyncRestTemplate;

    @Autowired
    public void setAuditLogDao(CommonDao<AuditLog> auditLogDao) {
        this.auditLogDao = auditLogDao;
    }

    @Autowired
    public void setAsyncRestTemplate(AsyncRestTemplate asyncRestTemplate) {
        this.asyncRestTemplate = asyncRestTemplate;
    }

    @Override
    protected void onMessage(Message message) {
        LOGGER.info(message.toString());

        try {
            String memberFullName = message.getHeader(MEMBER_FULL_NAME);
            LOGGER.info("当前人姓名:{}", memberFullName);
            LOGGER.info("过滤名:{}", Arrays.asList(memberFullNameRoot));
            if (Arrays.asList(memberFullNameRoot).contains(memberFullName)){
                LOGGER.error("超管过滤");
                return;
            }

            AtomicBoolean haveException = new AtomicBoolean(false);
            String organizationId = message.getHeader(ORGANIZATION_ID);
            Long currentDate = Long.parseLong(message.getHeader(CURRENT_DATE));
            String module = message.getHeader(MODULE);
            String subModule = message.getHeader(SUB_MODULE);
            int action = Integer.parseInt(message.getHeader(ACTION));
            String firstAction = message.getHeader(FIRST_ACTION);
            String secondAction = message.getHeader(SECOND_ACTION);
            String userAgent = message.getHeader(USER_AGENT);
            String ip = message.getHeader(IP);
            String desc = message.getHeader(DESC);
            String[] request = "".equals(message.getHeader(DESC_REQUEST)) ? new String[]{} : message.getHeader(DESC_REQUEST).split(SPLIT_FLAG);
            String[] keys = "".equals(message.getHeader(DESC_REST_TEMPLATE_KEYS)) ? new String[]{} : message.getHeader(DESC_REST_TEMPLATE_KEYS).split(SPLIT_FLAG);
            String[] ids = "".equals(message.getHeader(DESC_REST_TEMPLATE_IDS)) ? new String[]{} : message.getHeader(DESC_REST_TEMPLATE_IDS).split(SPLIT_FLAG);

            CountDownLatch cdl = new CountDownLatch(keys.length);
            Map<Integer, String[]> map = new HashMap<Integer, String[]>();
            for(int i=0; i<keys.length; i++){
                final int finalInt = i;
                String url = env.getProperty("key." + keys[i]) + ids[i];
                LOGGER.error("request url: {}", url);
                asyncRestTemplate.getForEntity(new URI(url), String.class).addCallback(response -> {
                    try {
                        LOGGER.info("response body: {}", new String(response.getBody()));
                        JSONObject jsonObject = new JSONObject(response.getBody());
                        LOGGER.info("jsonObject: {}", jsonObject);
                        map.put(finalInt, Arrays.stream(message.getHeader(DESC_REST_TEMPLATE_JSONS).split(SPLIT_FLAG)).map(jsonObject::getString).toArray(String[]::new));
                    }
                    finally {
                        cdl.countDown();
                    }
                }, failure -> {
                	LOGGER.error("response error: {}", failure);
                    haveException.set(true);
                    cdl.countDown();
                });
            }

            cdl.await(60, TimeUnit.SECONDS); // 默认超时时间60秒
            if (!haveException.get()) { // 如果请求正常，则入库
                List<String> allValues = new ArrayList<String>();
                for (int i = 0; i < request.length; i++) {
                    if ("".equals(request[i])) {
                        request[i] = null;
                    }
                }
                allValues.addAll(Arrays.stream(request).collect(Collectors.toList())); // request参数
                allValues.addAll(new TreeSet<Integer>(map.keySet()).stream().map(map::get).map(Arrays::stream).flatMap(a -> a).collect(Collectors.toList()));
                String descStr = MessageFormat.format(desc, allValues.toArray());

                String lastDesc = null;
                //13 = 查询
                if (action == 13) {
                    LOGGER.info("allValues参数:{}",allValues);
                    LOGGER.info("查询参数开始处理,处理前参数:{}", descStr);
                     descStr = findDesc(descStr);
                    if (StringUtils.isEmpty(descStr)){
                        return;
                    }
                    LOGGER.info("查询参数处理结束,处理后参数:{}", descStr);
                    lastDesc = MessageFormat.format(TEMPLATE_FIND, memberFullName, formatDate(currentDate), descStr);

                }else {
                    lastDesc = MessageFormat.format(TEMPLATE, memberFullName, formatDate(currentDate), descStr);
                }
                LOGGER.error("lastDesc:" + lastDesc);
                AuditLog auditLog = new AuditLog();
                auditLog.forInsert();
                auditLog.setOrganizationId(organizationId);
                auditLog.setDesc(lastDesc);
                auditLog.setModule(module);
                auditLog.setSubModule(subModule);
                //预览=14,如果是14则改为13(查询),其他照旧
                auditLog.setAction(action == 14 ? 13 : action);
                auditLog.setFirstAction(firstAction);
                auditLog.setSecondAction(secondAction);
                auditLog.setMemberFullName(memberFullName);
                auditLog.setUserAgent(userAgent);
                auditLog.setBrowser(UserAgent.parseUserAgentString(userAgent).getBrowser().getName());
                auditLog.setIp(ip);
                auditLog.setLogTime(currentDate);
                auditLogDao.insert(auditLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询参数处理
     *
     * @param desc
     * @return
     */
    private String findDesc(String desc) {
        //超级管理员于2023-03-22 15:12:06查询了题难易度型为单选、为中、状态为已发布、创建时间为2023-03-20 14:49:15到2023-03-20 14:49:20、的认证考试试题
        StringBuilder stringBuilder = new StringBuilder();
        try {
            List<String> aNull = Arrays.stream(desc.split("#")).filter(r -> !r.contains("null")).collect(Collectors.toList());
            for (int i = 0; i < aNull.size(); i++) {
                //最后两个元素不拼接、
                if (i >= aNull.size() - 2) {
                    stringBuilder.append(aNull.get(i));
                } else {
                    stringBuilder.append(aNull.get(i)).append("、");
                }
            }
        } catch (Exception e) {
            LOGGER.error("审计异常:{}", e.getMessage());
            return stringBuilder.toString();
        }
        return stringBuilder.toString();
    }

    @Override
    public int[] getTypes() {
        return new int[]{type};
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.env = environment;
        memberFullNameRoot = environment.getProperty("zxy.member.full.name.root", String.class, "超级管理员,系统管理员").split(",");
    }

    private String formatDate(Long date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(date));
    }
}
