package com.zxy.product.log.listener.display;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.task.DisplayTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import static com.zxy.product.log.content.CacheKeyConstant.DISPLAY_MODULE;
import static com.zxy.product.log.content.CacheKeyConstant.ZXY_LOG;
import static com.zxy.product.log.content.MessageTypeContent.*;

@Component
public class DisplayListener extends AbstractMessageListener {
    public static final Logger LOGGER = LoggerFactory.getLogger(DisplayListener.class);

    private static final String LOGIN_DISTRIBUTION = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.LOGIN_DISTRIBUTION);
    private static final String LOGIN_ACTIVITY_VIEW = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.LOGIN_ACTIVITY_VIEW);


    private DisplayTask displayTask;

    @Autowired
    public void setDisplayTask(DisplayTask displayTask) {
        this.displayTask = displayTask;
    }

    @Override
    protected void onMessage(Message message) {
        switch (message.getType()) {
            case LOGIN_ACTIVITY_VIEW_MESSAGE:
                displayTask.processLoginData();
                break;
            case LOGIN_ACTIVITY_DISTRIBUTION_MESSAGE:
                displayTask.loginDistribution();
            break;
            default:
                break;
        }
    }

    @Override
    public int[] getTypes() {
        return new int[]{LOGIN_ACTIVITY_VIEW_MESSAGE,LOGIN_ACTIVITY_DISTRIBUTION_MESSAGE};
    }
}
