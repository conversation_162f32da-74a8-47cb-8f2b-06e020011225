package com.zxy.product.log.config;

import com.zxy.product.log.task.AutoUpdateOnlineTask;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@Configuration
@EnableScheduling
public class ScheduleConfig implements SchedulingConfigurer {

	@Bean(destroyMethod="shutdown")
	public Executor taskScheduler() {
		return Executors.newScheduledThreadPool(10);
	}

	@Bean
	public AutoUpdateOnlineTask autoSendMessageTask() {
        return new AutoUpdateOnlineTask();
	}

	@Override
	public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
		scheduledTaskRegistrar.setScheduler(taskScheduler());
		scheduledTaskRegistrar.addCronTask(autoSendMessageTask(),"*/5 * * * * ?");
	}
}
