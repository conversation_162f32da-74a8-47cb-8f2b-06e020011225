package com.zxy.product.log.task;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zxy.common.cache.CacheService;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.content.MessageTypeContent;
import com.zxy.product.log.entity.LoginCountDay;
import com.zxy.product.log.entity.LoginLog;
import com.zxy.product.log.entity.Member;
import com.zxy.product.log.entity.MemberLoginDistribution;
import com.zxy.product.log.entity.MemberLoginStatus;
import com.zxy.product.log.entity.OrganizationLoginCountDay;
import com.zxy.product.log.util.DateUtil;
import com.zxy.product.system.content.MessageHeaderContent;
import org.jooq.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.zxy.product.log.content.CacheKeyConstant.DISPLAY_MODULE;
import static com.zxy.product.log.content.CacheKeyConstant.ZXY_LOG;
import static com.zxy.product.log.jooq.Tables.LOGIN_COUNT_DAY;
import static com.zxy.product.log.jooq.Tables.LOGIN_LOG;
import static com.zxy.product.log.jooq.Tables.MEMBER;
import static com.zxy.product.log.jooq.Tables.MEMBER_DETAIL;
import static com.zxy.product.log.jooq.Tables.ORGANIZATION;
import static com.zxy.product.log.jooq.Tables.ORGANIZATION_DETAIL;
import static com.zxy.product.log.jooq.Tables.USER_ACTIVE;

/**
 * <AUTHOR> zhouyong
 * @ClassName : DisplayTask
 * @Description : 大屏定时任务T+1
 * @date : 2021-10-28 17:42
 */
@Component
@EnableAsync
public class DisplayTask implements EnvironmentAware {


    public static final Logger LOGGER = LoggerFactory.getLogger(DisplayTask.class);

    private static final String LOGIN_COUNT_TASK = String.join("#", ZXY_LOG, DISPLAY_MODULE, CacheKeyConstant.LOGIN_COUNT, "task");

    private static final String DAU_TRENDS_TASK = String.join("#", ZXY_LOG, DISPLAY_MODULE, CacheKeyConstant.DAU_TRENDS, "task");

    private static final String RESOURCE_TOP3_TASK = String.join("#", ZXY_LOG, DISPLAY_MODULE, CacheKeyConstant.RESOURCE_TOP3, "task");

    public static final String CACHE_KEY_GENERATE_RV_TASK = String.join("#",ZXY_LOG,DISPLAY_MODULE,CacheKeyConstant.RESOURCE_VISIT_STATISTIC,"task");

    public static final String CACHE_KEY_AUTOMATIC_UPDATE_TASK = String.join("#",ZXY_LOG,DISPLAY_MODULE,CacheKeyConstant.AUTOMATIC_UPDATE_ONLINE,"task");

    private static final String LOGIN_DISTRIBUTION = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.LOGIN_DISTRIBUTION);
    private static final String LOGIN_ACTIVITY_VIEW = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.LOGIN_ACTIVITY_VIEW);

    public static final Long THREE_YEAR  = 94608000000L;
    public static final Long SIX_YEAR  = 189216000000L;
    public static final Long TEN_YEAR  = 315360000000L;
    private final static String GONG_CHAN_DANG_YUAN = "3e93951b-1101-4970-849e-0fea36f7d79f";
    private final static String YU_BEI_DANG_YUAN = "386257c5-783a-45de-95b9-21fb1b95b861";
    private static final String LOGIN_COUNT_LOCK = "login-count-lock";

    private MessageSender messageSender;

    private CacheService cacheService;

    private CommonDao<LoginLog> loginLogDao;

    private CommonDao<LoginCountDay> loginCountDayCommonDao;

    private CommonDao<OrganizationLoginCountDay> organizationLoginCountDayCommonDao;

    private String provinceIds;

    private Redis redis;

    @Autowired
    public void setLoginLogDao(CommonDao<LoginLog> loginLogDao) {
        this.loginLogDao = loginLogDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cacheService = cacheService;
    }

    @Autowired
    public void setLoginCountDayCommonDao(CommonDao<LoginCountDay> loginCountDayCommonDao) {
        this.loginCountDayCommonDao = loginCountDayCommonDao;
    }

    @Autowired
    public void setOrganizationLoginCountDayCommonDao(CommonDao<OrganizationLoginCountDay> organizationLoginCountDayCommonDao) {
        this.organizationLoginCountDayCommonDao = organizationLoginCountDayCommonDao;
    }

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    /**
     * 登录数据统计
     */
    @Async
    @Scheduled(cron = "0 30 0 * * ?")
    public void loginRecords() {
        cacheService.create(this.getClass().getName(), 60 * 60 * 3).get(LOGIN_COUNT_TASK,() -> {
            messageSender.send(MessageTypeContent.LOGIN_COUNT_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
            return LOGIN_COUNT_TASK;
        });
    }

    /**
     * 学员日活趋势
     */
    @Async
    @Scheduled(cron = "0 40 0 * * ?")
    public void dauTrends() {
        cacheService.create(this.getClass().getName(), 60 * 60 * 3).get(DAU_TRENDS_TASK,() -> {
            messageSender.send(MessageTypeContent.DAU_TRENDS_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
            return DAU_TRENDS_TASK;
        });
    }

    /**
     * 热门资源30日TOP3 (专题、课程)
     * 定时任务执行时间需设置在 countAndGenerateResourceVisits 任务之后，且预留一段时间
     */
    @Async
    @Scheduled(cron = "0 0 5 * * ?")
    public void resourceTop3() {
        cacheService.create(this.getClass().getName(), 60 * 60 * 3).get(RESOURCE_TOP3_TASK,() -> {
            messageSender.send(MessageTypeContent.RESOURCE_TOP3_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
            return RESOURCE_TOP3_TASK;
        });
    }

    @Async
    @Scheduled(cron = "0 0 2 * * ?")
    public void countAndGenerateResourceVisits() {
        cacheService.create(this.getClass().getName(), 60 * 60 * 3).get(CACHE_KEY_GENERATE_RV_TASK, () -> {
            messageSender.send(MessageTypeContent.GENERATE_RV_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
            return CACHE_KEY_GENERATE_RV_TASK;
        });
    }

    @Async
    @Scheduled(cron = "0 0 3 * * ? ")
    public void processLoginData() {
        LOGGER.error("数据大屏 - 登录活跃情况");
        Long lock = redis.process(x -> x.setnx("zxy-log-async-server#login-data#" + LOGIN_COUNT_LOCK, java.util.UUID.randomUUID().toString()));
        redis.process(x -> x.expire("zxy-log-async-server#login-data#" + LOGIN_COUNT_LOCK, 60));
        if (lock == 1){
            LOGGER.error("处理loginLog表数据");
            long startTime = DateUtil.firstTimestampYesterday();
            long endTime = DateUtil.lastTimestampPreviousDay(System.currentTimeMillis());

            List<LoginLog> yesterdayLoginLogData = getYesterdayLoginLogData(startTime,endTime);
            LOGGER.error("查询前一天loginLog表数据条数，{}" ,yesterdayLoginLogData.size());
            Integer systemCountNum = getSystemCountNum();
            LOGGER.error("当前系统总活跃用户，{}" ,systemCountNum);
            List<LoginLog> distinctMembers = yesterdayLoginLogData.stream().filter(distinctByKey(x -> x.getMemberId())).collect(Collectors.toList());
            LOGGER.error("前一天登录人次按照memberId去重后获得登录人数，{}" ,distinctMembers.size());
            Integer day = DateUtil.getDay(startTime);
            Integer month = DateUtil.getMonth(startTime);
            Integer year = DateUtil.getYear(startTime);
            Integer exitsDay = DateUtil.getDay(startTime);
            Integer count = exitsData(exitsDay);

            if (count > 0){
                deleteData(exitsDay);
            }
            insertLoginCountDay(startTime, endTime, yesterdayLoginLogData, systemCountNum, distinctMembers,day,month,year);
            insertOrganizationEntity(yesterdayLoginLogData, day, month, year);
            LOGGER.error("处理loginLog表数据完成，处理人群登录分布缓存");
//            loginDistribution();
            loginActivityStatus();
            LOGGER.error("处理人群登录分布缓存完成");

            redis.process(x -> x.del("zxy-log-async-server#login-data#" + LOGIN_COUNT_LOCK));
        }

    }
    public void deleteData(Integer day){
        loginCountDayCommonDao.delete(LOGIN_COUNT_DAY.DAY.eq(day));
    }
    public Integer exitsData(Integer day){
        Integer countMember = loginCountDayCommonDao.execute(dao -> {
            return dao.fetchCount(
                    dao.select(Fields.start().add(LOGIN_COUNT_DAY.ID).end())
                            .from(LOGIN_COUNT_DAY)
                            .where(LOGIN_COUNT_DAY.DAY.eq(day)));
        });
        return countMember;
    }

    public void loginActivityStatus() {
        LOGGER.error("数据大屏 — 获取前一天登陆活跃情况");
        Map<String,List<LoginCountDay>> resultMap = new HashMap<>();

        daysDataProcess(resultMap);
        weeksDataProcess(resultMap);
        monthDataProcess(resultMap);
        yearDataProcess(resultMap);

        redis.process(jedis -> {
            try {
                return jedis.set(LOGIN_ACTIVITY_VIEW, new ObjectMapper().writeValueAsString(resultMap));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    private void daysDataProcess(Map<String, List<LoginCountDay>> resultMap) {
        List<LoginCountDay> before7DaysLoginData = getBefore7DaysLoginData();
        before7DaysLoginData.stream().forEach(x -> {
            Integer memberTime = x.getMemberTime();
            x.setTime(month_day(String.valueOf(x.getDay())));
            x.setMemberTime(memberTime / 10000);
            x.setMemberTimes(new BigDecimal(String.valueOf((double) memberTime / 10000)).setScale(1, BigDecimal.ROUND_HALF_UP));
        });
        List<LoginCountDay> result = before7DaysLoginData.stream().sorted(Comparator.comparing(LoginCountDay::getDay)).collect(Collectors.toList());
//        Collections.sort(before7DaysLoginData);
        resultMap.put("7day",result);
    }

    private void weeksDataProcess(Map<String, List<LoginCountDay>> resultMap) {
        List<LoginCountDay> before5WeeksLoginData = getBefore5WeeksLoginData();
        Map<String, List<LoginCountDay>> collect = before5WeeksLoginData.stream().filter(x -> x.getTime() != null).collect(Collectors.groupingBy(LoginCountDay::getTime, Collectors.toList()));
        //获取5周内每周的起止日期
        SortedMap<Integer, Integer> before5WeekMap = DateUtil.getBefore5WeekMap();

        Map<String,List<LoginCountDay>> timeWeeksMap = new HashMap<>();
        Map<String,Integer> sortMap = new HashMap<>();
        //最终要返回的map ,key ：10.16-10.22 月.日期-月.日期
        before5WeekMap.forEach((key,value) ->{
            timeWeeksMap.put(week(String.valueOf(key),String.valueOf(value)),new ArrayList<>());
            sortMap.put(week(String.valueOf(key),String.valueOf(value)),key);
        });
        //根据查询出来的数据按照日期进行match到对应的集合中
        collect.forEach((key,value) ->{
            before5WeekMap.forEach((weekStart,weekEnd) ->{
                Integer day = Integer.valueOf(key);
                if(weekStart <= day && day <= weekEnd){
                    timeWeeksMap.get(week(String.valueOf(weekStart),String.valueOf(weekEnd))).addAll(value);
                }
            });
        });

        List<LoginCountDay> result = new ArrayList<>();
        //聚合，同一个key中的List累加
        timeWeeksMap.entrySet().stream().forEach(map ->{
            if (!CollectionUtils.isEmpty(map.getValue())){
                LoginCountDay loginCountDay = new LoginCountDay();
                Integer systemMember =map.getValue().stream().filter(x -> x.getSystemNum() != null).map(LoginCountDay::getSystemNum).reduce(0, Integer::sum);
                Integer memberNum = map.getValue().stream().filter(x -> x.getMemberNum() != null).map(LoginCountDay::getMemberNum).reduce(0, Integer::sum);
                Integer memberTime = map.getValue().stream().filter(x -> x.getMemberTime() != null).map(LoginCountDay::getMemberTime).reduce(0, Integer::sum);
                Integer day = map.getValue().stream().filter(x -> x.getDay() != null).map(LoginCountDay::getDay).reduce(0, Integer::max);
                loginCountDay.setMemberTime(memberTime / 10000);
                loginCountDay.setSystemNum(systemMember);
                loginCountDay.setMemberNum(memberNum);
                loginCountDay.setTime(map.getKey());
                loginCountDay.setDay(day);
                loginCountDay.setActivityRadio(new BigDecimal(String.valueOf(memberNum.doubleValue() /systemMember)).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
                loginCountDay.setMemberTimes(new BigDecimal(String.valueOf((double) memberTime / 10000)).setScale(1, BigDecimal.ROUND_HALF_UP));
                result.add(loginCountDay);
            }else{
                LoginCountDay loginCountDay = new LoginCountDay();
                loginCountDay.setTime(map.getKey());
                loginCountDay.setSystemNum(0);
                loginCountDay.setMemberNum(0);
                loginCountDay.setMemberTime(0);
                loginCountDay.setDay(sortMap.get(map.getKey()));
                result.add(loginCountDay);
            }
        });
//        Collections.sort(result);
        List<LoginCountDay> sortResult = result.stream().sorted(Comparator.comparing(LoginCountDay::getDay)).collect(Collectors.toList());
        resultMap.put("5week",sortResult);
    }

    private void monthDataProcess(Map<String, List<LoginCountDay>> resultMap) {
        Set<String> monthBeforeStrList = DateUtil.get11MonthBeforeStrList();
        List<LoginCountDay> loginCountDays = new ArrayList<>();
        monthBeforeStrList.stream().forEach(strDate ->{
            loginCountDays.addAll(getLoginMemberByYearAndMonth(Integer.valueOf(strDate.substring(0,4)),Integer.valueOf(strDate.substring(4,6))));
        });
        if (loginCountDays.size() < 12) {
            Set<Integer> monthBeforeList = DateUtil.get11MonthBeforeList();
            Set<Integer> longSet = loginCountDays.stream().map(x ->x.getMonth()).collect(Collectors.toSet());
            monthBeforeList.stream().forEach(x -> {
                if (longSet.contains(x)) {
                    return;
                }
                LoginCountDay loginCountDay = new LoginCountDay(x.toString());
                loginCountDay.setMonth(x);
                loginCountDay.setSystemNum(0);
                loginCountDay.setMemberNum(0);
                loginCountDay.setMemberTime(0);
                loginCountDays.add(loginCountDay);
            });
        }
        Integer systemCountNum = getSystemCountNum();
        loginCountDays.stream().forEach(x -> {
            Integer memberTime = x.getMemberTime();
            x.setTime(month(String.valueOf(x.getMonth())));
            x.setMemberTime(memberTime / 10000);
            x.setSystemNum(systemCountNum);
            x.setActivityRadio(new BigDecimal(String.valueOf(x.getMemberNum().doubleValue() / systemCountNum)).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
            x.setMemberTimes(new BigDecimal(String.valueOf((double) memberTime / 10000)).setScale(1, BigDecimal.ROUND_HALF_UP));
        });

        List<LoginCountDay> result = loginCountDays.stream().sorted(Comparator.comparing(LoginCountDay::getMonth)).collect(Collectors.toList());
        resultMap.put("12month",result);
    }

    private void yearDataProcess(Map<String, List<LoginCountDay>> resultMap) {
        LoginCountDay yearLoginData =   getCurYearLoginMember();
        Integer systemCountNum = getSystemCountNum();
        yearLoginData.setSystemNum(systemCountNum);
        yearLoginData.setActivityRadio(new BigDecimal(String.valueOf(yearLoginData.getMemberNum().doubleValue() /systemCountNum)).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
        yearLoginData.setMemberTimes(new BigDecimal(yearLoginData.getMemberTime()));//统一字段，方便前端使用
        resultMap.put("year",Arrays.asList(yearLoginData));
    }

    private List<LoginCountDay> getBefore7DaysLoginData(){
        long before7Day = DateUtil.lastTimestamp7Day();
        Integer day = DateUtil.getDay(before7Day);

        List<LoginCountDay> list = loginCountDayCommonDao.execute(e -> e.select(Fields.start()
                .add(LOGIN_COUNT_DAY).end())
                .from(LOGIN_COUNT_DAY))
                .where(LOGIN_COUNT_DAY.DAY.ge(day)).fetch().stream().map(r -> {
                    LoginCountDay log = new LoginCountDay();
                    log.setDay(r.getValue(LOGIN_COUNT_DAY.DAY));
                    log.setTime(String.valueOf(r.getValue(LOGIN_COUNT_DAY.DAY)));
                    log.setSystemNum(r.getValue(LOGIN_COUNT_DAY.SYSTEM_NUM));
                    log.setMemberNum(r.getValue(LOGIN_COUNT_DAY.MEMBER_NUM));
                    log.setMemberTime(r.getValue(LOGIN_COUNT_DAY.MEMBER_TIME));
                    log.setActivityRadio(new BigDecimal(String.valueOf(log.getMemberNum().doubleValue() / log.getSystemNum())).multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP));
                    return log;
                }).collect(Collectors.toList());

        //7天内某天没有数据，填充空值
        if (list.size() < 7) {
            List<Long> days = new ArrayList<>();
            Long dayNum = DateUtil.format2EpochDay(String.valueOf(day));
            IntStream.range(0, 7).forEach(i -> days.add(dayNum + i));
            Set<Long> longSet = list.stream().map(x ->x.getDay().longValue()).collect(Collectors.toSet());
            days.stream().map(DateUtil::dateFormat).forEach(x -> {
                if (longSet.contains(x)) {
                    return;
                }else{
                    LoginCountDay loginCountDay = new LoginCountDay(x.toString(), x.intValue());
                    loginCountDay.setSystemNum(0);
                    loginCountDay.setMemberNum(0);
                    loginCountDay.setMemberTime(0);
                    list.add(loginCountDay);
                }
            });
        }
        return list;
    }

    private List<LoginCountDay> getBefore12MonthLoginData(){
        Integer endDate = DateUtil.getYesterdayYearAndMonth();
        Integer startDate = DateUtil.get11MonthBefore();

        List<LoginCountDay> list = loginCountDayCommonDao.execute(e -> e.select(Fields.start()
                .add(LOGIN_COUNT_DAY.MONTH)
                .add(LOGIN_COUNT_DAY.DAY.max().as("day"))
                .add(LOGIN_COUNT_DAY.SYSTEM_NUM.sum().as("systemNum"))
                .add(LOGIN_COUNT_DAY.MEMBER_NUM.sum().as("memberNum"))
                .add(LOGIN_COUNT_DAY.MEMBER_TIME.sum().as("memberTime")).end())
                .from(LOGIN_COUNT_DAY))
                .where(LOGIN_COUNT_DAY.MONTH.ge(startDate).and(LOGIN_COUNT_DAY.MONTH.le(endDate)))
                .groupBy(LOGIN_COUNT_DAY.MONTH)
                .fetch().stream().map(r -> {
                    LoginCountDay log = new LoginCountDay();
                    log.setMonth(r.getValue(LOGIN_COUNT_DAY.MONTH));
                    log.setDay(r.getValue("day",Integer.class));
                    log.setSystemNum(r.getValue("systemNum",Integer.class));
                    log.setMemberTime(r.getValue("memberTime",Integer.class));
                    log.setMemberNum(r.getValue("memberNum",Integer.class));
                    log.setSystemNum(r.getValue("systemNum",Integer.class));
                    log.setTime(String.valueOf(r.getValue(LOGIN_COUNT_DAY.MONTH)));
                    log.setActivityRadio(new BigDecimal(String.valueOf(log.getMemberNum().doubleValue() / log.getSystemNum())).setScale(3, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
                    return log;
                }).collect(Collectors.toList());

        if (list.size() < 12) {
            Set<Integer> monthBeforeList = DateUtil.get11MonthBeforeList();
            Set<Integer> longSet = list.stream().map(x ->x.getMonth()).collect(Collectors.toSet());
            monthBeforeList.stream().forEach(x -> {
                if (longSet.contains(x)) {
                    return;
                }
                LoginCountDay loginCountDay = new LoginCountDay(x.toString());
                loginCountDay.setMonth(x);
                loginCountDay.setSystemNum(0);
                loginCountDay.setMemberNum(0);
                loginCountDay.setMemberTime(0);
                list.add(loginCountDay);
            });
        }
        return list;
    }

    private List<LoginCountDay> getYearLoginData(){

        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        Integer yearAndMonth = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("yyyy")));

        List<LoginCountDay> list = loginCountDayCommonDao.execute(e -> e.select(Fields.start()
                .add(LOGIN_COUNT_DAY).end())
                .from(LOGIN_COUNT_DAY))
                .where(LOGIN_COUNT_DAY.YEAR.eq(yearAndMonth)).fetch().stream().map(r -> {
                    LoginCountDay log = new LoginCountDay();
                    buildLoginCountDay(r, log);
                    return log;
                }).collect(Collectors.toList());

        return list;
    }

    private List<LoginCountDay> getLoginMemberByYearAndMonth(Integer year,Integer month){
        List<LoginCountDay> collect = loginCountDayCommonDao.execute(e -> e.select(Fields.start()
                .add(USER_ACTIVE.MEMBER_ID.countDistinct().as("memberNum"))
                .add(USER_ACTIVE.LOGIN_TIMES.sum().as("memberTime"))
                .add(USER_ACTIVE.YEAR_INFO)
                .add(USER_ACTIVE.MONTH_INFO)
                .end())
                .from(USER_ACTIVE))
                .leftJoin(MEMBER).on(USER_ACTIVE.MEMBER_ID.eq(MEMBER.ID))
                .where(MEMBER.FROM.eq(1)
                        .and(MEMBER.STATUS.eq(1))
                        .and(USER_ACTIVE.YEAR_INFO.eq(year))
                        .and(USER_ACTIVE.MONTH_INFO.eq(month))).fetch().stream().map(r -> {
                    LoginCountDay log = new LoginCountDay();
                    Integer curYear = r.getValue(USER_ACTIVE.YEAR_INFO);
                    Integer curMonth = r.getValue(USER_ACTIVE.MONTH_INFO);
                    String yearAndMonth = curYear + String.valueOf(curMonth < 10 ? "0"+curMonth : curMonth);
                    log.setMonth(Integer.valueOf(yearAndMonth));
                    log.setMemberTime(r.getValue("memberTime", Integer.class));
                    log.setMemberNum(r.getValue("memberNum", Integer.class));
                    return log;
                }).collect(Collectors.toList());
        return collect;
    }

    private LoginCountDay getCurYearLoginMember(){
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        Integer yearAndMonth = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("yyyy")));
        return loginCountDayCommonDao.execute(e -> e.select(Fields.start()
                .add(USER_ACTIVE.MEMBER_ID.countDistinct().as("memberNum"),USER_ACTIVE.LOGIN_TIMES.sum().as("memberTime")).end())
                .from(USER_ACTIVE))
                .leftJoin(MEMBER).on(USER_ACTIVE.MEMBER_ID.eq(MEMBER.ID))
                .where(MEMBER.FROM.eq(1).and(MEMBER.STATUS.eq(1)).and(USER_ACTIVE.YEAR_INFO.eq(yearAndMonth))).fetchOne(r -> {
                    LoginCountDay log = new LoginCountDay();
                    log.setMemberTime(r.getValue("memberTime",Integer.class));
                    log.setMemberNum(r.getValue("memberNum",Integer.class));
                    return log;
                });
    }



    private List<MemberLoginStatus> getCurrentYearLoginMemberDetail() {
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        Integer yearAndMonth = Integer.valueOf(yesterday.format(DateTimeFormatter.ofPattern("yyyy")));

        return loginCountDayCommonDao.execute(e -> e.select(Fields.start()
                .add(USER_ACTIVE.MEMBER_ID)
                .add(USER_ACTIVE.LOGIN_TIMES.sum().as("memberTime"))
                .add(MEMBER.SEX)
                .add(MEMBER_DETAIL.ENTRY_DATE)
                .add(MEMBER_DETAIL.POLITICALIZATION_ID)
                .end())
                .from(USER_ACTIVE))
                .leftJoin(MEMBER).on(USER_ACTIVE.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(MEMBER_DETAIL).on(MEMBER.ID.eq(MEMBER_DETAIL.MEMBER_ID))
                .where(MEMBER.STATUS.eq(1).and(USER_ACTIVE.YEAR_INFO.eq(yearAndMonth)))
                .groupBy(USER_ACTIVE.MEMBER_ID).fetch().stream().map(r -> {
                    MemberLoginStatus log = new MemberLoginStatus();
                    log.setEntryDate(r.getValue(MEMBER_DETAIL.ENTRY_DATE));
                    log.setMemberId(r.getValue(USER_ACTIVE.MEMBER_ID));
                    log.setSex(r.getValue(MEMBER.SEX));
                    log.setMemberTime(r.get("memberTime", Integer.class));
                    log.setPoliticalizationId(r.getValue(MEMBER_DETAIL.POLITICALIZATION_ID));
                    return log;
                }).collect(Collectors.toList());
    }


    private void buildLoginCountDay(Record r, LoginCountDay log) {
        log.setSystemNum(r.getValue(LOGIN_COUNT_DAY.SYSTEM_NUM));
        log.setMemberNum(r.getValue(LOGIN_COUNT_DAY.MEMBER_NUM));
        log.setMemberTime(r.getValue(LOGIN_COUNT_DAY.MEMBER_TIME));
    }

    private LoginCountDay buildLoginCountDay(Record r) {
        LoginCountDay log = new LoginCountDay();
        log.setDay(r.getValue(LOGIN_COUNT_DAY.DAY));
        buildLoginCountDay(r, log);
        return log;
    }

    private List<LoginCountDay> getBefore5WeeksLoginData(){
        SortedMap<Integer,Integer> before5WeekMap = DateUtil.getBefore5WeekMap();
        Integer startDate = before5WeekMap.firstKey();
        Integer endDate = before5WeekMap.get(before5WeekMap.lastKey());

        List<LoginCountDay> list = loginCountDayCommonDao.execute(e -> e.select(Fields.start()
                .add(LOGIN_COUNT_DAY).end())
                .from(LOGIN_COUNT_DAY))
                .where(LOGIN_COUNT_DAY.DAY.ge(startDate).and(LOGIN_COUNT_DAY.DAY.le(endDate))).fetch().stream().map(r -> {
                    LoginCountDay log = new LoginCountDay();
                    log.setDay(r.getValue(LOGIN_COUNT_DAY.DAY));
                    log.setTime(String.valueOf(r.getValue(LOGIN_COUNT_DAY.DAY)));
                    buildLoginCountDay(r, log);
                    return log;
                }).collect(Collectors.toList());

        return list;
    }

    @Async
    @Scheduled(cron = "0 0 3 * * ? ")
    public void loginDistribution(){
        LOGGER.error("新数据大屏 -- 各人群登录情况 --- ");
        MemberLoginDistribution m = new MemberLoginDistribution();
        Map<String,MemberLoginDistribution> memberLoginDistributionMap = new HashMap<>();

        List<MemberLoginStatus> currentYearLoginMemberDetail = getCurrentYearLoginMemberDetail();
        //今年总登录人数
        Integer curYearLoginNum = currentYearLoginMemberDetail.size();
        //今年女登录人数
        Integer curYearFemaleNum = getFemaleNum(currentYearLoginMemberDetail);
        //今年男登录人数
        Integer curYearMaleNum = curYearLoginNum - curYearFemaleNum;
        //今年党员、预备党员人数
        long curYearPoliticalizationNum = currentYearLoginMemberDetail.stream().filter(x -> x.getPoliticalizationId() != null && (x.getPoliticalizationId().equals(YU_BEI_DANG_YUAN) || x.getPoliticalizationId().equals(GONG_CHAN_DANG_YUAN))).count();
        LOGGER.error("各人群登录情况 -- 查询截止至昨天 当前年登录总人数 ： {} ， 女登录人数 ： {}，男登录人数 ：{} ， 党员、预备党员人数：{}", curYearLoginNum, curYearFemaleNum, curYearMaleNum, curYearPoliticalizationNum);
        long endTime = DateUtil.lastTimestampPreviousDay(System.currentTimeMillis());
        AtomicInteger curYearLessThanThreeYears = new AtomicInteger();
        AtomicInteger curYearThreeToFiveYears = new AtomicInteger();
        AtomicInteger curYearSixToTenYears = new AtomicInteger();
        AtomicInteger curYearMoreThenTenYears = new AtomicInteger();

        countCurrentYearLoginMemberEntryDate(currentYearLoginMemberDetail, endTime, curYearLessThanThreeYears, curYearThreeToFiveYears, curYearSixToTenYears, curYearMoreThenTenYears);
        LOGGER.error("各人群登录情况 -- 查询截止至昨天 0 ~ 2年人数 ： {} ，  3 ~ 5年人数 ： {}， 6 ~ 10年人数 ：{}，大于10年人数 ：{}", curYearLessThanThreeYears.get(), curYearThreeToFiveYears.get(), curYearSixToTenYears.get(),curYearMoreThenTenYears.get());
        //系统所有用户
        List<Member> systemAllMembers = getSystemAllMember();
        Integer allMember = systemAllMembers.size();
        //系统所有女用户
        Integer allFemaleNum = systemAllMembers.stream().map(Member::getSex).reduce(0, Integer::sum);
        Integer allMaleNum = allMember - allFemaleNum;
        //系统所有党员、预备党员
        long allPoliticalizationNum = systemAllMembers.stream().filter(x -> x.getPoliticalizationId() != null && (x.getPoliticalizationId().equals(YU_BEI_DANG_YUAN) || x.getPoliticalizationId().equals(GONG_CHAN_DANG_YUAN))).count();
        LOGGER.error("各人群登录情况 -- 系统内部所有激活用户  ， 系统内部所有女激活用户 ： {}，系统内部所有男激活用户 ：{}， 党员、预备党员人数：{}", allMember, allFemaleNum, allMaleNum, allPoliticalizationNum);
        AtomicInteger allLessThanThreeYears = new AtomicInteger();
        AtomicInteger allThreeToFiveYears = new AtomicInteger();
        AtomicInteger allSixToTenYears = new AtomicInteger();
        AtomicInteger allMoreThenTenYears = new AtomicInteger();

        countAllMemberEntryDate(endTime, systemAllMembers, allLessThanThreeYears, allThreeToFiveYears, allSixToTenYears, allMoreThenTenYears);
        LOGGER.error("各人群登录情况 -- 系统内部所有 0 ~ 2年人数 ： {} ，  3 ~ 5年人数 ： {}， 6 ~ 10年人数 ：{}，大于10年人数 ：{}", allLessThanThreeYears.get(), allThreeToFiveYears.get(), allSixToTenYears.get(),allMoreThenTenYears.get());

        MemberLoginDistribution female = new MemberLoginDistribution();
        double femalePieCharRatio = buildMemberLoginDistribution(memberLoginDistributionMap, curYearLoginNum, allFemaleNum, curYearFemaleNum,female, "female");

        MemberLoginDistribution male = new MemberLoginDistribution();
        buildMemberLoginDistribution(memberLoginDistributionMap, curYearLoginNum, allMaleNum, curYearMaleNum, male, "male");
        male.setPieCharRatio(100 - femalePieCharRatio);

        MemberLoginDistribution lessThanThreeYearType = new MemberLoginDistribution();
        double lessThanThreeYearsLoginMemberPieCharRatio = buildMemberLoginDistribution(memberLoginDistributionMap, curYearLoginNum, allLessThanThreeYears.get(),curYearLessThanThreeYears.get(), lessThanThreeYearType,  "zeroToTwoYears");

        MemberLoginDistribution threeToFiveYeasType = new MemberLoginDistribution();
        double threeToFiveYeasLoginMemberPieCharRatio = buildMemberLoginDistribution(memberLoginDistributionMap, curYearLoginNum, allThreeToFiveYears.get(), curYearThreeToFiveYears.get(),threeToFiveYeasType, "threeToFiveYears");

        MemberLoginDistribution sixToTenYearsType = new MemberLoginDistribution();
        double sixToTenYearsLoginMemberPieCharRatio = buildMemberLoginDistribution(memberLoginDistributionMap, curYearLoginNum, allSixToTenYears.get(), curYearSixToTenYears.get(),sixToTenYearsType, "sixToTenYears");

        MemberLoginDistribution moreThanTenYearsType = new MemberLoginDistribution();
        buildMemberLoginDistribution(memberLoginDistributionMap, curYearLoginNum, allMoreThenTenYears.get(), curYearMoreThenTenYears.get(),moreThanTenYearsType, "moreThanTenYears");
        moreThanTenYearsType.setPieCharRatio(100 - lessThanThreeYearsLoginMemberPieCharRatio - threeToFiveYeasLoginMemberPieCharRatio - sixToTenYearsLoginMemberPieCharRatio);


        MemberLoginDistribution politicalOutlookType = new MemberLoginDistribution();
        double politicalizationLoginMemberPieCharRatio = buildMemberLoginDistribution(memberLoginDistributionMap, curYearLoginNum, (int) allPoliticalizationNum, (int) curYearPoliticalizationNum, politicalOutlookType, "politicalOutlook");

        MemberLoginDistribution other = new MemberLoginDistribution();
        other.setPieCharRatio(100 - politicalizationLoginMemberPieCharRatio);
        memberLoginDistributionMap.put( "other",other);

        redis.process(jedis -> {
            try {
                return jedis.set(LOGIN_DISTRIBUTION, new ObjectMapper().writeValueAsString(memberLoginDistributionMap));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    //计算各类别人群在饼状图中数据占比
    private double buildMemberLoginDistribution(Map<String, MemberLoginDistribution> memberLoginDistributionMap, Integer curYearLoginNum, Integer allTypeNum,Integer curYearTypeNum, MemberLoginDistribution m, String key) {
        // 截止至昨天为止，系统中登录的某一个类别的人数 转换成：万
        double femaleLoginMember = getLoginMember(curYearTypeNum.doubleValue(), 10000);
        //截止至昨天为止，系统中已激活内部用户有多少该类别的人数 转换成：万
        double femaleTotalMember = getLoginMember(allTypeNum.doubleValue(), 10000);
        //该类别人数在系统中所有这类别人数的占比
        double loginRatio = getLoginMemberRatio(curYearTypeNum.doubleValue(), allTypeNum);
        // 该类别人数在截止至昨天已登录人数中占比
        double pieCharRatio = getLoginMemberRatio(curYearTypeNum.doubleValue(), curYearLoginNum);
        m.setLoginMember(femaleLoginMember);
        m.setTotalMember(femaleTotalMember);
        m.setLoginRatio(loginRatio);
        m.setPieCharRatio(pieCharRatio);
        memberLoginDistributionMap.put(key, m);
        return pieCharRatio;
    }

    private Integer getFemaleNum(List<MemberLoginStatus> currentYearLoginMemberDetail) {
        return currentYearLoginMemberDetail.stream().map(MemberLoginStatus::getSex).reduce(0, Integer::sum);
    }

    private void countCurrentYearLoginMemberEntryDate(List<MemberLoginStatus> currentYearLoginMemberDetail, long endTime, AtomicInteger curYearLessThanThreeYears, AtomicInteger curYearThreeToFiveYears, AtomicInteger curYearSixToTenYears, AtomicInteger curYearMoreThenTenYears) {
        currentYearLoginMemberDetail.stream().filter(x -> x.getEntryDate() != null).forEach( member -> {
            Long realityEntryDate = endTime - member.getEntryDate();
            countEntryDateDistribution(curYearLessThanThreeYears, curYearThreeToFiveYears, curYearSixToTenYears, curYearMoreThenTenYears, realityEntryDate);
        });
    }

    private void countEntryDateDistribution(AtomicInteger lessThanThreeYears, AtomicInteger threeToFiveYears, AtomicInteger sixToTenYears, AtomicInteger moreThenTenYears, Long realityEntryDate) {
        if (realityEntryDate < THREE_YEAR) {
            lessThanThreeYears.incrementAndGet();
        } else if (THREE_YEAR <= realityEntryDate && realityEntryDate < SIX_YEAR) {
            threeToFiveYears.incrementAndGet();
        } else if (realityEntryDate >= SIX_YEAR && realityEntryDate < TEN_YEAR) {
            sixToTenYears.incrementAndGet();
        } else if (realityEntryDate >= TEN_YEAR) {
            moreThenTenYears.incrementAndGet();
        }
    }

    private void countAllMemberEntryDate(long endTime, List<Member> systemAllMembers, AtomicInteger allLessThanThreeYears, AtomicInteger allThreeToFiveYears, AtomicInteger allSixToTenYears, AtomicInteger allMoreThenTenYears) {
        systemAllMembers.stream().filter(x -> x.getEntryDate() != null).forEach( member -> {
            Long realityEntryDate = endTime - member.getEntryDate();
            countEntryDateDistribution(allLessThanThreeYears, allThreeToFiveYears, allSixToTenYears, allMoreThenTenYears, realityEntryDate);
        });
    }

    private double getLoginMember(double v, int i) {
        return i == 0 ? 0 :(new BigDecimal(String.valueOf(v / i)).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue());
    }

    private double getLoginMemberRatio(double v, int i) {
        return i == 0 ? 0 :(new BigDecimal(String.valueOf(v / i)).multiply(new BigDecimal(100).setScale(1, BigDecimal.ROUND_HALF_UP)).doubleValue());
    }

    private void insertOrganizationEntity(List<LoginLog> yesterdayLoginLogData, Integer day, Integer month, Integer year) {
        //循环70个省id 查询对应省活跃人数
        List<String> provinceList = new ArrayList<>(Arrays.asList(provinceIds.split(",")));

        //昨天登录人数中有多少是北京的， 用  startWith "1,1000001," 拼接 + 对应的省  有多少符合的就是人次 去重是人数
        provinceList.forEach(orgId -> {
            //各组织可用人数
            Integer activityProvinceNum = getActivityProvinceNum(orgId);
            //昨天每个组织的登陆人次
            long orgMemberLoginTime = yesterdayLoginLogData.stream().filter(x -> x.getPath().startsWith("1,10000001," + orgId)).count();
            //昨天每个组织的登陆人数
            long orgMemberLoginNum = yesterdayLoginLogData.stream().filter(distinctByKey(x -> x.getMemberId())).filter(x -> x.getPath().startsWith("1,10000001," + orgId)).count();

            OrganizationLoginCountDay entity = new OrganizationLoginCountDay();
            entity.forInsert();
            entity.setDay(day);
            entity.setMonth(month);
            entity.setYear(year);
            entity.setOrganizationId(orgId);
            entity.setOrganizationNum(activityProvinceNum);
            entity.setMemberNum( Long.valueOf(orgMemberLoginNum).intValue());
            entity.setMemberTime( Long.valueOf(orgMemberLoginTime).intValue());
            organizationLoginCountDayCommonDao.insert(entity);
        });
    }

    private void insertLoginCountDay(long startTime, long endTime, List<LoginLog> yesterdayLoginLogData, Integer systemCountNum, List<LoginLog> distinctMembers,Integer day,Integer month,Integer year) {
        //登录人次
        Integer memberTime = yesterdayLoginLogData.size();
        //登录人数
        Integer memberNum = distinctMembers.size();
        //女性登录人数
        int female = distinctMembers.stream().filter(x -> x.getSex() != null).mapToInt(LoginLog::getSex).sum();

        List<LoginLog> politicalizationLits = distinctMembers.stream().filter(x ->x.getPoliticalizationId() != null && ( x.getPoliticalizationId().equals(YU_BEI_DANG_YUAN) || x.getPoliticalizationId().equals(GONG_CHAN_DANG_YUAN))).collect(Collectors.toList());
        AtomicInteger lessThanThreeYeas = new AtomicInteger();
        AtomicInteger threeToFiveYears = new AtomicInteger();
        AtomicInteger sixToTenYears = new AtomicInteger();
        AtomicInteger moreThenTenYears = new AtomicInteger();
        distinctMembers.stream().filter(x -> x.getEntryDate() != null).forEach( member -> {
            Long realityEntryDate = endTime - member.getEntryDate();
            countEntryDateDistribution(lessThanThreeYeas, threeToFiveYears, sixToTenYears, moreThenTenYears, realityEntryDate);
        });
        LoginCountDay loginCountDay = new LoginCountDay();
        loginCountDay.forInsert();
        // 年 月 日
        loginCountDay.setDay(day);
        loginCountDay.setMonth(month);
        loginCountDay.setYear(year);
        //当前系统可用总人数 登录人数 登录人次
        loginCountDay.setMemberTime(memberTime);
        loginCountDay.setMemberNum(memberNum);
        loginCountDay.setSystemNum(systemCountNum);
        //男登录人数 女登录人数
        loginCountDay.setFemaleNum(female);
        loginCountDay.setMaleNum(memberNum - loginCountDay.getFemaleNum());
        //登录人数中是政治面貌(中共党员（含预备党员）)登录人数
        loginCountDay.setPoliticalOutlook(politicalizationLits.size());
        //登录人数中按入职年限统计对应人数
        loginCountDay.setEntryYearZeroTwo(lessThanThreeYeas.get());
        loginCountDay.setEntryYearThreeFive(threeToFiveYears.get());
        loginCountDay.setEntryYearSixTen(sixToTenYears.get());
        loginCountDay.setEntryYearTen(moreThenTenYears.get());

        loginCountDayCommonDao.insert(loginCountDay);
    }

    private List<LoginLog> getYesterdayLoginLogData(long startTime,long endTime) {

        return loginLogDao.execute(e -> e.select(Fields.start()
                .add(LOGIN_LOG.ID)
                .add(LOGIN_LOG.MEMBER_ID)
                .add(LOGIN_LOG.ORGANIZATION_ID)
                .add(MEMBER_DETAIL.POLITICALIZATION_ID)
                .add(MEMBER_DETAIL.ENTRY_DATE)
                .add(MEMBER.SEX)
                .add(ORGANIZATION.PATH).end())
                .from(LOGIN_LOG))
                .leftJoin(MEMBER).on(LOGIN_LOG.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(MEMBER_DETAIL).on(MEMBER.ID.eq(MEMBER_DETAIL.MEMBER_ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(LOGIN_LOG.CREATE_TIME.between(startTime).and(endTime)
                        .and(MEMBER.FROM.eq(1).and(MEMBER.STATUS.eq(1)))).fetch().stream().map(r -> {
                    LoginLog log = new LoginLog();
                    log.setId(r.get(LOGIN_LOG.ID));
                    log.setOrganizationId(r.getValue(LOGIN_LOG.ORGANIZATION_ID));
                    log.setMemberId(r.getValue(LOGIN_LOG.MEMBER_ID));
                    log.setSex(r.getValue(MEMBER.SEX));
                    log.setEntryDate(r.getValue(MEMBER_DETAIL.ENTRY_DATE));
                    log.setPoliticalizationId(r.getValue(MEMBER_DETAIL.POLITICALIZATION_ID));
                    log.setPath(r.getValue(ORGANIZATION.PATH));
                    return log;
                }).collect(Collectors.toList());
    }

    //查询当前系统所有内部用户
    private Integer getSystemCountNum(){
        Integer countMember = loginLogDao.execute(dao -> {
            return dao.fetchCount(
                    dao.select(Fields.start().add(MEMBER.ID).end())
                .from(MEMBER)
                .where(MEMBER.STATUS.eq(1)
                        .and(MEMBER.FROM.eq(1))));
    });
        return countMember;
    }

    private List<Member> getSystemAllMember() {
        return loginLogDao.execute(e -> e.select(Fields.start()
                .add(MEMBER_DETAIL.POLITICALIZATION_ID)
                .add(MEMBER_DETAIL.ENTRY_DATE)
                .add(MEMBER.SEX)
                .end())
                .from(MEMBER))
                .leftJoin(MEMBER_DETAIL).on(MEMBER.ID.eq(MEMBER_DETAIL.MEMBER_ID))
                .where(MEMBER.STATUS.eq(1)).fetch().stream().map(r -> {
                    Member m = new Member();
                    m.setEntryDate(r.getValue(MEMBER_DETAIL.ENTRY_DATE));
                    m.setPoliticalizationId(r.getValue(MEMBER_DETAIL.POLITICALIZATION_ID));
                    m.setSex(r.getValue(MEMBER.SEX));
                    return m;
                }).collect(Collectors.toList());
    }


    private Integer getActivityProvinceNum(String orgId){
        Integer countNum = loginLogDao.execute(dao -> {
            return dao.fetchCount(
                    dao.select(Fields.start().add(MEMBER.ID).end())
                            .from(MEMBER)
                            .leftJoin(ORGANIZATION_DETAIL).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB))
                            .where(MEMBER.STATUS.eq(1)
                                  .and(MEMBER.FROM.eq(1))
                                   .and(ORGANIZATION_DETAIL.ROOT.eq(orgId))));
        });
        return countNum;
    }
    private <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    /**
     *
     * @param time : yyyymmdd
     * @return  : m-d
     */
    private String month_day(String time) {
        time = time.substring(4);
        return String.format("%s.%s", Integer.parseInt(time.substring(0,2)), Integer.parseInt(time.substring(2)));
    }

    /**
     *
     * @param time1 : yyyymmdd  20191018
     * @param time2 : yyyymmdd
     * @return  : 月.日期-月.日期
     */
    private static String week(String time1,String time2){
        time1 = time1.substring(4);
        time2 = time2.substring(4);
        return String.format("%s.%s-%s.%s",time1.substring(0,2),time1.substring(2,4),time2.substring(0,2),time2.substring(2,4));
    }

    /**
     *
     * @param time : yyyymmdd
     * @return  : m月
     */
    private String month(String time){
        return String.format("%s月", Integer.parseInt(time.substring(4,6)));
    }
    @Override
    public void setEnvironment(Environment environment) {
       provinceIds  = environment.getProperty("organization.province.ids", "96493,1105092,1567690,1011203,98209,1093623,1061451,1073360,44261,1754249,1148390,80307,1093072,552512,567498,1180849,55836,1018307,105408,1073923,993478,1013550,1095592,1495996,1495590,1094794,1133482,1012970,1322085,1100282,1012774,1062701");
    }
}
