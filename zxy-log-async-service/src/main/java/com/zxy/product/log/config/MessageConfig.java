package com.zxy.product.log.config;

import com.zxy.common.message.CommonMessageConverter;
import com.zxy.common.message.consumer.MessageException;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.message.provider.MessageSenderFactory;
import com.zxy.common.serialize.Serializer;
import com.zxy.common.serialize.hessian.HessianSerializer;
import com.zxy.product.log.listener.*;
import com.zxy.product.log.listener.display.DauTrendsListener;
import com.zxy.product.log.listener.display.DisplayListener;
import com.zxy.product.log.listener.display.LoginRecordListener;
import com.zxy.product.log.listener.display.ResourceTop3Listener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.util.ErrorHandler;

import java.util.Arrays;
import com.zxy.common.message.consumer.MessageException;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.message.provider.MessageSenderFactory;

/**
 * Created by chengzhi on 17/8/10.
 */
@Configuration
public class MessageConfig implements EnvironmentAware, BeanFactoryAware {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageConfig.class);
    private String loginLogQueueName;
    private String auditLogQueueName;
    private String onlineInfoQueueName;
    private String userBehaviorQueueName;
    private String generateResourceVisitQueueName;
    private String automaticUpdateOnlineQueueName;
    private Integer prefetchCount;
    private Integer concurrentConsumers;
    private Integer maxConcurrentConsumers;
    private String envName;
    private DefaultListableBeanFactory beanFactory;

    private String loginRecordQueueName;
    private String resourceTop3QueueName;
    private String dauTrendQueueName;
    private String displayQueueName;

    private MessageSender messageSender;

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        if(beanFactory instanceof DefaultListableBeanFactory) {
            this.beanFactory = (DefaultListableBeanFactory) beanFactory;
        }
    }

    @Override
    public void setEnvironment(Environment environment) {
        loginLogQueueName = environment.getProperty("message.queue.login.log");
        auditLogQueueName = environment.getProperty("message.queue.audit.log");
        onlineInfoQueueName = environment.getProperty("message.queue.online.info");
        userBehaviorQueueName = environment.getProperty("message.queue.behavior.info");
        generateResourceVisitQueueName=environment.getProperty("message.queue.generate.vs.task");
        automaticUpdateOnlineQueueName=environment.getProperty("message.queue.automatic.online.task");
        prefetchCount = environment.getProperty("spring.rabbitmq.listener.simple.prefetch", Integer.class, 1);
        concurrentConsumers = environment.getProperty("spring.rabbitmq.listener.simple.concurrency", Integer.class, 1);
        maxConcurrentConsumers = environment.getProperty("spring.rabbitmq.listener.simple.max-concurrency", Integer.class, 1);
        envName = environment.getProperty("application.env.name", String.class, "dev9");
        loginRecordQueueName = environment.getProperty("message.queue.login.record.task");
        resourceTop3QueueName = environment.getProperty("message.queue.resource.top3.task");
        dauTrendQueueName = environment.getProperty("message.queue.dau.trend.task");
        displayQueueName = environment.getProperty("message.queue.data.display.task");

    }

    @Bean
    public Serializer serializer() {
        return new HessianSerializer();
    }

    @Bean
    public CommonMessageConverter commonMessageConverter(Serializer serializer) {
        CommonMessageConverter converter = new CommonMessageConverter();
        converter.setSerializer(serializer);
        return converter;
    }

    @Bean
    public MessageSenderFactory messageSenderFactory(){
        return new MessageSenderFactory();
    }

    @Bean
    public MessageSender messageSender(MessageSenderFactory messageSenderFactory, Environment env){
        messageSender = messageSenderFactory.create(env.getProperty("spring.rabbitmq.default-exchange"));
        return messageSender;
    }

    @Bean
    DirectExchange exchange(Environment env) {
        return new DirectExchange(env.getProperty("spring.rabbitmq.default-exchange"));
    }

    @Bean
    public SimpleMessageListenerContainer loginLogListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, LoginLogListener loginLogListener) {
        Queue queue = new Queue(loginLogQueueName,true);
        this.beanFactory.registerSingleton(loginLogQueueName, queue);
        Arrays.stream(loginLogListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(loginLogQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, loginLogListener, loginLogQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer auditLogListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, AuditLogListener auditLogListener) {
        Queue queue = new Queue(auditLogQueueName,true);
        this.beanFactory.registerSingleton(auditLogQueueName, queue);
        Arrays.stream(auditLogListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(auditLogQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, auditLogListener, auditLogQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer onlineInfoListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, UserOnlineInfoListener onlineInfoListener) {
        Queue queue = new Queue(onlineInfoQueueName,true);
        this.beanFactory.registerSingleton(onlineInfoQueueName, queue);
        Arrays.stream(onlineInfoListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(onlineInfoQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, onlineInfoListener, onlineInfoQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer dauTrendsListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, DauTrendsListener dauTrendsListener) {
        Queue queue = new Queue(dauTrendQueueName,true);
        this.beanFactory.registerSingleton(dauTrendQueueName, queue);
        Arrays.stream(dauTrendsListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(dauTrendQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, dauTrendsListener, dauTrendQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer resourceTop3ListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ResourceTop3Listener resourceTop3Listener) {
        Queue queue = new Queue(resourceTop3QueueName,true);
        this.beanFactory.registerSingleton(resourceTop3QueueName, queue);
        Arrays.stream(resourceTop3Listener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(resourceTop3QueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, resourceTop3Listener, resourceTop3QueueName);
    }

    @Bean
    public SimpleMessageListenerContainer loginRecordListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, LoginRecordListener loginRecordListener) {
        Queue queue = new Queue(loginRecordQueueName,true);
        this.beanFactory.registerSingleton(loginRecordQueueName, queue);
        Arrays.stream(loginRecordListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(loginRecordQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, loginRecordListener, loginRecordQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer displayListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, DisplayListener displayListener) {
        Queue queue = new Queue(displayQueueName,true);
        this.beanFactory.registerSingleton(displayQueueName, queue);
        Arrays.stream(displayListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(displayQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, displayListener, displayQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer userBehaviorListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, UserBehaviorListener userBehaviorListener) {
        Queue queue = new Queue(userBehaviorQueueName,true);
        this.beanFactory.registerSingleton(userBehaviorQueueName, queue);
        Arrays.stream(userBehaviorListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(userBehaviorQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, userBehaviorListener, userBehaviorQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer generateResourceVisitListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, GenerateResourceVisitListener generateResourceVisitListener) {
        Queue queue = new Queue(generateResourceVisitQueueName,true);
        this.beanFactory.registerSingleton(generateResourceVisitQueueName, queue);
        Arrays.stream(generateResourceVisitListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(generateResourceVisitQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, generateResourceVisitListener, generateResourceVisitQueueName);
    }

    @Bean
    public SimpleMessageListenerContainer automaticUpdateOnlineListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, AutomaticUpdateOnlineListener automaticUpdateOnlineListener) {
        Queue queue = new Queue(automaticUpdateOnlineQueueName,true);
        this.beanFactory.registerSingleton(automaticUpdateOnlineQueueName, queue);
        Arrays.stream(automaticUpdateOnlineListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(automaticUpdateOnlineQueueName + "#" + type, BindingBuilder.bind(queue).to(exchange).with(String.valueOf(type)));
        });
        return createListener(connectionFactory, automaticUpdateOnlineListener, automaticUpdateOnlineQueueName);
    }

    private SimpleMessageListenerContainer createListener(ConnectionFactory connectionFactory, MessageListener listener, String queue) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(queue);
        container.setMessageListener(listener);
        container.setPrefetchCount(prefetchCount);
        container.setConcurrentConsumers(concurrentConsumers);
        container.setMaxConcurrentConsumers(maxConcurrentConsumers);
        container.setErrorHandler(new ErrorHandler() {
            @Override
            public void handleError(Throwable throwable) {
                if (causeChainContainsARADRE(throwable)) {
                    StringBuilder errorMessage = new StringBuilder(envName);
                    errorMessage.append("环境异步监听服务出错: ");
                    errorMessage.append(throwable.getCause().getMessage());
                    LOGGER.error("message listener shutdown: " + throwable.getCause().getMessage());
                    // 发送邮件消息
//                    messageSender.send(MessageTypeContent.SEND_MESSAGE_WARNING_EMAIL, (Object)errorMessage.toString(),
//                            MessageHeaderContent.SUBJECT, "zxy-log project " + listener.getClass().getSimpleName() + "服务挂起");
                    // 停止监听
                    container.shutdown();
                }
            }
            private boolean causeChainContainsARADRE(Throwable t) {
                for(Throwable cause = t.getCause(); cause != null; cause = cause.getCause()) {
                    if(cause instanceof MessageException) {
                        return true;
                    }
                }
                return false;
            }
        });
        return container;
    }

//   class MessageErrorHandler implements ErrorHandler {
//
//       private SimpleMessageListenerContainer container;
//       private String emailSubject;
//
//       public MessageErrorHandler(SimpleMessageListenerContainer container, String emailSubject) {
//           this.container = container;
//           this.emailSubject = emailSubject;
//       }
//
//       @Override
//       public void handleError(Throwable throwable) {
//           if (causeChainContainsARADRE(throwable)) {
//               String errorMessage = throwable.getCause().getMessage();
//               LOGGER.error("message listener shutdown: " + throwable.getCause().getMessage());
//               // 发送邮件消息
//               messageSender.send(MessageTypeContent.SEND_MESSAGE_WARNING_EMAIL, (Object)errorMessage, MessageHeaderContent.SUBJECT, emailSubject);
//               // 停止监听
//               container.shutdown();
//           }
//       }
//
//       private boolean causeChainContainsARADRE(Throwable t) {
//           for(Throwable cause = t.getCause(); cause != null; cause = cause.getCause()) {
//               if(cause instanceof MessageException) {
//                   return true;
//               }
//           }
//           return false;
//       }
//   }

}
