package com.zxy.product.log.listener.display;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.content.MessageTypeContent;
import com.zxy.product.log.entity.CourseInfo;
import com.zxy.product.log.entity.HotResourceVisit;
import com.zxy.product.log.entity.ResourceVisit;
import com.zxy.product.log.util.DateUtil;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zxy.product.log.content.CacheKeyConstant.DISPLAY_MODULE;
import static com.zxy.product.log.content.CacheKeyConstant.ZXY_LOG;
import static com.zxy.product.log.jooq.Tables.COURSE_INFO;
import static com.zxy.product.log.jooq.Tables.HOT_RESOURCE_VISIT;
import static com.zxy.product.log.jooq.Tables.ORGANIZATION;
import static com.zxy.product.log.jooq.Tables.RESOURCE_VISIT;

/**
 * <AUTHOR> zhouyong
 */
@Component
public class ResourceTop3Listener extends AbstractMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResourceTop3Listener.class);

    private static final String RESOURCE_TOP3 = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.RESOURCE_TOP3);

    private Redis redis;

    private CommonDao<ResourceVisit> resourceVisitDao;

    private CommonDao<HotResourceVisit> hotResourceVisitDao;

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setResourceVisitDao(CommonDao<ResourceVisit> resourceVisitDao) {
        this.resourceVisitDao = resourceVisitDao;
    }

    @Autowired
    public void setHotResourceVisitDao(CommonDao<HotResourceVisit> hotResourceVisitDao) {
        this.hotResourceVisitDao = hotResourceVisitDao;
    }

    @Override
    protected void onMessage(Message message) {
        Map<String, List<HotResourceVisit>> top3 = new HashMap<>();
        Date d = new Date();
        Map<Integer, List<String>> typeMaps = this.queryShowCourseAndSubjectVisits()
            .stream().collect(Collectors.groupingBy(HotResourceVisit::getBusinessType, Collectors.mapping(HotResourceVisit::getBusinessId, Collectors.toList())));
        List<String> showCourseIds  = typeMaps.getOrDefault(HotResourceVisit.BUSINESS_TYPE_COURSE, new ArrayList<>());
        List<String> showSubjectIds = typeMaps.getOrDefault(HotResourceVisit.BUSINESS_TYPE_SUBJECT, new ArrayList<>());

        List<ResourceVisit> courseTop20 = this.resourceTop20(d, ResourceVisit.COURSE_TYPE);
        List<String> finalShowCourseIds = courseTop20.stream().map(ResourceVisit::getContentId).filter(showCourseIds::contains).collect(Collectors.toList());
        if (finalShowCourseIds.size() < 3) {
            finalShowCourseIds.addAll(courseTop20.stream().map(ResourceVisit::getContentId)
                .filter(x -> !finalShowCourseIds.contains(x)).limit(3 - finalShowCourseIds.size()).collect(Collectors.toList()));
        }
        List<HotResourceVisit> courseHotVisits = courseTop20.stream().map(x ->
            new HotResourceVisit(x.getContentId(), x.getContentName(), HotResourceVisit.BUSINESS_TYPE_COURSE, x.getOrganizationId(), x.getOrganizationName(),
                x.getVisit(), finalShowCourseIds.contains(x.getContentId()) ? HotResourceVisit.STATUS_SHOW : HotResourceVisit.STATUS_HIDE))
            .collect(Collectors.toList());

        List<ResourceVisit> subjectTop20 = this.resourceTop20(d, ResourceVisit.SUBJECT_TYPE);
        List<String> finalShowSubjectIds = subjectTop20.stream().map(ResourceVisit::getContentId).filter(showSubjectIds::contains).collect(Collectors.toList());
        if (finalShowSubjectIds.size() < 3) {
            finalShowSubjectIds.addAll(subjectTop20.stream().map(ResourceVisit::getContentId)
                .filter(x -> !finalShowSubjectIds.contains(x)).limit(3 - finalShowSubjectIds.size()).collect(Collectors.toList()));
        }
        List<HotResourceVisit> subjectHotVisits = subjectTop20.stream().map(x ->
            new HotResourceVisit(x.getContentId(), x.getContentName(), HotResourceVisit.BUSINESS_TYPE_SUBJECT, x.getOrganizationId(), x.getOrganizationName(),
                x.getVisit(), finalShowSubjectIds.contains(x.getContentId()) ? HotResourceVisit.STATUS_SHOW : HotResourceVisit.STATUS_HIDE))
            .collect(Collectors.toList());

        top3.put("course", courseHotVisits.stream().filter(x -> x.getStatus() == HotResourceVisit.STATUS_SHOW).collect(Collectors.toList()));
        top3.put("subject", subjectHotVisits.stream().filter(x -> x.getStatus() == HotResourceVisit.STATUS_SHOW).collect(Collectors.toList()));
        courseHotVisits.addAll(subjectHotVisits);

        hotResourceVisitDao.delete(HOT_RESOURCE_VISIT.BUSINESS_TYPE.in(HotResourceVisit.BUSINESS_TYPE_COURSE, HotResourceVisit.BUSINESS_TYPE_SUBJECT));
        hotResourceVisitDao.insert(courseHotVisits);

        redis.process(jedis -> {
            try {
                return jedis.set(RESOURCE_TOP3, new ObjectMapper().writeValueAsString(top3));
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            return null;
        });
    }

    private List<ResourceVisit> resourceTop20(Date d, int type) {
        Integer current = Integer.valueOf(DateUtil.format(d, DateUtil.DATE_PATTERN_NEW));
        Integer before30 = Integer.valueOf(DateUtil.format(DateUtil.offset(d, Calendar.DAY_OF_MONTH, -30), DateUtil.DATE_PATTERN_NEW));
        return resourceVisitDao.execute(ctx ->
            ctx.select(RESOURCE_VISIT.CONTENT_ID, RESOURCE_VISIT.CONTENT_NAME, RESOURCE_VISIT.VISIT.sum().as("visit"), COURSE_INFO.ORGANIZATION_ID, ORGANIZATION.NAME)
                .from(RESOURCE_VISIT)
                .leftJoin(COURSE_INFO).on(RESOURCE_VISIT.CONTENT_ID.eq(COURSE_INFO.ID))
                .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(RESOURCE_VISIT.CONTENT_TYPE.eq(type).and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_PUBLISHED)).and(RESOURCE_VISIT.DAY.ge(before30)).and(RESOURCE_VISIT.DAY.lt(current)))
                .groupBy(RESOURCE_VISIT.CONTENT_ID)
                .orderBy(DSL.field("visit", Integer.class).desc())
                .limit(20)
                .fetch(r -> new ResourceVisit(
                    r.get(0, String.class),
                    r.get(1, String.class),
                    r.get(2, Integer.class),
                    r.get(3, String.class),
                    r.get(4, String.class))
                )
        );
    }

    private List<HotResourceVisit> queryShowCourseAndSubjectVisits() {
        return hotResourceVisitDao.execute(ctx -> ctx.select(HOT_RESOURCE_VISIT.fields())
            .from(HOT_RESOURCE_VISIT)
            .where(HOT_RESOURCE_VISIT.STATUS.eq(HotResourceVisit.STATUS_SHOW)
                .and(HOT_RESOURCE_VISIT.BUSINESS_TYPE.in(HotResourceVisit.BUSINESS_TYPE_COURSE, HotResourceVisit.BUSINESS_TYPE_SUBJECT)))
            .fetchInto(HotResourceVisit.class)
        );
    }

    @Override
    public int[] getTypes() {
        return new int[]{MessageTypeContent.RESOURCE_TOP3_TASK_MESSAGE};
    }
}
