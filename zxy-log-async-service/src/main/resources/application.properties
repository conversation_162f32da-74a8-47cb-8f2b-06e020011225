spring.application.name=zxy-log-async-service
application.env.name=dev9
spring.datasource.url=jdbc:mysql://**************:3306/zxy-log
spring.datasource.username=root
spring.datasource.password=dreamtech%IT
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.validation-query=SELECT 1
spring.datasource.initial-size=5
spring.datasource.max-active=10
spring.datasource.max-idle=5
spring.datasource.min-idle=1
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=true
spring.datasource.time-between-eviction-runs-millis=5000
spring.datasource.min-evictable-idle-time-millis=60000


spring.datasource.tomcat.test-while-idle=true
spring.datasource.tomcat.test-on-borrow=true
spring.datasource.tomcat.time-between-eviction-runs-millis=5000
spring.datasource.tomcat.min-evictable-idle-time-millis=60000
spring.datasource.tomcat.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.tomcat.validation-query=SELECT 1


spring.jooq.sql-dialect = mysql

#spring.data.mongodb.uri=mongodb://127.0.0.1:27017/test
# cmudev9
#spring.data.mongodb.host = ************** 
#spring.data.mongodb.port = 30005
# cmutest9
spring.data.mongodb.host = **************
spring.data.mongodb.port = 30005
spring.data.mongodb.dbname = zxy_online
spring.data.mongodb.connectTimeout = 60000
spring.data.mongodb.socketTimeout = 120000
spring.data.mongodb.socketKeepAlive = true
spring.data.mongodb.maxConnectionIdleTime = 120000
spring.data.mongodb.maxConnectionLifeTime = 120000
spring.data.mongodb.connectionsPerHost = 100
spring.data.mongodb.username = cmutest
spring.data.mongodb.password = asd123


logging.level.org.jooq=ERROR

dubbo.application.name=zxy-log-async-service
dubbo.application.version=1
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator

spring.rabbitmq.host=**************
spring.rabbitmq.port=30007
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

# restTemplate
resttemplate.charset = UTF-8
resttemplate.timeout.read = 60000
resttemplate.timeout.connect = 60000
resttemplate.header.authorization = #@&^%#!@1

# key-url
key.member = https://dev9.zhixueyun.com/api/v1/human/member/get/
key.position = https://dev9.zhixueyun.com/api/v1/human/position/
key.job = https://dev9.zhixueyun.com/api/v1/human/job/
key.job-type = https://dev9.zhixueyun.com/api/v1/human/job-type/
key.tag = https://dev9.zhixueyun.com/api/v1/human/tag/
key.class-info = https://dev9.zhixueyun.com/api/v1/train/class-info/
key.configuration = https://dev9.zhixueyun.com/api/v1/train/configuration/
key.class-required-theme = http://dev9.zhixueyun.com/api/v1/train/class-required-theme/
key.project = https://dev9.zhixueyun.com/api/v1/train/project/

key.course-info = https://dev9.zhixueyun.com/api/v1/course-study/course-info/audit/
key.study-push = https://dev9.zhixueyun.com/api/v1/course-study/study-push/audit/
key.study-push-recorde = https://dev9.zhixueyun.com/api/v1/course-study/study-push/push-recorde/
key.course-progress = https://dev9.zhixueyun.com/api/v1/course-study/course-study-progress/course-info-by-progress/

key.exam = https://dev9.zhixueyun.com/api/v1/exam/exam/audit/
key.exam-sign-up-name=https://dev9.zhixueyun.com/api/v1/exam/sign-up/name/
key.exam-invigilator-name=https://dev9.zhixueyun.com/api/v1/exam/invigilator/name/
key.exam-record-name=https://dev9.zhixueyun.com/api/v1/exam/exam-record/name/
key.exam-question-depot=https://dev9.zhixueyun.com/api/v1/exam/question-depot/
key.exam-question=https://dev9.zhixueyun.com/api/v1/exam/question/audit/
key.exam-detail=https://dev9.zhixueyun.com/api/v1/exam/exam/audit/
key.research-detail = https://dev9.zhixueyun.com/api/v1/exam/research-activity/simple-data/
key.exam-paper-name=https://dev9.zhixueyun.com/api/v1/exam/paper-class/name/


# name of queues
message.queue.login.log = zxy-login-log-queue
message.queue.audit.log = zxy-audit-log-queue
message.queue.online.info = zxy-user-online-info-queue
message.queue.behavior.info = zxy-user-behavior-info-queue
message.queue.generate.vs.task=zxy-log-resource-visit-generate-task-queue
message.queue.automatic.online.task=zxy-log-resource-automatic-online-task-queue
message.queue.login.record.task=zxy-log-display-login-record-task-queue
message.queue.resource.top3.task=zxy-log-display-resource-top3-task-queue
message.queue.dau.trend.task=zxy-log-display-dau-trend-task-queue
message.queue.data.display.task=zxy-log-display-data-display-task-queue


# redis
spring.redis.cluster = false
spring.redis.cluster.nodes = localhost:6379
spring.redis.timeout=10000
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1
