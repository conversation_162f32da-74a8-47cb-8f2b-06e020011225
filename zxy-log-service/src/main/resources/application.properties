spring.application.name=zxy-log-service

spring.datasource.url=***********************************************************
spring.datasource.username=root
spring.datasource.password=dreamtech%IT
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.validation-query=SELECT 1
spring.datasource.initial-size=5
spring.datasource.max-active=10
spring.datasource.max-idle=5
spring.datasource.min-idle=1
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=true
spring.datasource.time-between-eviction-runs-millis=5000
spring.datasource.min-evictable-idle-time-millis=60000

spring.datasource.tomcat.test-while-idle=true
spring.datasource.tomcat.test-on-borrow=true
spring.datasource.tomcat.time-between-eviction-runs-millis=5000
spring.datasource.tomcat.min-evictable-idle-time-millis=60000
spring.datasource.tomcat.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.tomcat.validation-query=SELECT 1


#spring.data.mongodb.uri=mongodb://127.0.0.1:27017/test
# cmudev9
#spring.data.mongodb.host = ************** 
#spring.data.mongodb.port = 30005
# cmutest9
spring.data.mongodb.host = **************
spring.data.mongodb.port = 30005
spring.data.mongodb.dbname = zxy_online
spring.data.mongodb.connectTimeout = 60000
spring.data.mongodb.socketTimeout = 120000
spring.data.mongodb.socketKeepAlive = true
spring.data.mongodb.maxConnectionIdleTime = 120000
spring.data.mongodb.maxConnectionLifeTime = 120000
spring.data.mongodb.username = cmutest
spring.data.mongodb.password = asd123

spring.jooq.sql-dialect = mysql
logging.level.org.jooq=DEBUG

dubbo.application.name=zxy-log-service
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.application.version=1
dubbo.protocol.port = 20887
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator


spring.rabbitmq.host=**************
spring.rabbitmq.port=30007
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/mis
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1


graphite.server=**************
graphite.port=30004


# redis
spring.redis.cluster = false
spring.redis.cluster.nodes = localhost:6379
spring.redis.timeout=10000
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3


# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1