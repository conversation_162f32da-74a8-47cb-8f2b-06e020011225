package com.zxy.product.log.service.support;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.log.api.WhiteDetailService;
import com.zxy.product.log.entity.LoginLog;
import com.zxy.product.log.entity.WhiteRecord;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.zxy.product.log.jooq.Tables.WHITE_RECORD;
import static com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG;


/**
 * 作者 冯春 创建于 2019/8/7.
 */
@Service
public class WhiteDetailServiceSupport implements WhiteDetailService {
    private CommonDao<LoginLog> commonDao;
    private CommonDao<WhiteRecord> whiteRecordCommonDao;

    @Autowired
    public void setWhiteRecordCommonDao(CommonDao<WhiteRecord> whiteRecordCommonDao) {
        this.whiteRecordCommonDao = whiteRecordCommonDao;
    }

    @Autowired
    public void setCommonDao(CommonDao<LoginLog> commonDao) {
        this.commonDao = commonDao;
    }
    public long timeToStamp(String timers) {
        Date d = new Date();
        long timeStemp = 0;
        try {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        d = sf.parse(timers);// 日期转换为时间戳
        } catch (ParseException e) {
            e.printStackTrace();
        }
        timeStemp = d.getTime();
        return timeStemp;
    }
    @Override
    public PagedResult<WhiteRecord> find(Integer page, Integer pageSize, String memberId, Optional<String> time, Integer status) {
//        List<WhiteRecord> loginLogList =  new ArrayList<WhiteRecord>();
        List<WhiteRecord> whiteRecordList =  new ArrayList<WhiteRecord>();
        Integer count = 0;
        String logTime = "1=1";
        String whiteTime = "1=1";
        String logStart = "1=1";
        String whiteEnd = "1=1";
        if (time.isPresent()&&!time.get().trim().equals("to")){
            String t[] = time.get().split("to");
            Long start = this.timeToStamp(t[0].trim());
            Long end = this.timeToStamp(t[1].trim()) + 86400000;
            logTime = "t_login_log.f_create_time>"+start+"";
            logStart = "t_login_log.f_create_time<"+end+"";
            whiteTime = "t_white_record.f_create_time>"+start+"";
            whiteEnd = "t_white_record.f_create_time<"+end+"";
        }
        String finalLog = logTime;
        String finalWhite = whiteTime;
        String finalLongStart = logStart;
        String finalwhiteEnd = whiteEnd;
        if(status==1) {
            SelectOrderByStep<Record> step = commonDao.execute(x -> x
                    .selectDistinct(Fields.start().add(LOGIN_LOG.TERMINAL_TYPE, LOGIN_LOG.CREATE_TIME, LOGIN_LOG.SOURCE, LOGIN_LOG.PASSWORD_TYPE)
                            .end()).from(LOGIN_LOG).where(LOGIN_LOG.MEMBER_ID.eq(memberId).and(finalLog).and(finalLongStart)));
            count = commonDao.execute(e -> e.fetchCount(step));
            whiteRecordList =  step.orderBy(LOGIN_LOG.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch(r -> {
                        WhiteRecord w = new WhiteRecord();
                        w.setCreateTime(r.getValue(LOGIN_LOG.CREATE_TIME));
                        w.setType(r.getValue(LOGIN_LOG.TERMINAL_TYPE));
                        w.setSource(r.getValue(LOGIN_LOG.SOURCE));
                        w.setPasswordType(r.getValue(LOGIN_LOG.PASSWORD_TYPE));
                        w.setLoginStatus("登录成功");
                        return w;
                    });
        }else if(status==2){
                    SelectOrderByStep<Record> step = whiteRecordCommonDao.execute(x->x.selectDistinct(Fields.start()
                    .add(WHITE_RECORD.TYPE,WHITE_RECORD.CREATE_TIME,WHITE_RECORD.SOURCE,WHITE_RECORD.PASSWORD_TYPE,WHITE_RECORD.ERROR).end()).from(WHITE_RECORD).where(WHITE_RECORD.MEMBER_ID.eq(memberId).and(finalWhite).and(finalwhiteEnd)));
            count = whiteRecordCommonDao.execute(e -> e.fetchCount(step));
            whiteRecordList = step.orderBy(WHITE_RECORD.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch(r->{
                WhiteRecord w = r.into(WhiteRecord.class);
                w.setLoginStatus("登录失败");
                return w;
            });
        }else{
            SelectOrderByStep<Record> step = commonDao.execute(x -> x
                    .selectDistinct(Fields.start().add(LOGIN_LOG.TERMINAL_TYPE, LOGIN_LOG.CREATE_TIME, LOGIN_LOG.SOURCE, LOGIN_LOG.PASSWORD_TYPE,LOGIN_LOG.MEMBER_ID)
                            .end()).from(LOGIN_LOG).where(LOGIN_LOG.MEMBER_ID.eq(memberId).and(finalLog).and(finalLongStart)));
            step.unionAll(whiteRecordCommonDao.execute(x->x.selectDistinct(Fields.start()
                    .add(WHITE_RECORD.TYPE,WHITE_RECORD.CREATE_TIME,WHITE_RECORD.SOURCE,WHITE_RECORD.PASSWORD_TYPE,WHITE_RECORD.ERROR).end()).from(WHITE_RECORD).where(WHITE_RECORD.MEMBER_ID.eq(memberId).and(finalWhite).and(finalwhiteEnd))));
            count = commonDao.execute(e -> e.fetchCount(step));
            whiteRecordList =  step.orderBy(DSL.val(2).desc()).limit((page - 1) * pageSize, pageSize).fetch(r->{
                WhiteRecord w = new WhiteRecord();
                w.setId(java.util.UUID.randomUUID().toString()  );
                w.setCreateTime(r.getValue(LOGIN_LOG.CREATE_TIME));
                w.setType(r.getValue(LOGIN_LOG.TERMINAL_TYPE));
                w.setSource(r.getValue(LOGIN_LOG.SOURCE));
                w.setPasswordType(r.getValue(LOGIN_LOG.PASSWORD_TYPE));
                 if(r.getValue(LOGIN_LOG.MEMBER_ID)!=null&&memberId.equals(r.getValue(LOGIN_LOG.MEMBER_ID))){
                     w.setLoginStatus("登录成功");
                }else{
                     w.setError(r.getValue(LOGIN_LOG.MEMBER_ID));
                     w.setLoginStatus("登录失败");
                }
                return w;
            });
        }
        return PagedResult.create(count, whiteRecordList);
    }

}
