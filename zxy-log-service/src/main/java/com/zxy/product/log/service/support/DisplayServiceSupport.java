package com.zxy.product.log.service.support;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.log.api.DisplayService;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.entity.DailyActiveUser;
import com.zxy.product.log.entity.LoginCountDay;
import com.zxy.product.log.entity.LoginLog;
import com.zxy.product.log.entity.LoginRecord;
import com.zxy.product.log.entity.MemberLoginDistribution;
import com.zxy.product.log.entity.ResourceVisit;
import com.zxy.product.log.service.util.DateUtil;
import com.zxy.product.system.content.MessageHeaderContent;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.zxy.product.log.content.CacheKeyConstant.DISPLAY_MODULE;
import static com.zxy.product.log.content.CacheKeyConstant.ZXY_LOG;
import static com.zxy.product.log.content.MessageTypeContent.BUILT_RESOURCE_TASK_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.DAU_TRENDS_TASK_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.LOGIN_ACTIVITY_DISTRIBUTION_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.LOGIN_ACTIVITY_VIEW_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.LOGIN_COUNT_TASK_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.RESOURCE_TOP3_TASK_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.STUDY_ACTIVITY_TASK_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.STUDY_DATA_DAY30_TASK_MESSAGE;
import static com.zxy.product.log.content.MessageTypeContent.STUDY_TIME_DAY14_TASK_MESSAGE;
import static com.zxy.product.log.jooq.Tables.LOGIN_LOG;
import static com.zxy.product.log.jooq.Tables.MEMBER;
import static com.zxy.product.log.jooq.Tables.RESOURCE_VISIT;
import static com.zxy.product.system.content.MessageTypeContent.TOP20_TOPIC_TASK_MESSAGE;


/**
 * <AUTHOR> zhouyong
 */
@Service
public class DisplayServiceSupport implements DisplayService {

    private Logger log = LoggerFactory.getLogger(DisplayServiceSupport.class);

    private static final String LOGIN_COUNT = String.join("#", ZXY_LOG, DISPLAY_MODULE, CacheKeyConstant.LOGIN_COUNT);

    private static final String DAU_TRENDS  = String.join("#", ZXY_LOG, DISPLAY_MODULE, CacheKeyConstant.DAU_TRENDS );

    private static final String RESOURCE_TOP3 = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.RESOURCE_TOP3);

    private static final String REAL_TIME_LOGIN_USER = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.REAL_TIME_LOGIN_USER);

    private static final String LOGIN_DISTRIBUTION = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.LOGIN_DISTRIBUTION);

    private static final String LOGIN_ACTIVITY_VIEW = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.LOGIN_ACTIVITY_VIEW);

    private static final String LOGIN_COUNT_LOCK = "login-count-lock";

    private Redis redis;

    private CommonDao<LoginLog> loginLogDao;

    private MessageSender messageSender;

    //热门学习 -- LJY -- 2022/05/17
    private CommonDao<ResourceVisit> resourceVisitDao;

    private CommonDao<LoginCountDay> loginCountDayCommonDao;

    @Autowired
    public void setResourceVisitDao(CommonDao<ResourceVisit> resourceVisitDao) {
        this.resourceVisitDao = resourceVisitDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setLoginLogDao(CommonDao<LoginLog> loginLogDao) {
        this.loginLogDao = loginLogDao;
    }

    @Autowired
    public void setLoginCountDayCommonDao(CommonDao<LoginCountDay> loginCountDayCommonDao) {
        this.loginCountDayCommonDao = loginCountDayCommonDao;
    }

    public String taskScheduler(String type) {
        switch (type){
            case "1":
                messageSender.send(LOGIN_COUNT_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                break;
            case "2":
                messageSender.send(DAU_TRENDS_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                break;
            case "3":
                messageSender.send(TOP20_TOPIC_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                break;
            case "4":
                messageSender.send(STUDY_DATA_DAY30_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                break;
            case "5":
                messageSender.send(STUDY_TIME_DAY14_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                break;
            case "6":
                messageSender.send(STUDY_ACTIVITY_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                break;
            case "7":
                messageSender.send(BUILT_RESOURCE_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                break;
            case "8":
                messageSender.send(RESOURCE_TOP3_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                break;
            case "9":
                //新数据大屏 登陆活跃状况手动调用
                redis.process(x -> x.del("zxy-log-async-server#login-data#" + LOGIN_COUNT_LOCK));
                log.error(" 手动触发数据大屏 - 登录活跃情况 " );
                messageSender.send(LOGIN_ACTIVITY_VIEW_MESSAGE,MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                break;
            case "10":
                //新数据大屏 人群登录分布手动调用
                messageSender.send(LOGIN_ACTIVITY_DISTRIBUTION_MESSAGE,MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                break;
            case "0":
                messageSender.send(LOGIN_COUNT_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                messageSender.send(DAU_TRENDS_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                messageSender.send(RESOURCE_TOP3_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                messageSender.send(TOP20_TOPIC_TASK_MESSAGE, MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                messageSender.send(STUDY_DATA_DAY30_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                messageSender.send(STUDY_TIME_DAY14_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                messageSender.send(STUDY_ACTIVITY_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                messageSender.send(BUILT_RESOURCE_TASK_MESSAGE,MessageHeaderContent.SYSTEM_TIME,String.valueOf(System.currentTimeMillis()));
                messageSender.send(LOGIN_ACTIVITY_VIEW_MESSAGE,MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                messageSender.send(LOGIN_ACTIVITY_DISTRIBUTION_MESSAGE,MessageHeaderContent.SYSTEM_TIME, String.valueOf(System.currentTimeMillis()));
                break;
            default:
                break;
        }
        return "success";
    }


    @Override
    public LoginRecord loginCount() {
        LoginRecord loginRecord = Optional.ofNullable(redis.process(jedis -> jedis.get(LOGIN_COUNT))).map(json -> JSONObject.parseObject(json, LoginRecord.class)).orElse(new LoginRecord());
        //统计当日登录人次数据
        Long todayVisit = loginLogDao.execute(ctx ->
            ctx.select(LOGIN_LOG.ID.count().as("visit"))
                .from(LOGIN_LOG).leftJoin(MEMBER).on(LOGIN_LOG.MEMBER_ID.eq(MEMBER.ID))
                .where(MEMBER.STATUS.eq(1).and(LOGIN_LOG.CREATE_TIME.between(DateUtil.getTodayZeroTimeStamp(), System.currentTimeMillis())))
                .fetchOne(r -> Optional.ofNullable(r.get(0, Long.class)).orElse(0L)));
        loginRecord.setTotalVisit(loginRecord.getTotalVisit() + todayVisit);
        loginRecord.setTodayVisit(todayVisit);
        return loginRecord;
    }

    @Override
    public LoginRecord getLoginUserCounts() {
        LoginRecord record = new LoginRecord();
        loginLogDao.execute(ctx ->
                ctx.select(LOGIN_LOG.ID.count().as("visit"),LOGIN_LOG.MEMBER_ID.countDistinct().as("number"))
                        .from(LOGIN_LOG).leftJoin(MEMBER).on(LOGIN_LOG.MEMBER_ID.eq(MEMBER.ID))
                        .where(MEMBER.STATUS.eq(1)
                                .and(LOGIN_LOG.CREATE_TIME.between(DateUtil.getTodayZeroTimeStamp(),System.currentTimeMillis())))
                        .fetchOne(r -> {
                            record.setTotalVisit(Optional.ofNullable(r.get(0, Long.class)).orElse(0L));
                            record.setNumber(Optional.ofNullable(r.get(1, Long.class)).orElse(0L));
                            return null;
                        }));
        log.error(" 数据大屏  获取今天目前为止在线人数 ：{} , 在线人次 ：{}  当前时间 ： {}" ,record.getNumber(),record.getTotalVisit(), System.currentTimeMillis() );

        redis.process(jedis -> jedis.set(REAL_TIME_LOGIN_USER, JSONObject.toJSONString(record)));
        redis.expire(REAL_TIME_LOGIN_USER,10);
        return record;
    }

    @Override
    public Map<String, MemberLoginDistribution> getMemberLoginDistribution() {
        return Optional.ofNullable(redis.process(jedis -> jedis.get(LOGIN_DISTRIBUTION)))
                .map(data -> {
                    try {
                        return new ObjectMapper().readValue(data, new TypeReference<Map<String, MemberLoginDistribution>>() {});
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return new HashMap<String, MemberLoginDistribution>();
                })
                .orElse(new HashMap<>());
    }

    @Override
    public List<DailyActiveUser> dauTrends() {
        if (!redis.process(jedis -> jedis.exists(DAU_TRENDS))) {
            List<Long> days = new ArrayList<>();
            Long dayNum = DateUtil.format2EpochDay(DateUtil.format(new Date(DateUtil.before7Timestamp()),DateUtil.DATE_PATTERN_NEW));
            IntStream.range(0, 7).forEach(i -> days.add(dayNum + i));
            return days.stream()
                .map(time -> new DailyActiveUser(DateUtil.dateFormat(time).toString()))
                .sorted()
                .peek(dau -> dau.setTime(this.month_day(dau.getTime())))
                .collect(Collectors.toList());
        }
        String dauList = redis.process(jedis -> jedis.get(DAU_TRENDS));
        try {
            return  new ObjectMapper().readValue(dauList, new TypeReference<List<DailyActiveUser>>() {});
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    @Override
    public Map<String, List<ResourceVisit>> resourceTop3() {
        return Optional.ofNullable(redis.process(jedis -> jedis.get(RESOURCE_TOP3)))
            .map(data -> {
                try {
                    return new ObjectMapper().readValue(data, new TypeReference<Map<String, List<ResourceVisit>>>() {});
                } catch (IOException e) {
                    e.printStackTrace();
                }
                return new HashMap<String, List<ResourceVisit>>();
            })
            .orElse(new HashMap<>());
    }

    //热门学习 -- LJY -- 2022/05/17
    @Override
//    public List<ResourceVisit> resourceTop10(Date date, int type, int page, int pageSize) {
    public List<ResourceVisit> resourceTop10(Date date,  int page, int pageSize) {

        int firstResult = (page - 1) * pageSize;
        Integer current = Integer.valueOf(DateUtil.format(date, DateUtil.DATE_PATTERN_NEW));
        Integer before30 = Integer.valueOf(DateUtil.format(DateUtil.offset(date, Calendar.DAY_OF_MONTH, -30), DateUtil.DATE_PATTERN_NEW));
        return resourceVisitDao.execute(ctx ->
                ctx.select(RESOURCE_VISIT.CONTENT_TYPE, RESOURCE_VISIT.CONTENT_ID,RESOURCE_VISIT.VISIT.sum().as("visit"))
                        .from(RESOURCE_VISIT)
//                        .where(RESOURCE_VISIT.CONTENT_TYPE.eq(type).and(RESOURCE_VISIT.DAY.ge(before30)).and(RESOURCE_VISIT.DAY.lt(current)))
                        .where(RESOURCE_VISIT.DAY.ge(before30).and(RESOURCE_VISIT.DAY.lt(current)))
                        .groupBy(RESOURCE_VISIT.CONTENT_ID)
                        .orderBy(DSL.field("visit", Integer.class).desc())
                        .limit(firstResult, pageSize)
                        .fetch(r -> new ResourceVisit(r.get(0, Integer.class),r.get(1, String.class), r.get(2, Integer.class)))
        );
    }

    @Override
    public LoginRecord getLoginMemberCounts() {
        return Optional.ofNullable(redis.process(jedis -> jedis.get(REAL_TIME_LOGIN_USER))).map(json -> JSONObject.parseObject(json, LoginRecord.class)).orElse(getLoginUserCounts());
    }

    @Override
    public Map<String, List<LoginCountDay>> getLoginActivityView() {
        return Optional.ofNullable(redis.process(jedis -> jedis.get(LOGIN_ACTIVITY_VIEW)))
                .map(data -> {
                    try {
                        return new ObjectMapper().readValue(data, new TypeReference<Map<String, List<LoginCountDay>>>() {});
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                    return new HashMap<String, List<LoginCountDay>>();
                })
                .orElse(new HashMap<>());
    }


    /**
     *
     * @param time : yyyymmdd
     * @return  : m-dd
     */
    private String month_day(String time) {
        time = time.substring(4);
        return String.format("%s.%s", Integer.parseInt(time.substring(0,2)), time.substring(2));
    }

}
