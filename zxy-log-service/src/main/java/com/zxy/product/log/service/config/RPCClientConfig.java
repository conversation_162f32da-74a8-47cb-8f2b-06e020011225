package com.zxy.product.log.service.config;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.MonitorConfig;
import com.alibaba.dubbo.config.ProtocolConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import com.zxy.common.rpc.spring.DubboReferenceAutowireCandidateResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class RPCClientConfig {
    public RPCClientConfig() {
    }

    @Bean
    public DubboReferenceAutowireCandidateResolver dubboReferenceAutowireCandidateResolver(Environment env) {
        DubboReferenceAutowireCandidateResolver resolver = new DubboReferenceAutowireCandidateResolver();
        resolver.setVersion((Integer)env.getProperty("dubbo.application.version", Integer.TYPE, 1));
        resolver.setRetries((Integer)env.getProperty("dubbo.application.retries", Integer.TYPE, 0));
        resolver.setTimeout((Integer)env.getProperty("dubbo.application.timeout", Integer.TYPE, 0));
        return resolver;
    }

    @Bean
    public ApplicationConfig applicationConfig(Environment env) {
        ApplicationConfig config = new ApplicationConfig();
        config.setName(env.getProperty("dubbo.application.name"));
        return config;
    }

    @Bean
    public ProtocolConfig protocolConfig(Environment env) {
        ProtocolConfig config = new ProtocolConfig();
        config.setName("dubbo");
        config.setSerialization("hessian3");
        config.setThreads((Integer)env.getProperty("dubbo.protocol.threads", Integer.class, 200));
        config.setPort((Integer)env.getProperty("dubbo.protocol.port", Integer.TYPE, 20880));
        return config;
    }

    @Bean
    public RegistryConfig registryConfig(Environment environment) {
        RegistryConfig registryConfig = new RegistryConfig();
        registryConfig.setAddress(environment.getProperty("dubbo.registry.address"));
        registryConfig.setUsername(environment.getProperty("dubbo.registry.username",String.class,null));
        registryConfig.setPassword(environment.getProperty("dubbo.registry.password",String.class,null));
        registryConfig.setClient(environment.getProperty("dubbo.registry.client",String.class,null));
        return registryConfig;
    }

    @Bean
    public MonitorConfig monitorConfig() {
        MonitorConfig monitorConfig = new MonitorConfig();
        monitorConfig.setProtocol("registry");
        return monitorConfig;
    }
}

