package com.zxy.product.log.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.log.api.LoginCountDayService;
import com.zxy.product.log.entity.LoginLog;
import com.zxy.product.log.entity.OrganizationLoginCountDay;
import com.zxy.product.log.service.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.zxy.product.log.jooq.Tables.*;

@Service
public class LoginCountDayServiceSupport implements LoginCountDayService {

    private CommonDao<OrganizationLoginCountDay> orgDao;


    @Autowired
    public void setOrgDao(CommonDao<OrganizationLoginCountDay> orgDao) {
        this.orgDao = orgDao;
    }

    @Override
    public List<OrganizationLoginCountDay> getLoginRecordGroupByOrdId() {
        long lastTimestampYesterday = DateUtil.lastTimestampYesterday();
        Integer currentYear = DateUtil.getYear(lastTimestampYesterday);
        return orgDao.execute(e -> e.select(Fields.start()
                        .add(ORGANIZATION_LOGIN_COUNT_DAY.ID)
                        .add(ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID)
                        .add(ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_NUM.sum())
                        .add(ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_TIME.sum()).end())
                .from(ORGANIZATION_LOGIN_COUNT_DAY))
                .where(ORGANIZATION_LOGIN_COUNT_DAY.YEAR.eq(currentYear))
                .groupBy(ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID)
                .fetch().stream().map(r -> {
            OrganizationLoginCountDay log = new OrganizationLoginCountDay();
            log.setId(r.getValue(ORGANIZATION_LOGIN_COUNT_DAY.ID));
            log.setOrganizationId(r.getValue(ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID));
            log.setMemberTime(r.getValue(ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_TIME.sum()).intValue());
            log.setMemberNum(r.getValue(ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_NUM.sum()).intValue());
            return log;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OrganizationLoginCountDay> getCurOrgActivityMember() {
        long lastTimestampYesterday = DateUtil.lastTimestampYesterday();
        Integer yesterday = DateUtil.getDay(lastTimestampYesterday);
        return orgDao.execute(e -> e.select(Fields.start()
                .add(ORGANIZATION_LOGIN_COUNT_DAY.ID)
                .add(ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID)
                .add(ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_NUM).end())
                .from(ORGANIZATION_LOGIN_COUNT_DAY))
                .where(ORGANIZATION_LOGIN_COUNT_DAY.DAY.eq(yesterday))
                .fetch().stream().map(r -> {
                    OrganizationLoginCountDay log = new OrganizationLoginCountDay();
                    log.setId(r.getValue(ORGANIZATION_LOGIN_COUNT_DAY.ID));
                    log.setOrganizationId(r.getValue(ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID));
                    log.setOrganizationNum(r.getValue(ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_NUM));
                    return log;
                }).collect(Collectors.toList());
    }


}
