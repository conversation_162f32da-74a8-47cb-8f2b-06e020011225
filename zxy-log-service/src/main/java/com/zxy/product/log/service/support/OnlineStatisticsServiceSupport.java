package com.zxy.product.log.service.support;

import static com.zxy.product.log.jooq.Tables.ONLINE_STATISTICS;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.log.api.OnlineInfoService;
import com.zxy.product.log.api.OnlineStatisticsService;
import com.zxy.product.log.content.GlobalConstant;
import com.zxy.product.log.entity.OnlineStatistics;
import com.zxy.product.log.service.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * Created by tianc on 2019/4/3.
 */
@Service
public class OnlineStatisticsServiceSupport implements OnlineStatisticsService {

    private CommonDao<OnlineStatistics> dao;
    private OnlineInfoService onlineInfoService;

    @Autowired
    public void setDao(CommonDao<OnlineStatistics> dao) {
        this.dao = dao;
    }

    @Autowired
    public void setOnlineInfoService(OnlineInfoService onlineInfoService) {
        this.onlineInfoService = onlineInfoService;
    }

    @Override
    public OnlineStatistics insert(Long mills) {
        // 获取当前时间
        int month = DateUtil.formatTimeStamp(mills, "yyyyMM");
        int day = DateUtil.formatTimeStamp(mills, "yyyyMMdd");
        int hour = DateUtil.formatTimeStamp(mills, "HH");
        // 获取在线人数，pc在线人数，app在线人数
        int onlineNumber = onlineInfoService.getOnlineCount(GlobalConstant.ROOT_ORGANIZATION_ID, Optional.of(GlobalConstant.TERMINAL_TYPE_ALL), Optional.empty());
        int pcOnlineNumber = onlineInfoService.getOnlineCount(GlobalConstant.ROOT_ORGANIZATION_ID, Optional.of(GlobalConstant.TERMINAL_TYPE_PC), Optional.empty());
        int appOnlineNumber = onlineInfoService.getOnlineCount(GlobalConstant.ROOT_ORGANIZATION_ID, Optional.of(GlobalConstant.TERMINAL_TYPE_APP), Optional.empty());
        OnlineStatistics os = new OnlineStatistics();
        os.forInsert();
        os.setOrganizationId(GlobalConstant.ROOT_ORGANIZATION_ID);
        os.setOnlineNumber(onlineNumber);
        os.setPcOnlineNumber(pcOnlineNumber);
        os.setAppOnlineNumber(appOnlineNumber);
        os.setMonth(month);
        os.setDay(day);
        os.setHour(hour);
        return dao.insert(os);
    }

    @Override
    public List<OnlineStatistics> list(int day) {
        return dao.fetch(Stream.of(ONLINE_STATISTICS.DAY.eq(day)),ONLINE_STATISTICS.HOUR.asc());
    }
}
