package com.zxy.product.log.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.log.api.UserBehaviorClickService;
import com.zxy.product.log.entity.UserBehavior;
import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.log.jooq.Tables.USER_BEHAVIOR;

@Service
public class UserBehaviorClickServiceSupport implements UserBehaviorClickService {

    private Logger log = LoggerFactory.getLogger(UserBehaviorClickServiceSupport.class);
    private CommonDao<UserBehavior> userBehaviorDao;

    @Autowired
    public void setUserBehaviorDao(CommonDao<UserBehavior> userBehaviorDao) {
        this.userBehaviorDao = userBehaviorDao;
    }

    /**
     * 保存
     *
     * @param userId      用户id
     * @param contentId   内容id
     * @param contentType 内容类型 1：专题，2：课程，3：知识，4：直播，5：话题，6：文章，7：问题，8：调研，9：考试，10：班级，11：培训班
     * @param contentName 内容名称
     * @param clientType  客户端类型 0：PC；1：APP
     * @param type        类型：9：点击，10：发现点击
     * @param value       具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
     * @param pageSource  页面来源
     * @return
     */
    @Override
    public UserBehavior insert(Optional<String> userId, Optional<String> contentId, Optional<String> contentType, Optional<String> contentName,
                               Optional<String> clientType, Optional<String> type, Optional<String> value, Optional<String> pageSource, Optional<String> status) {
        log.info("======执行insert方法");
        UserBehavior userBehavior = new UserBehavior();
        userBehavior.forInsert();
        userBehavior.setUserId(userId.orElse(""));
        userBehavior.setContentId(contentId.orElse(""));
        userBehavior.setContentType(contentType.orElse(""));
        userBehavior.setContentName(contentName.orElse(""));
        userBehavior.setClientType(clientType.orElse(""));
        userBehavior.setType(type.orElse(""));
        userBehavior.setValue(value.orElse(""));
        userBehavior.setPageSource(pageSource.orElse(""));
        userBehavior.setStatus(status.orElse(UserBehavior.DELETE_TRUE));  //默认为1
        return userBehaviorDao.insert(userBehavior);
    }

    /**
     * 提前前一天的数据集
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offSet 分页
     * @param pageSize 每页大小
     */
    @Override
    public List<UserBehavior> getListByPage(int offSet, int pageSize, Optional<Long> startTime, Optional<Long> endTime) {
        log.info("========开始调用UserBehavior，参数：offset:"+offSet+",pageSize:"+pageSize+",startTime:"+startTime+",endTime:"+endTime);
        List<Condition> conditions = Stream.of(
                startTime.map(USER_BEHAVIOR.CREATE_TIME::ge),
                endTime.map(USER_BEHAVIOR.CREATE_TIME::le)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(USER_BEHAVIOR.STATUS.eq(UserBehavior.DELETE_FLASE));
        return userBehaviorDao.execute(e->e.select(Fields.start().add(USER_BEHAVIOR).end()).from(USER_BEHAVIOR)
                .where(conditions).
                limit(offSet,pageSize).fetch(item->{
                    UserBehavior userBehavior = new UserBehavior();
                    userBehavior.setModifiTime(item.getValue(USER_BEHAVIOR.MODIFI_TIME));
                    userBehavior.setUserId(item.getValue(USER_BEHAVIOR.USER_ID));
                    userBehavior.setClientType(item.getValue(USER_BEHAVIOR.CLIENT_TYPE));
                    userBehavior.setContentType(item.getValue(USER_BEHAVIOR.CONTENT_TYPE));
                    userBehavior.setContentId(item.getValue(USER_BEHAVIOR.CONTENT_ID));
                    userBehavior.setContentName(item.getValue(USER_BEHAVIOR.CONTENT_NAME));
                    userBehavior.setId(item.getValue(USER_BEHAVIOR.ID));
                    userBehavior.setCreateTime(item.getValue(USER_BEHAVIOR.CREATE_TIME));
                    userBehavior.setPageSource(item.getValue(USER_BEHAVIOR.PAGE_SOURCE));
                    userBehavior.setStatus(item.getValue(USER_BEHAVIOR.STATUS));
                    userBehavior.setType(item.getValue(USER_BEHAVIOR.TYPE));
                    userBehavior.setValue(item.getValue(USER_BEHAVIOR.VALUE));
                    return userBehavior;
                }));
    }

    @Override
    public void delete(Optional<Long> startTime, Optional<Long> endTime) {
        log.error("执行delete，删除用户行为数据，startTime：{} ，endTime：{}",startTime,endTime);
        List<String> ids = userBehaviorDao.execute(x -> x.select(USER_BEHAVIOR.ID)
                        .from(USER_BEHAVIOR)
                        .where( startTime.map(USER_BEHAVIOR.CREATE_TIME::ge).orElse(DSL.trueCondition())
                                .and(endTime.map(USER_BEHAVIOR.CREATE_TIME::le).orElse(DSL.trueCondition())))
                        .fetch(r -> r.get(USER_BEHAVIOR.ID))
        );
        userBehaviorDao.delete(ids);
    }

    @Override
    public String get(String sql) {
        return userBehaviorDao.execute(dsl -> dsl.resultQuery(sql).fetchOne(record -> record.getValue("value", String.class)));
    }

    @Override
    public int insert(String sql) {
        return userBehaviorDao.execute(dsl -> dsl.execute(sql));
    }
}
