package com.zxy.product.log.service.support;

import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.log.api.UserActiveService;
import com.zxy.product.log.entity.OrganizationLoginCountDay;
import com.zxy.product.log.entity.UserActive;
import com.zxy.product.log.service.util.DateUtil;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.log.jooq.Tables.*;


@Service
public class UserActiveServiceSupport implements UserActiveService {

    private static List<String> yearTime = DateUtil.getDate(Calendar.getInstance().get(Calendar.YEAR));

    public static final String DATE_PATTERN_SEPARATOR = "-";

    public static final Integer ZERO_TIMES = 0;

    private CommonDao<UserActive> userActiveDao;

    @Autowired
    public void setUserActiveDao(CommonDao<UserActive> userActiveDao) {
        this.userActiveDao = userActiveDao;
    }

    @Override
    public List<List<Object>> getUserActiveData(String memberId) {
        List<UserActive> userActives = userActiveDao.execute(d -> d.select(USER_ACTIVE.fields())
                .from(USER_ACTIVE)
                .where(USER_ACTIVE.MEMBER_ID.eq(memberId))
                .fetchInto(UserActive.class)
        );
        Map<String, Integer> dayTimesMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userActives)) {
            dayTimesMap = userActives.stream()
            .collect(Collectors.toMap(u -> u.getYearInfo() + DATE_PATTERN_SEPARATOR  + u.getMonthInfo() + DATE_PATTERN_SEPARATOR + u.getDayInfo()
                            , UserActive::getLoginTimes,(ew1,ew2)->ew1,HashMap::new));
        }

        List<List<Object>> timeList = new ArrayList<>();
        for(String date : yearTime){
            List<Object> data = new ArrayList<>();
            Integer times = dayTimesMap.get(date);
            data.add(date);
            if (ObjectUtils.isEmpty(times)){
                data.add(ZERO_TIMES);
            }else {
                data.add(times);
            }
            timeList.add(data);
        }
        return timeList;
    }

    @Override
    public List<UserActive> getLoginInfo(Integer year, String orgId) {
        return userActiveDao.execute(e -> e.select(Fields.start()
                                .add(DSL.countDistinct(MEMBER.ID))
                                .add(DSL.sum(USER_ACTIVE.LOGIN_TIMES))
                               .end())
                        .from(USER_ACTIVE))
                .leftJoin(MEMBER).on(USER_ACTIVE.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(ORGANIZATION_DETAIL).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB))
                .where(MEMBER.FROM.eq(1))
                .and(MEMBER.STATUS.eq(1))
                .and(USER_ACTIVE.YEAR_INFO.eq(year))
                .and(ORGANIZATION_DETAIL.ROOT.eq(orgId))
                .fetch().stream().map(r -> {
                    UserActive userActive = new UserActive();
                    userActive.setMemberNum(r.getValue(DSL.countDistinct(MEMBER.ID)));
                    userActive.setMemberTimes(r.getValue(DSL.sum(USER_ACTIVE.LOGIN_TIMES)) == null ? 0 : r.getValue(DSL.sum(USER_ACTIVE.LOGIN_TIMES)).intValue());
                    return userActive;
                }).collect(Collectors.toList());
    }

}
