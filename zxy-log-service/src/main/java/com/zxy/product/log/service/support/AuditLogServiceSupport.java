package com.zxy.product.log.service.support;

import static com.zxy.product.log.jooq.Tables.AUDIT_LOG;

import java.util.Optional;
import java.util.stream.Stream;

import org.jooq.Condition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.log.api.AuditLogService;
import com.zxy.product.log.entity.AuditLog;

/**
 * <AUTHOR>
 *
 */
@Service
public class AuditLogServiceSupport implements AuditLogService {

    private CommonDao<AuditLog> dao;

    @Autowired
    public void setDao(CommonDao<AuditLog> dao) {
        this.dao = dao;
    }

    @Override
    public PagedResult<AuditLog> list(Integer page, Integer pageSize, String organizationId, Optional<String> desc, Optional<String> module,
            Optional<String> memberFullName, Optional<Integer> action, Optional<String> browser, Optional<Long> start, Optional<Long> end) {

    	Long oneDay = 1 * 24 * 3600 * 1000L;
    	if(end.isPresent()) {
    		end = Optional.of(end.get() + oneDay);
    	}
        Stream<Optional<Condition>> condition = Stream.of(
                desc.map(AUDIT_LOG.DESC::contains),
                module.map(AUDIT_LOG.MODULE::eq),
                memberFullName.map(AUDIT_LOG.MEMBER_FULL_NAME::contains),
                action.map(AUDIT_LOG.ACTION::eq),
                browser.map(AUDIT_LOG.BROWSER::contains),
                start.map(AUDIT_LOG.LOG_TIME::ge),
                end.map(AUDIT_LOG.LOG_TIME::le),
                Optional.of(AUDIT_LOG.ORGANIZATION_ID.eq(organizationId)));
        return dao.fetchWithOptional(page, pageSize, condition, AUDIT_LOG.LOG_TIME.desc());
    }

}
