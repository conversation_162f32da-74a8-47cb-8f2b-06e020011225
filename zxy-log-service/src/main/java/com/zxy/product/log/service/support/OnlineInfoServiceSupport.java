package com.zxy.product.log.service.support;

import com.google.common.collect.Lists;
import com.mongodb.DBObject;
import com.mongodb.WriteResult;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.cache.redis.RedisCacheService;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.serialize.Serializer;
import com.zxy.product.log.api.OnlineInfoService;
import com.zxy.product.log.content.ErrorCode;
import com.zxy.product.log.content.GlobalConstant;
import com.zxy.product.log.entity.LoginLog;
import com.zxy.product.log.entity.OnlineInfo;
import com.zxy.product.log.service.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import static com.zxy.product.log.jooq.Tables.LOGIN_LOG;

@Service
public class OnlineInfoServiceSupport implements OnlineInfoService {

    private static final Logger LOGGER = LoggerFactory.getLogger(OnlineInfoServiceSupport.class);
    private final static Long REQUEST_TIMEOUT_MILLS = 60 * 1000L;

    private MongoTemplate mongoTemplate;
    private CommonDao<LoginLog> loginLogDao;
    private Cache cache;
    private static final Object LOCK = new Object();
    private static final String ONLINE_COUNT = "online-count";
    private static final String ONLINE_COUNT_PC = "online-count-pc";
    private static final String ONLINE_COUNT_APP = "online-count-app";
    private static final String ONLINE_COUNT_TIME = "online-count-time";
    private static final String DISPLAY_ONLINE_COUNT = "display_online-count";
    /**
     * Mongodb中存储用户在线状态信息的集合名
     */
    private static final String USER_ONLINE_INFO_COLL = "user_online_info";
    public static final String APPLICATION_NAME = "zxy-log-web-server";

    @Autowired
    public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Autowired
    public void setLoginLogDao(CommonDao<LoginLog> loginLogDao) {
        this.loginLogDao = loginLogDao;
    }

    @Autowired
    public void setOauthRedisCacheService(Redis redis, Serializer ser) {
        RedisCacheService redisCacheService = new RedisCacheService(APPLICATION_NAME);
        redisCacheService.setRedis(redis);
        redisCacheService.setSerializer(ser);
        this.cache = redisCacheService.create("online-info");
    }

    @Override
    public PagedResult<OnlineInfo> list(Integer page, Integer pageSize, String companyId,
                                        Optional<Integer> terminalType) {
        int curTime = (int) (System.currentTimeMillis() / 1000L);
        if (terminalType.isPresent()) {
            Criteria criteria = Criteria.where("company_id").is(companyId);
            if (terminalType.get() == GlobalConstant.TERMINAL_TYPE_APP) {
                criteria = criteria.and("app_expiration_time").gt(curTime);
            } else {
                criteria = criteria.and("pc_expiration_time").gt(curTime);
            }
            PagedResult<OnlineInfo> rst = this.doQuery(criteria, page, pageSize);
            return rst;
        } else {
            Criteria criteriaApp = Criteria.where("company_id").is(companyId).and("app_expiration_time").gt(curTime);
            Criteria criteriaPc = Criteria.where("company_id").is(companyId).and("pc_expiration_time").gt(curTime);
            Criteria criteriaAll = new Criteria();
            criteriaAll = criteriaAll.orOperator(criteriaApp, criteriaPc);
            PagedResult<OnlineInfo> rst = this.doQuery(criteriaAll, page, pageSize);
            return rst;
        }

    }

    private PagedResult<OnlineInfo> doQuery(Criteria criteria, Integer page, Integer pageSize) {
        Query query = new Query(criteria);
        int count = (int) mongoTemplate.count(query, USER_ONLINE_INFO_COLL);
        query.skip((page - 1) * pageSize).limit(pageSize);
        List<OnlineInfo> onlineInfoList = new LinkedList<>();
        mongoTemplate.executeQuery(query, USER_ONLINE_INFO_COLL, doc -> {
            onlineInfoList.add(convertDocument(doc));
        });
        return PagedResult.create(count, onlineInfoList);
    }

    private OnlineInfo convertDocument(DBObject doc) {
        OnlineInfo info = new OnlineInfo();
        info.setMemberId((String) doc.get("member_id"));
        info.setCompanyId((String) doc.get("company_id"));
        info.setAppExpirationTime((int) doc.get("app_expiration_time"));
        info.setAppIP((String) doc.get("app_ip"));
        info.setAppLoginTime((int) doc.get("app_login_time"));
        info.setPcBrowser((String) doc.get("pc_browser"));
        info.setPcExpirationTime((int) doc.get("pc_expiration_time"));
        info.setPcIP((String) doc.get("pc_ip"));
        info.setPcLoginTime((int) doc.get("pc_login_time"));
        info.setPcSystem((String) doc.get("pc_system"));
        info.setPhoneModel((String) doc.get("phone_model"));
        info.setPhoneSystem((String) doc.get("phone_system"));
        return info;
    }

    /**
     * 获取指定终端的在线人数
     * @param companyId 公司ID
     * @param terminalType 终端类型，APP/PC，空表示所有
     * @param isEnable 是否只获取启用人的数据
     * @return
     */
    @Override
    public int getOnlineCount(String companyId, Optional<Integer> terminalType, Optional<Boolean> isEnable) {

        Integer terminalTypeCode = null;
        Integer onlineCount = null;

        //1.在线登录统计类型判断
        if (terminalType.isPresent()) {
            terminalTypeCode = terminalType.get();
        }else {
            throw new UnprocessableException(ErrorCode.OnlineTypeError);
        }

        if(Integer.valueOf(GlobalConstant.TERMINAL_TYPE_PC).equals(terminalTypeCode)){

            //pc
            onlineCount = cache.get(ONLINE_COUNT_PC, Integer.class);
        }else if(Integer.valueOf(GlobalConstant.TERMINAL_TYPE_APP).equals(terminalTypeCode)){

            //app
            onlineCount = cache.get(ONLINE_COUNT_APP, Integer.class);
        }else {

            //pc+app
            onlineCount = cache.get(ONLINE_COUNT, Integer.class);
        }

        Long timestamp = cache.get(ONLINE_COUNT_TIME, Long.class);

        if(onlineCount == null){
            LOGGER.error("登陆获取当前在线人数onlineCount异常：{} ", onlineCount);
            return 98765;
        }
        Long current = System.currentTimeMillis();
        if (Math.abs(current - timestamp) > REQUEST_TIMEOUT_MILLS) {
            LOGGER.error("登陆获取当前在线人数时间戳异常 current:{} , timestamp:{} ,onlineCount:{}", current, timestamp,onlineCount);
            return 98765;
        }
        return onlineCount;
    }


	@Override
	public int getTodayLoginCount() {
		long todayZero = DateUtil.getTodayZeroTimeStamp();
		int count = loginLogDao.execute(x->x.select(LOGIN_LOG.ID.count()).from(LOGIN_LOG)
				.where(LOGIN_LOG.CREATE_TIME.ge(todayZero)).fetchOne(LOGIN_LOG.ID.count()));
		LOGGER.info("getTodayLoginCount.count:" + count);
		return count;
	}

    @Override
    public int getYearLoginCount() {
        long todayZero = DateUtil.getTodayZeroTimeStamp();
        long firstDayOfYearTimestamp = DateUtil.getFirstDayOfYearTimestamp();
        int count = loginLogDao.execute(x->x.select(LOGIN_LOG.ID.count()).from(LOGIN_LOG)
                .where(LOGIN_LOG.CREATE_TIME.between(firstDayOfYearTimestamp,todayZero)).fetchOne(LOGIN_LOG.ID.count()));
        LOGGER.info("getYearLoginCount.count:" + count);
        return count;
    }

    @Override
    public int getTodayLoginMemberCount() {
        long todayZero = DateUtil.getTodayZeroTimeStamp();
        int count = loginLogDao.execute(x->x.select(LOGIN_LOG.MEMBER_ID.countDistinct()).from(LOGIN_LOG)
                .where(LOGIN_LOG.CREATE_TIME.ge(todayZero)).fetchOne(LOGIN_LOG.MEMBER_ID.countDistinct()));
        LOGGER.info("getTodayLoginMemberCount.count:" + count);
        return count;
    }
    @Override
    public void testAddMongo(String valueName, String value, String name) {
        Query query = new Query(Criteria.where("name").is(name));
        Update update = new Update();
        update.set(valueName, value);
        WriteResult result = mongoTemplate.upsert(query, update, name);

        LOGGER.info("测试mongo:添加值:{}",result.toString());
    }

    @Override
    public List<String> testQueryMongo(String name, String valueName) {
        List<String> list = Lists.newArrayList();
        Query query = new Query(Criteria.where("name").is(name));
        mongoTemplate.executeQuery(query, name, doc -> {
            LOGGER.info("测试mongo:查询值:{}",doc);
            list.add((String) doc.get(valueName));
        });

        return list;
    }

}
