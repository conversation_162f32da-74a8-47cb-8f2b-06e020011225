package com.zxy.product.log.service.support;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.log.api.HotResourceVisitService;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.content.ErrorCode;
import com.zxy.product.log.entity.HotResourceVisit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zxy.product.log.content.CacheKeyConstant.DISPLAY_MODULE;
import static com.zxy.product.log.content.CacheKeyConstant.ZXY_LOG;
import static com.zxy.product.log.jooq.Tables.HOT_RESOURCE_VISIT;

/**
 * <AUTHOR> zhouyong
 * @ClassName : HotResourceVisitServiceSupport
 * @Description : 热门资源接口实现
 * @date : 2024-09-24 9:03
 */
@Service
public class HotResourceVisitServiceSupport implements HotResourceVisitService {

    private static final String RESOURCE_TOP3 = String.join("#", ZXY_LOG,DISPLAY_MODULE, CacheKeyConstant.RESOURCE_TOP3);
    private static final String LIVING_TOP3   = String.join("#", CacheKeyConstant.COURSE_STUDY, DISPLAY_MODULE, CacheKeyConstant.LIVING_TOP3);

    private CommonDao<HotResourceVisit> hotResourceVisitDao;

    private Redis redis;

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }


    @Autowired
    public void setHotResourceVisitDao(CommonDao<HotResourceVisit> hotResourceVisitDao) {
        this.hotResourceVisitDao = hotResourceVisitDao;
    }

    @Override
    public HotResourceVisit findById(String id) {
        return hotResourceVisitDao.getOptional(id).orElseThrow(ErrorCode.DATA_NOT_EXISTS);
    }

    @Override
    public Integer findShowCount(Integer businessType) {
        return hotResourceVisitDao.execute(ctx -> ctx.selectCount()
            .from(HOT_RESOURCE_VISIT)
            .where(HOT_RESOURCE_VISIT.BUSINESS_TYPE.eq(businessType).and(HOT_RESOURCE_VISIT.STATUS.eq(HotResourceVisit.STATUS_SHOW)))
            .fetchOne(0, Integer.class)
        );
    }

    @Override
    public void hotResourceVisitInsert(List<HotResourceVisit> hotResourceVisits, Integer businessType) {
        hotResourceVisitDao.delete(HOT_RESOURCE_VISIT.BUSINESS_TYPE.eq(businessType));
        if (!CollectionUtils.isEmpty(hotResourceVisits)) {
            hotResourceVisitDao.insert(hotResourceVisits);
        }
    }

    @Override
    public Integer updateStatus(String id, Integer businessType, Integer status) {
        Integer count = hotResourceVisitDao.execute(ctx ->
            ctx.update(HOT_RESOURCE_VISIT).set(HOT_RESOURCE_VISIT.STATUS, status)
                .where(HOT_RESOURCE_VISIT.ID.eq(id)).execute());
        Map<String, List<HotResourceVisit>> topN = new HashMap<>();
        switch (businessType) {
            case HotResourceVisit.BUSINESS_TYPE_COURSE:
            case HotResourceVisit.BUSINESS_TYPE_SUBJECT:
                redis.del(RESOURCE_TOP3);
                List<HotResourceVisit> visits = this.queryByType(HotResourceVisit.BUSINESS_TYPE_COURSE, HotResourceVisit.BUSINESS_TYPE_SUBJECT);
                Map<Integer, List<HotResourceVisit>> visitMaps = visits.stream().collect(Collectors.groupingBy(HotResourceVisit::getBusinessType));
                List<HotResourceVisit> courseTopN = visitMaps.getOrDefault(HotResourceVisit.BUSINESS_TYPE_COURSE, new ArrayList<>())
                    .stream().filter(x -> x.getStatus() == HotResourceVisit.STATUS_SHOW)
                    .sorted(Comparator.comparing(HotResourceVisit::getDay30Visit).reversed()).collect(Collectors.toList());
                List<HotResourceVisit> subjectTopN = visitMaps.getOrDefault(HotResourceVisit.BUSINESS_TYPE_SUBJECT, new ArrayList<>())
                    .stream().filter(x -> x.getStatus() == HotResourceVisit.STATUS_SHOW)
                    .sorted(Comparator.comparing(HotResourceVisit::getDay30Visit).reversed()).collect(Collectors.toList());
                topN.put("course", courseTopN);
                topN.put("subject", subjectTopN);
                redis.process(jedis -> {
                    try {
                        return jedis.set(RESOURCE_TOP3, new ObjectMapper().writeValueAsString(topN));
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                    return null;
                });
                break;
            case HotResourceVisit.BUSINESS_TYPE_LIVE:
                redis.del(LIVING_TOP3);
                List<HotResourceVisit> liveVisits = this.queryShowLiveVisits().stream()
                    .sorted(Comparator.comparing(HotResourceVisit::getDay30Visit).reversed()).collect(Collectors.toList());
                topN.put("living", liveVisits);
                redis.process(jedis -> {
                    try {
                        return jedis.set(LIVING_TOP3, new ObjectMapper().writeValueAsString(topN));
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                    return null;
                });
                break;
            default: break;
        }
        return count;
    }

    @Override
    public List<HotResourceVisit> queryShowLiveVisits() {
        return hotResourceVisitDao.execute(ctx -> ctx.select(HOT_RESOURCE_VISIT.fields())
            .from(HOT_RESOURCE_VISIT)
            .where(HOT_RESOURCE_VISIT.STATUS.eq(HotResourceVisit.STATUS_SHOW).and(HOT_RESOURCE_VISIT.BUSINESS_TYPE.eq(HotResourceVisit.BUSINESS_TYPE_LIVE)))
            .fetchInto(HotResourceVisit.class)
        );
    }

    @Override
    public List<HotResourceVisit> queryByType(Integer ... businessType) {
        return hotResourceVisitDao.execute(ctx -> ctx.select(HOT_RESOURCE_VISIT.fields())
            .from(HOT_RESOURCE_VISIT)
            .where(HOT_RESOURCE_VISIT.BUSINESS_TYPE.in(businessType)).fetchInto(HotResourceVisit.class))
            .stream().sorted(Comparator.comparing(HotResourceVisit::getDay30Visit).reversed()).collect(Collectors.toList());
    }
}
