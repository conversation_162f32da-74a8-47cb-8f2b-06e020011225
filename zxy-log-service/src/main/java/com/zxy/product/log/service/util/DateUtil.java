package com.zxy.product.log.service.util;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by tianc on 2019/4/3.
 */
public class DateUtil {

    public static final String DATE_PATTERN_NEW = "yyyyMMdd";
    public static final String FIRST_DAY_SUFFIX = "-01-01";
    public static final String LAST_DAY_SUFFIX = "-12-31";





    /**
     * 获取当前日期时间
     *
     * @param pattern 模式
     * @return int值
     */
    public static int formatTimeStamp(Long mills, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date date = new Date(mills);
        return Integer.parseInt(sdf.format(date));
    }

    /**
     * 获取当天0点的时间戳
     *
     * @return 时间戳
     */
    public static long getTodayZeroTimeStamp() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * @return : 获取昨天最后时间戳
     */
    public static long lastTimestampYesterday() {
        return LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MAX).toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    public static Integer getYear(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMDD");
        Date date = new Date(time);
        return Integer.valueOf(sdf.format(date).substring(0,4));
    }

    public static Integer getDay(Long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date date = new Date(time);
        return Integer.valueOf(sdf.format(date));
    }

    public static String format(Date date, String pattern) {
        Instant instant = date.toInstant();
        LocalDateTime localDateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(dateTimeFormatter);
    }

    /**
     * @param time : yyyymmdd
     * @return : 1970.1.1到现在的天数
     */
    public static Long format2EpochDay(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN_NEW);
        LocalDate localDate = LocalDate.parse(time, formatter);
        return localDate.atStartOfDay(ZoneId.systemDefault()).toLocalDate().toEpochDay();
    }

    /**
     * @param epochDay : 1970.1.1到现在的天数
     * @return : yyyymmdd
     */
    public static Long dateFormat(Long epochDay) {
        LocalDate date = LocalDate.ofEpochDay(epochDay).atStartOfDay(ZoneId.systemDefault()).toLocalDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN_NEW);
        return Long.valueOf(date.format(formatter));
    }

    /**
     * @return : 获取7天前开始时间
     */
    public static long before7Timestamp() {
        Calendar instance = Calendar.getInstance();
        instance.setTime(offset(new Date(), Calendar.DAY_OF_MONTH, -7));
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.MILLISECOND, 0);
        return instance.getTime().getTime();
    }

    /**
     * @param date      : 时间
     * @param dateField ：便宜量单位 Calendar.DAY_OF_MONTH  {@link Calendar}
     * @param offset    ：时间偏移量
     * @return ：Date
     */
    public static Date offset(Date date, int dateField, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(dateField, offset);
        return calendar.getTime();
    }

    public static String getDayBefore(String date, int i) {
        // 获取前一天日期
        return LocalDate.parse(date)
                .minusDays(i)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    public static String getDayAfter(String date, int i) {
        // 获取前一天日期
        return LocalDate.parse(date)
                .plusDays(i)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 计算某一天是周几
     *
     * @param pTime
     * @return 格式为2018-01-01
     * 如果错误就返回0
     */
    public static int dayForWeek(String pTime) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Date tmpDate = format.parse(pTime);
            Calendar cal = Calendar.getInstance();
            int[] weekDays = {7, 1, 2, 3, 4, 5, 6};
            try {
                cal.setTime(tmpDate);
            } catch (Exception e) {
                e.printStackTrace();
            }
            int w = cal.get(Calendar.DAY_OF_WEEK) - 1; // 指示一个星期中的某天。
            if (w < 0) {
                w = 0;
            }
            return weekDays[w];
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    public static ArrayList<String> getDate(int year) {
        ArrayList<String> dateList = new ArrayList<>();
        for (int i = dayForWeek(year + FIRST_DAY_SUFFIX) - 1; i > 0; i--) {
            dateList.add(getDayBefore(year + FIRST_DAY_SUFFIX, 7 - i));
        }
        for (int i = 7 - dayForWeek(year + LAST_DAY_SUFFIX); i > 0; i--) {
            dateList.add(getDayAfter(year + LAST_DAY_SUFFIX, i));
        }
        for (int mon = 1; mon < 13; mon++) {
            int month = mon;
            int day = 0;
            //第二步：求输入的年份月份与1900年1月1日相隔的总天数
            for (int i = 1900; i < year; i++) {
                if ((i % 4 == 0 && i % 100 != 0) || (i % 400 == 0)) {
                    day += 366;
                } else {
                    day += 365;
                }
            }
            //求相隔月份的天数
            for (int i = 1; i < month; i++) {
                if (i == 2) {
                    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
                        day += 29;
                    } else {
                        day += 28;
                    }
                } else if (i == 4 || i == 6 || i == 9 || i == 11) {
                    day += 30;
                } else {
                    day += 31;
                }
            }
            //第三步：计算上个月最后一天是星期几
            int weeks = day % 7;
            int day2 = 0;
            switch (month) {
                case 2:
                    if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {
                        day2 += 29;
                    } else {
                        day2 += 28;
                    }
                    break;
                case 4:
                case 6:
                case 9:
                case 11:
                    day2 = 30;
                    break;
                default:
                    day2 = 31;
                    break;
            }
            for (int i = 1; i <= day2; i++) {
                String sdate = year + "-" + mon + "-" + i;
                dateList.add(sdate);
            }
        }
        return dateList;
    }

    public static List<List<String>> getYearWeek(int year) {
        //获取到某年的所有的日期
        //1900年1月1日相隔的总天数
        ArrayList<String> dateList = getDate(year);
        ArrayList<String> MondayList = new ArrayList<>();
        ArrayList<String> TuesdayList = new ArrayList<>();
        ArrayList<String> WednesdayList = new ArrayList<>();
        ArrayList<String> ThursdayList = new ArrayList<>();
        ArrayList<String> FridayList = new ArrayList<>();
        ArrayList<String> SaturdayList = new ArrayList<>();
        ArrayList<String> SundayList = new ArrayList<>();
        //判断每天是周几
        dateList.forEach(s -> {
            try {
                int i = dayForWeek(s);
                // System.out.println(s+"周"+CalculationDate.dayForWeek(s));
                if (i == 1) {
                    MondayList.add(s);
                    return;
                }
                if (i == 2) {
                    TuesdayList.add(s);
                    return;
                }
                if (i == 3) {
                    WednesdayList.add(s);
                    return;
                }
                if (i == 4) {
                    ThursdayList.add(s);
                    return;
                }
                if (i == 5) {
                    FridayList.add(s);
                    return;
                }
                if (i == 6) {
                    SaturdayList.add(s);
                    return;
                }
                if (i == 7) {
                    SundayList.add(s);
                    return;
                }

            } catch (Throwable throwable) {
                throwable.printStackTrace();
            }

        });
        ArrayList<List<String>> lists = new ArrayList<>();
        lists.add(MondayList);
        lists.add(TuesdayList);
        lists.add(WednesdayList);
        lists.add(ThursdayList);
        lists.add(FridayList);
        lists.add(SaturdayList);
        lists.add(SundayList);
        return lists;
    }


    /**
     * 获取某月是一年的第几周
     *
     * @param year
     * @param month
     * @return
     */
    public static int getMonthWeekIndex(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month);
        //获取当月最小值
        int lastDay = cal.getMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中的月份，当月+1月-1天=当月最后一天
        cal.set(Calendar.DAY_OF_MONTH, lastDay - 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        int week = cal.get(Calendar.WEEK_OF_YEAR);
        if (week == 1 && cal.get(Calendar.MONTH) == 11) {
            cal.add(Calendar.DATE, -6);
            week = cal.get(Calendar.WEEK_OF_YEAR) + 1;
        }
        int dayForWeek = dayForWeek(lastDayOfMonth);
        if (dayForWeek != 7) {
            week = week + 1;
        }
        return week;
    }

    /**
     * 获取当年第一天0点的毫秒时间戳
     * @return
     */
    public static long getFirstDayOfYearTimestamp() {
        // 获取当前日期
        LocalDate now = LocalDate.now();
        // 设置为当年的1月1日
        LocalDate firstDayOfYear = now.withDayOfYear(1);
        // 将LocalDate转换为指定时区的Date对象（这里以系统默认时区为例）
        return firstDayOfYear.atStartOfDay(ZoneId.systemDefault()).toInstant().getEpochSecond() * 1000;
    }

}
