package com.zxy.product.exam.async.task.szfn.producer;

import com.zxy.product.exam.api.QuestionCopyService;
import com.zxy.product.exam.entity.ExamRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 数智赋能考试档案数据生产者
 * 负责批量查询考试记录并放入队列供消费者处理
 */
@Component
@Scope("prototype")
public class SzfnExamProfileProducer implements Runnable {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SzfnExamProfileProducer.class);
    
    // 批量查询用户数量
    private static final int BATCH_SIZE = 15;
    
    @Autowired
    private QuestionCopyService questionCopyService;
    
    @Value("${szfn.exam.id:}")
    private String szfnExamId;
    
//    @Value("${szfn.exam.paper.instance.id:}")
//    private String szfnExamPaperInstanceId;
    
    // 生产者运行时参数
    private LinkedBlockingQueue<ExamRecord> queue;
    private ExamRecord endMarker;
    private List<String> memberIds;
    private AtomicInteger activeProducers;
    private String szfnExamPaperInstanceId;

    /**
     * 初始化生产者参数
     */
    public void init(LinkedBlockingQueue<ExamRecord> queue, 
                     ExamRecord endMarker,
                     List<String> memberIds,
                     AtomicInteger activeProducers,
                     String szfnExamPaperInstanceId) {
        this.queue = queue;
        this.endMarker = endMarker;
        this.memberIds = memberIds;
        this.activeProducers = activeProducers;
        this.szfnExamPaperInstanceId = szfnExamPaperInstanceId;
    }
    
    @Override
    public void run() {
        try {
            if (!isInitialized()) {
                LOGGER.error("生产者未正确初始化，退出执行");
                return;
            }

            LOGGER.info("生产者开始执行，处理 {} 个人员", memberIds.size());

            int processedCount = processMembersBatch();

            LOGGER.info("生产者完成，处理了 {} 个人员，生产了 {} 条考试记录",
                       memberIds.size(), processedCount);

        } catch (InterruptedException e) {
            LOGGER.warn("生产者线程被中断", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            LOGGER.error("生产者执行异常", e);
        } finally {
            // 确保无论何种情况下都会正确处理生产者完成逻辑
            handleProducerCompletion();
        }
    }
    
    /**
     * 检查生产者是否已正确初始化
     */
    private boolean isInitialized() {
        return queue != null && endMarker != null && 
               memberIds != null && activeProducers != null;
    }
    
    /**
     * 分批处理人员ID
     */
    private int processMembersBatch() throws InterruptedException {
        int processedCount = 0;

        for (int i = 0; i < memberIds.size() && !Thread.currentThread().isInterrupted(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, memberIds.size());
            List<String> batchMemberIds = memberIds.subList(i, endIndex);

            try {
                processedCount += processSingleBatch(batchMemberIds);
            } catch (InterruptedException e) {
                LOGGER.warn("生产者处理批次时被中断，已处理 {} 条记录", processedCount);
                throw e;
            } catch (Exception e) {
                LOGGER.error("批量处理人员异常，人员列表: {}", batchMemberIds, e);
                // 继续处理下一批，不因为单个批次失败而停止整个生产者
            }
        }

        if (Thread.currentThread().isInterrupted()) {
            LOGGER.info("生产者因中断提前结束，已处理 {} 条记录", processedCount);
        }

        return processedCount;
    }
    
    /**
     * 处理单个批次的人员
     */
    private int processSingleBatch(List<String> batchMemberIds) throws InterruptedException {
        Map<String, ExamRecord> examRecordMap = questionCopyService
            .findQuestionsByPaperAndExamId(szfnExamPaperInstanceId, szfnExamId, batchMemberIds);
        
        // 将查询结果放入队列
        for (ExamRecord examRecord : examRecordMap.values()) {
            queue.put(examRecord);
        }
        
        LOGGER.info("批量处理 {} 个人员完成，获得 {} 条考试记录",
                   batchMemberIds.size(), examRecordMap.size());
        
        return examRecordMap.size();
    }
    
    /**
     * 处理生产者完成逻辑
     */
    private void handleProducerCompletion() {
        int remaining = activeProducers.decrementAndGet();
        
        // 如果是最后一个生产者，放入结束标识对象
        if (remaining == 0) {
            try {
                queue.put(endMarker);
                LOGGER.info("最后一个生产者完成，放入结束标识对象");
            } catch (InterruptedException e) {
                LOGGER.error("放入结束标识对象失败", e);
                Thread.currentThread().interrupt();
            }
        }
    }
}
