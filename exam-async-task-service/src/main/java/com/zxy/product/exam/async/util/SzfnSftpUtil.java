package com.zxy.product.exam.async.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jcraft.jsch.*;
import com.zxy.product.exam.api.DigitalIntelligenceResultService;
import com.zxy.product.exam.entity.szfn.DigitalIntelligenceResult;
import com.zxy.product.exam.entity.szfn.UserExamProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数智赋能SFTP工具类
 */
@Component
public class SzfnSftpUtil implements EnvironmentAware {
    private static final Logger logger = LoggerFactory.getLogger(SzfnSftpUtil.class);
    private static final int MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    
    private String host;
    private Integer port;
    private String username;
    private String password;
    private Integer timeout;
    private String uploadPath;
    private String downloadPath;
    
    private Session session = null;
    private ChannelSftp channel = null;

    @Autowired
    private DigitalIntelligenceResultService digitalIntelligenceResultService;

    @Override
    public void setEnvironment(Environment environment) {
        host = environment.getProperty("szfn.sftp.host");
        port = environment.getProperty("szfn.sftp.port", Integer.class, 22);
        username = environment.getProperty("szfn.sftp.username");
        password = environment.getProperty("szfn.sftp.password");
        timeout = environment.getProperty("szfn.sftp.timeout", Integer.class, 30000);
        uploadPath = environment.getProperty("szfn.sftp.upload.path", "/");
        downloadPath = environment.getProperty("szfn.sftp.download.path", "/");
    }

    /**
     * 登录SFTP服务器
     */
    public boolean login() {
        try {
            JSch jsch = new JSch();
            session = jsch.getSession(username, host, port);
            if (password != null) {
                session.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.setTimeout(timeout);
            session.connect();
            logger.debug("sftp session connected");

            channel = (ChannelSftp) session.openChannel("sftp");
            channel.connect();
            logger.debug("sftp channel connected successfully");
            return true;
        } catch (JSchException e) {
            logger.error("sftp login failed", e);
            return false;
        }
    }

    /**
     * 切换工作目录
     */
    public boolean changeDir(String pathName) {
        if (pathName == null || pathName.trim().equals("")) {
            logger.debug("invalid pathName");
            return false;
        }

        try {
            channel.cd(pathName.replaceAll("\\\\", "/"));
            logger.debug("directory changed to: {}", channel.pwd());
            return true;
        } catch (SftpException e) {
            logger.error("failed to change directory to: {}", pathName, e);
            return false;
        }
    }

    /**
     * 获取当前工作目录
     */
    public String getCurrentDir() {
        try {
            return channel.pwd();
        } catch (SftpException e) {
            logger.error("failed to get current directory", e);
            return "/";
        }
    }

    /**
     * 登出
     */
    public void logout() {
        if (channel != null) {
            channel.quit();
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
        logger.debug("logout successfully");
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String fileName) {
        String currentDir = getCurrentDir();
        if (!changeDir(uploadPath)) {
            return false;
        }

        try {
            channel.lstat(fileName);
            return true;
        } catch (SftpException e) {
            return false;
        } finally {
            changeDir(currentDir);
        }
    }

    /**
     * 列出指定目录下的文件
     * @param directory 目录路径
     * @return 文件列表
     */
    @SuppressWarnings("unchecked")
    public List<String> listFiles(String directory) {
        List<String> fileList = new ArrayList<>();
        String currentDir = getCurrentDir();

        try {
            if (!changeDir(directory)) {
                return fileList;
            }

            Vector<ChannelSftp.LsEntry> entries = channel.ls(".");
            for (ChannelSftp.LsEntry entry : entries) {
                if (!entry.getAttrs().isDir()) {
                    fileList.add(entry.getFilename());
                }
            }

            return fileList;
        } catch (SftpException e) {
            logger.error("列出目录文件失败: {}", directory, e);
            return fileList;
        } finally {
            changeDir(currentDir);
        }
    }

    /**
     * 创建目录
     * @param directory 目录路径
     * @return 是否成功
     */
    public boolean createDirectory(String directory) {
        try {
            channel.mkdir(directory);
            logger.debug("创建目录成功: {}", directory);
            return true;
        } catch (SftpException e) {
            // 目录可能已存在，检查是否为目录已存在的错误
            if (e.id == ChannelSftp.SSH_FX_FAILURE) {
                try {
                    // 尝试切换到该目录，如果成功说明目录已存在
                    String currentDir = getCurrentDir();
                    if (changeDir(directory)) {
                        changeDir(currentDir);
                        logger.debug("目录已存在: {}", directory);
                        return true;
                    }
                } catch (Exception ex) {
                    logger.error("检查目录是否存在失败: {}", directory, ex);
                }
            }
            logger.error("创建目录失败: {}", directory, e);
            return false;
        }
    }

    /**
     * 移动文件
     * @param sourceFile 源文件路径
     * @param targetFile 目标文件路径
     * @return 是否成功
     */
    public boolean moveFile(String sourceFile, String targetFile) {
        try {
            channel.rename(sourceFile, targetFile);
            logger.debug("移动文件成功: {} -> {}", sourceFile, targetFile);
            return true;
        } catch (SftpException e) {
            logger.error("移动文件失败: {} -> {}", sourceFile, targetFile, e);
            return false;
        }
    }

    /**
     * 删除文件
     * @param fileName 文件名
     * @return 是否成功
     */
    public boolean deleteFile(String fileName) {
        try {
            channel.rm(fileName);
            logger.debug("删除文件成功: {}", fileName);
            return true;
        } catch (SftpException e) {
            logger.error("删除文件失败: {}", fileName, e);
            return false;
        }
    }

    /**
     * 查找可处理的目标文件
     * @return 包含目标文件和结束文件的数组，[0]为目标文件，[1]为结束文件，如果没找到返回null
     */
    public String[] findTargetFiles() {
        List<String> fileList = listFiles(downloadPath);

        // 目标文件格式：年月日_小时_时间戳_随机数_examRecord_results.dat
        // 结束文件格式：年月日_小时_时间戳_随机数_examRecord_results.ok
        // 注意：目标文件和结束文件的随机数可能不一致，但时间戳应该一致
        Pattern datFilePattern = Pattern.compile("(\\d{8}_\\d{2}_\\d+)_\\d+_examRecord_results\\.dat");
        Pattern okFilePattern = Pattern.compile("(\\d{8}_\\d{2}_\\d+)_\\d+_examRecord_results\\.ok");

        // 收集所有的目标文件，按时间戳前缀分组
        Map<String, String> datFileMap = new HashMap<>();
        for (String fileName : fileList) {
            Matcher matcher = datFilePattern.matcher(fileName);
            if (matcher.matches()) {
                String timePrefix = matcher.group(1); // 年月日_小时_时间戳
                datFileMap.put(timePrefix, fileName);
            }
        }

        // 查找所有的结束文件，并尝试匹配对应的目标文件
        for (String fileName : fileList) {
            Matcher matcher = okFilePattern.matcher(fileName);
            if (matcher.matches()) {
                String timePrefix = matcher.group(1); // 年月日_小时_时间戳
                String datFile = datFileMap.get(timePrefix);

                if (datFile != null) {
                    logger.info("找到可处理的文件对: 目标文件={}, 结束文件={}", datFile, fileName);
                    return new String[]{datFile, fileName};
                }
            }
        }

        logger.debug("未找到可处理的文件对");
        return null;
    }
    
    /**
     * 上传UserExamProfile列表文件
     * @param dataList 数据列表
     * @return 是否成功
     */
    public boolean uploadFile(List<UserExamProfile> dataList) {
        if (!login()) {
            return false;
        }
        
        try {
            String dataDate = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String generateTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            
            boolean uploadResult = false;
            
            // 如果数据为空，上传空文件
            if (dataList == null || dataList.isEmpty()) {
                uploadResult = uploadEmptyFiles(dataDate, generateTime);
            } else {
                // 转换数据为JSON字符串
                StringBuilder content = new StringBuilder();
                for (UserExamProfile profile : dataList) {
                    content.append(JSON.toJSONString(profile)).append("\n");
                }
                
                byte[] contentBytes = content.toString().getBytes(StandardCharsets.UTF_8);
                
                // 检查是否需要拆分文件
                if (contentBytes.length <= MAX_FILE_SIZE) {
                    // 单文件上传
                    uploadResult = uploadSingleFile(contentBytes, dataDate, generateTime, dataList.size(), 1);
                } else {
                    // 多文件上传
                    uploadResult = uploadMultipleFiles(dataList, dataDate, generateTime);
                }
            }
            
            // 如果数据上传成功，上传finish文件
            if (uploadResult) {
                String hour = new SimpleDateFormat("HH").format(new Date());
                long timestamp = System.currentTimeMillis();
                String finishFileName = dataDate + "_" + hour + "_" + timestamp + "_examRecord.finish";
                uploadResult = uploadFileContent(new ByteArrayInputStream(new byte[0]), finishFileName);
            }
            
            return uploadResult;
            
        } finally {
            logout();
        }
    }
    
    /**
     * 上传单个文件
     */
    private boolean uploadSingleFile(byte[] contentBytes, String dataDate, 
                                   String generateTime, int recordCount, int sequence) {
        long timestamp = System.currentTimeMillis();
        String hour = new SimpleDateFormat("HH").format(new Date());
        String fileName = dataDate + "_" + hour + "_" + timestamp + "_examRecord_" + sequence + ".dat";
        String tmpFileName = fileName + ".tmp";
        String chkFileName = dataDate + "_" + hour + "_" + timestamp + "_examRecord_" + sequence + ".chk";
        
        try {
            // 1. 上传临时文件
            if (!uploadFileContent(new ByteArrayInputStream(contentBytes), tmpFileName)) {
                return false;
            }
            
            // 2. 重命名为正式文件
            if (!renameFile(tmpFileName, fileName)) {
                return false;
            }
            
            // 3. 上传校验文件
            String chkContent = createCheckContent(fileName, contentBytes.length, recordCount, dataDate, generateTime);
            if (!uploadFileContent(new ByteArrayInputStream(chkContent.getBytes(StandardCharsets.UTF_8)), 
                                 chkFileName)) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            logger.error("upload single file failed", e);
            return false;
        }
    }
    
    /**
     * 上传多个拆分文件
     */
    private boolean uploadMultipleFiles(List<UserExamProfile> dataList, 
                                      String dataDate, String generateTime) {
        int sequence = 1;
        int startIndex = 0;
        
        while (startIndex < dataList.size()) {
            StringBuilder content = new StringBuilder();
            int recordCount = 0;
            int currentSize = 0;
            
            // 计算当前文件包含的记录
            for (int i = startIndex; i < dataList.size(); i++) {
                String jsonStr = JSON.toJSONString(dataList.get(i)) + "\n";
                byte[] jsonBytes = jsonStr.getBytes(StandardCharsets.UTF_8);
                
                if (currentSize + jsonBytes.length > MAX_FILE_SIZE && recordCount > 0) {
                    break;
                }
                
                content.append(jsonStr);
                currentSize += jsonBytes.length;
                recordCount++;
            }
            
            // 上传当前文件
            byte[] contentBytes = content.toString().getBytes(StandardCharsets.UTF_8);
            if (!uploadSingleFile(contentBytes, dataDate, generateTime, recordCount, sequence)) {
                return false;
            }
            
            startIndex += recordCount;
            sequence++;
        }
        
        return true;
    }
    
    /**
     * 上传空文件
     */
    private boolean uploadEmptyFiles(String dataDate, String generateTime) {
        long timestamp = System.currentTimeMillis();
        String hour = new SimpleDateFormat("HH").format(new Date());
        String fileName = dataDate + "_" + hour + "_" + timestamp + "_examRecord.dat";
        String chkFileName = dataDate + "_" + hour + "_" + timestamp + "_examRecord.chk";
        
        try {
            // 上传空数据文件
            if (!uploadFileContent(new ByteArrayInputStream(new byte[0]), fileName)) {
                return false;
            }
            
            // 上传空校验文件
            String chkContent = createCheckContent(fileName, 0, 0, dataDate, generateTime);
            if (!uploadFileContent(new ByteArrayInputStream(chkContent.getBytes(StandardCharsets.UTF_8)), 
                                 chkFileName)) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            logger.error("upload empty files failed", e);
            return false;
        }
    }
    
    /**
     * 创建校验文件内容
     */
    private String createCheckContent(String fileName, int fileSize, int recordCount, 
                                    String dataDate, String generateTime) {
        return fileName + "|" + fileSize + "|" + recordCount + "|" + dataDate + "|" + generateTime + "\r\n";
    }
    
    /**
     * 重命名文件
     */
    private boolean renameFile(String oldName, String newName) {
        String currentDir = getCurrentDir();
        if (!changeDir(uploadPath)) {
            return false;
        }
        
        try {
            channel.rename(oldName, newName);
            logger.debug("rename successful: {} -> {}", oldName, newName);
            return true;
        } catch (SftpException e) {
            logger.error("rename failed: {} -> {}", oldName, newName, e);
            return false;
        } finally {
            changeDir(currentDir);
        }
    }
    
    /**
     * 上传文件内容
     */
    private boolean uploadFileContent(InputStream inputStream, String fileName) {
        String currentDir = getCurrentDir();
        if (!changeDir(uploadPath)) {
            return false;
        }
        
        try {
            channel.put(inputStream, fileName, ChannelSftp.OVERWRITE);
            logger.debug("upload file content successful: {}", fileName);
            return true;
        } catch (SftpException e) {
            logger.error("upload file content failed: {}", fileName, e);
            return false;
        } catch (Exception e) {
            logger.error("upload file content error: {}", fileName, e);
            return false;
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                logger.error("close inputStream error", e);
            }
            changeDir(currentDir);
        }
    }

    /**
     * 下载并解析数智赋能结果文件（内部方法，假设已经登录）
     * @param remoteFileName 远程文件名
     * @return 解析后的数据列表
     */
    private List<DigitalIntelligenceResult> downloadAndParseDigitalIntelligenceFileInternal(String remoteFileName) {
        String currentDir = getCurrentDir();
        if (!changeDir(downloadPath)) {
            return new ArrayList<>();
        }

        List<DigitalIntelligenceResult> results = new ArrayList<>();

        try (InputStream inputStream = channel.get(remoteFileName);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue;
                }

                try {
                    // 解析JSON并转换为实体对象
                    JSONObject jsonObject = JSON.parseObject(line);
                    DigitalIntelligenceResult result = convertToDigitalIntelligenceResult(jsonObject);
                    results.add(result);
                } catch (Exception e) {
                    logger.error("解析JSON行失败: {}", line, e);
                }
            }

            logger.info("成功解析数智赋能文件: {}, 记录数: {}", remoteFileName, results.size());
            return results;

        } catch (SftpException e) {
            logger.error("下载文件失败: {}", remoteFileName, e);
            return new ArrayList<>();
        } catch (IOException e) {
            logger.error("读取文件流失败: {}", remoteFileName, e);
            return new ArrayList<>();
        } finally {
            changeDir(currentDir);
        }
    }

    /**
     * 转换JSONObject为DigitalIntelligenceResult对象
     */
    private DigitalIntelligenceResult convertToDigitalIntelligenceResult(JSONObject jsonObject) {
        DigitalIntelligenceResult result = new DigitalIntelligenceResult();
        result.forInsert();
        
        result.setName(jsonObject.getString("name"));
        result.setMemberId(jsonObject.getString("id_"));
        
        // 转换数组字段为用|分隔的字符串
        result.setAbilityTags(convertArrayToString(jsonObject.getJSONArray("ability_tags")));
        result.setStrengthEnhancement(convertArrayToString(jsonObject.getJSONArray("strength_enhancement")));
        result.setSkillImprovement(convertArrayToString(jsonObject.getJSONArray("skill_improvement")));
        result.setShortTermPlan(convertArrayToString(jsonObject.getJSONArray("short_term_plan")));
        result.setMediumTermPlan(convertArrayToString(jsonObject.getJSONArray("medium_term_plan")));
        result.setRecommendedCoursesId(convertArrayToString(jsonObject.getJSONArray("recommended_courses_id")));
        
        return result;
    }

    /**
     * 下载文件并批量写入数据库
     * 自动查找符合条件的目标文件进行处理，处理完成后备份并删除文件
     * @return 是否成功
     */
    public boolean downloadAndSaveDigitalIntelligenceData() {
        if (!login()) {
            logger.error("SFTP登录失败");
            return false;
        }

        try {
            // 查找可处理的文件
            String[] targetFiles = findTargetFiles();
            if (targetFiles == null) {
                logger.info("未找到可处理的数智赋能文件");
                return true;
            }

            String datFile = targetFiles[0];
            String okFile = targetFiles[1];

            logger.info("开始处理数智赋能文件: {}", datFile);

            // 解析并处理数据
            List<DigitalIntelligenceResult> results = downloadAndParseDigitalIntelligenceFileInternal(datFile);

            if (results.isEmpty()) {
                logger.warn("文件 {} 中没有有效数据", datFile);
            } else {
                // 分离新增和更新数据
                List<DigitalIntelligenceResult> insertList = new ArrayList<>();
                List<DigitalIntelligenceResult> updateList = new ArrayList<>();

                for (DigitalIntelligenceResult result : results) {
                    // 检查用户是否已存在数据
                    List<DigitalIntelligenceResult> existingResults = digitalIntelligenceResultService.findByMemberId(result.getMemberId());
                    if (!existingResults.isEmpty()) {
                        // 存在数据，使用第一条记录的ID进行更新
                        result.setId(existingResults.get(0).getId());
                        result.forUpdate();
                        updateList.add(result);
                    } else {
                        insertList.add(result);
                    }
                }

                // 批量新增
                if (!insertList.isEmpty()) {
                    digitalIntelligenceResultService.batchInsert(insertList);
                    logger.info("成功批量新增数智赋能数据: {} 条记录", insertList.size());
                }

                // 批量更新
                if (!updateList.isEmpty()) {
                    for (DigitalIntelligenceResult result : updateList) {
                        digitalIntelligenceResultService.update(result);
                    }
                    logger.info("成功批量更新数智赋能数据: {} 条记录", updateList.size());
                }

                logger.info("数智赋能数据处理完成，新增: {} 条，更新: {} 条", insertList.size(), updateList.size());
            }

            // 数据处理完成后，备份文件
            String backupPath = downloadPath.endsWith("/") ? downloadPath + "back" : downloadPath + "/back";
            if (backupFiles(datFile, okFile, backupPath)) {
                // 备份成功（文件已经移动到备份目录）
                logger.info("文件处理完成，已备份原文件: {}, {}", datFile, okFile);
            } else {
                logger.error("文件备份失败，保留原文件: {}, {}", datFile, okFile);
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.error("下载并保存数智赋能数据失败", e);
            return false;
        } finally {
            logout();
        }
    }

    /**
     * 备份文件到指定目录
     * @param datFile 目标文件名
     * @param okFile 结束文件名
     * @param backupPath 备份目录路径
     * @return 是否成功
     */
    private boolean backupFiles(String datFile, String okFile, String backupPath) {
        String currentDir = getCurrentDir();

        try {
            // 切换到下载目录
            if (!changeDir(downloadPath)) {
                logger.error("切换到下载目录失败: {}", downloadPath);
                return false;
            }

            // 标准化备份路径，提取目录名
            String normalizedBackupPath = backupPath.replaceAll("\\\\", "/");
            String backupDirName = normalizedBackupPath.substring(normalizedBackupPath.lastIndexOf("/") + 1);

            // 创建备份目录
            if (!createDirectory(backupDirName)) {
                logger.error("创建备份目录失败: {}", backupDirName);
                return false;
            }

            // 备份目标文件
            String datBackupPath = backupDirName + "/" + datFile;
            if (!moveFile(datFile, datBackupPath)) {
                logger.error("备份目标文件失败: {} -> {}", datFile, datBackupPath);
                return false;
            }

            // 备份结束文件
            String okBackupPath = backupDirName + "/" + okFile;
            if (!moveFile(okFile, okBackupPath)) {
                logger.error("备份结束文件失败: {} -> {}", okFile, okBackupPath);
                // 如果结束文件备份失败，尝试恢复目标文件
                moveFile(datBackupPath, datFile);
                return false;
            }

            logger.info("文件备份成功: {} 和 {} 已备份到 {}/{}", datFile, okFile, downloadPath, backupDirName);
            return true;

        } catch (Exception e) {
            logger.error("备份文件过程中发生异常", e);
            return false;
        } finally {
            changeDir(currentDir);
        }
    }



    /**
     * 将JSONArray转换为用|分隔的字符串
     */
    private String convertArrayToString(JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) {
            return "";
        }

        try {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                list.add(jsonArray.getString(i));
            }
            return String.join("|", list);
        } catch (Exception e) {
            logger.error("转换JSONArray为字符串失败: {}", jsonArray, e);
            return "";
        }
    }
}
