spring.application.name=exam-async-task-service
application.env.name=dev9

spring.datasource.url=********************************************************
spring.datasource.username=root
spring.datasource.password=dreamtech%IT
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.validation-query=SELECT 1
spring.datasource.initial-size=5
spring.datasource.max-active=10
spring.datasource.max-idle=5
spring.datasource.min-idle=1
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=true
spring.datasource.time-between-eviction-runs-millis=5000
spring.datasource.min-evictable-idle-time-millis=60000


spring.datasource.exam.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.exam.test-while-idle=true
spring.datasource.exam.test-on-borrow=true
spring.datasource.exam.time-between-eviction-runs-millis=5000
spring.datasource.exam.min-evictable-idle-time-millis=60000
spring.datasource.exam.validation-query=SELECT 1


spring.datasource.juhe.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.juhe.test-while-idle=true
spring.datasource.juhe.test-on-borrow=true
spring.datasource.juhe.time-between-eviction-runs-millis=5000
spring.datasource.juhe.min-evictable-idle-time-millis=60000
spring.datasource.juhe.validation-query=SELECT 1


spring.jooq.sql-dialect = mysql
logging.level.org.jooq=DEBUG

dubbo.application.name=exam-async-task-service
dubbo.application.version=1
dubbo.registry.address=zookeeper://localhost:2181
#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator



#
spring.rabbitmq.host=**************
spring.rabbitmq.port=30007
#spring.rabbitmq.host=localhost
#spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/dev
#spring.rabbitmq.host=localhost
#spring.rabbitmq.port=5672
#spring.rabbitmq.username=dev
#spring.rabbitmq.password=dev
#spring.rabbitmq.virtual-host=/dev
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

spring.rabbitmq.restful-exchange=restful.amq.fanout

#spring.data.mongodb.uri=mongodb://127.0.0.1:27017/test
spring.data.mongodb.host = **************
spring.data.mongodb.port = 30005
spring.data.mongodb.dbname = test
spring.data.mongodb.connectTimeout = 60000
spring.data.mongodb.socketTimeout = 120000
spring.data.mongodb.socketKeepAlive = true
spring.data.mongodb.maxConnectionIdleTime = 120000
spring.data.mongodb.maxConnectionLifeTime = 120000
spring.data.mongodb.connectionsPerHost = 100
spring.data.mongodb.username = admin
spring.data.mongodb.password = dreamtech



graphite.server=mw9.zhixueyun.com
graphite.port=19912

# redis
spring.redis.cluster = false
spring.redis.cluster.nodes = *************:30006
#spring.redis.cluster.nodes= ***************:30006
#spring.redis.cluster.nodes = ***********:30006
#spring.redis.cluster.nodes = ***************:30006
#spring.redis.cluster.nodes = mw9.zhixueyun.com:10301
spring.redis.timeout = 10000

# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

# name of queues
exam.message.queue.restfulLog = zxy-exam-restful-log
exam.message.queue.signup = zxy-exam-signup
exam.message.queue.exam = zxy-exam-exam
exam.message.queue.submitPaper = zxy-exam-submit-paper
exam.message.queue.grantDetail = zxy-exam-grant-detail
exam.message.queue.member = zxy-exam-member
exam.message.queue.organization.detail = zxy-exam-organization-detail
exam.message.queue.organization = zxy-exam-organization
exam.message.queue.audience.item = zxy-exam-audience-item
exam.message.queue.audience.member = zxy-exam-audience-member
exam.message.queue.schedule = zxy-exam-schedule
exam.message.queue.answer.record = zxy-exam-answer-record
exam.message.queue.researchActivity = zxy-eaxm-researchActivity
exam.message.queue.questionary-inserting = zxy-exam-questionary-inserting
exam.message.queue.message-notice = zxy-exam-message-notice
exam.message.queue.activity = zxy-exam-activity
exam.message.queue.task = zxy-exam-task
exam.message.queue.business-topic = zxy-exam-business-topic
exam.message.queue.exam.record.async = zxy-exam-record-async
exam.message.queue.exam.to.do = zxy-exam-to-do
exam.message.queue.after.audience.generate = zxy-exam-after-audience-generate
exam.message.queue.question.count = zxy-exam-question-count
exam.message.queue.invigilator = zxy-exam-invigilator
exam.message.queue.researchRecord = zxy-exam-researchRecord
exam.message.queue.researchMember = zxy-exam-research-member


# fastdfs
spring.fastdfs.connect-timeout = 30
spring.fastdfs.network-timeout = 60
spring.fastdfs.charset = utf-8
spring.fastdfs.tracker-servers = *************:10401
spring.fastdfs.tracker-http-port = 10402
spring.fastdfs.anti-steal-token = false
spring.fastdfs.secret-key = 123456


# exam localhost
#exam.csv.ftp.time.start = 01
exam.csv.ftp.ip =*************
exam.csv.ftp.port =22
exam.csv.ftp.username =online
exam.csv.ftp.password =CJ28v^pkNUkydLxZ
#MFT_HR_RCFZ_HQ_00005 èè¯æç»©
onlineExamScore.csv.ftp.dir.put =/uat/MFTS/HQ_WDtoIHR_HQ/MftXfeOnlineExamScoreSrv

szfn.sync.enabled=true
szfn.exam.id=ba300712-b4c8-4c93-af79-0199b00908fb
szfn.exam.subject.id=37202487-0bb1-4dc3-a342-73c79a02aea0
szfn.sftp.host=*************
szfn.sftp.port=22
szfn.sftp.username=devuser
szfn.sftp.password=dreamtech@123
szfn.sftp.timeout=30000
szfn.sftp.upload.path=/upload/szfn/upload/
szfn.sftp.download.path=/upload/szfn/download/
