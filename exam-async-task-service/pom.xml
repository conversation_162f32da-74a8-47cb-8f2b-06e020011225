<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>exam</artifactId>
    <groupId>com.zxy.product</groupId>
    <version>cmu-9.6.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>exam-async-task-service</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.zxy.common</groupId>
      <artifactId>async-service-parent</artifactId>
      <type>pom</type>
    </dependency>
    <dependency>
      <groupId>com.zxy.product</groupId>
      <artifactId>exam-api</artifactId>
      <version>${version}</version>
    </dependency>
    <dependency>
      <groupId>com.zxy.product</groupId>
      <artifactId>human-resource-api</artifactId>
      <version>${version}</version>
    </dependency>
    <dependency>
      <groupId>com.zxy.product</groupId>
      <artifactId>system-api</artifactId>
      <version>${version}</version>
    </dependency>
    <dependency>
      <groupId>com.zxy.product</groupId>
      <artifactId>course-study-api</artifactId>
      <version>${version}</version>
    </dependency>
    <dependency>
      <groupId>com.zxy.product</groupId>
      <artifactId>train-api</artifactId>
      <version>${version}</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.29.sec10</version>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>2.9.3</version>
    </dependency>

    <!-- PCCW 亚信依赖配置-->
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.1</version>
    </dependency>

    <dependency>
      <groupId>net.sourceforge.javacsv</groupId>
      <artifactId>javacsv</artifactId>
      <version>2.0</version>
    </dependency>

    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
      <version>0.1.53</version>
    </dependency>

    <dependency>
      <groupId>com.zxy.common</groupId>
      <artifactId>web-server-parent</artifactId>
      <type>pom</type>
      <exclusions>
        <exclusion>
          <artifactId>hibernate-validator</artifactId>
          <groupId>org.hibernate</groupId>
        </exclusion>
      </exclusions>

    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.5</version>
    </dependency>

    <dependency>
      <groupId>com.asiainfo</groupId>
      <artifactId>aiesb_sdk</artifactId>
      <version>1.0.0</version>
      <scope>system</scope>
      <systemPath>${basedir}/src/main/lib/aiesb_sdk_1.8_2020.4.16.jar</systemPath>
    </dependency>
  </dependencies>

  <build>

    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <targetPath>BOOT-INF/classes/</targetPath>
      </resource>
      <resource>
        <directory>src/main/lib</directory>
        <targetPath>BOOT-INF/lib/</targetPath>
        <includes>
          <include>**/*.jar</include>
        </includes>
      </resource>
    </resources>

    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <includeSystemScope>true</includeSystemScope>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>springloaded</artifactId>
            <version>1.2.3.RELEASE</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>

</project>
