spring.application.name=zxy-log-web-server

dubbo.application.name=zxy-log-web-server
dubbo.application.version=1
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator

spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=rabbit
spring.rabbitmq.password=rabbit
spring.rabbitmq.virtual-host=/dev
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

graphite.server=mw9.zhixueyun.com
graphite.port=19912

# fastdfs
spring.fastdfs.connect-timeout = 30
spring.fastdfs.network-timeout = 60
spring.fastdfs.charset = utf-8
spring.fastdfs.tracker-servers = mw9.zhixueyun.com:10401
spring.fastdfs.tracker-http-port = 10402
spring.fastdfs.anti-steal-token = false
spring.fastdfs.secret-key = 123456
spring.fastdfs.max-total = 5
spring.fastdfs.max-idle = 5

# redis
spring.redis.cluster = false
spring.redis.cluster.nodes = localhost:6379
spring.redis.timeout=10000
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3


# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

# temp config
server.port=10008
server.context-path=/api/v1/zxy-log
server.tomcat.max-http-post-size=20971520

# upload config
spring.http.multipart.max-file-size=2048Mb
spring.http.multipart.max-request-size=2048Mb
spring.http.multipart.file-size-threshold=0
