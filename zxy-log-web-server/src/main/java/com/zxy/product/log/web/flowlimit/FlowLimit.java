package com.zxy.product.log.web.flowlimit;

import java.util.concurrent.atomic.LongAdder;

/**
 * <AUTHOR>
 * @data 2024/3/19
 * desc 滑动时间窗口限流器
 */
public class FlowLimit {

    /**滑动时间片*/
    private LongAdder[] timeSlices;

    /**每个时间片的时长*/
    private static final long timeMillisPerSlice = 500;

    /**窗口长度*/
    private static final int windowSize = 2;

    /**上次请求的时间戳*/
    private volatile long oldTimeShip = System.currentTimeMillis()+timeMillisPerSlice;

    /**当前所使用的时间片位置*/
    private LongAdder cursor = new LongAdder();

    /**限流规则，内置的目的是减少根据业务类型的检索损耗(假设限流规则条数很多)*/
    private FlowRule flowRule;

    /**初始化滑动窗口*/
    public FlowLimit(FlowRule flowRule) {
        this.flowRule = flowRule;
        initSlidingWindow();
    }

    /**初始化滑动窗口*/
    private void initSlidingWindow() {
        LongAdder[] localTimeSlices = new LongAdder[windowSize];
        for (int i = 0; i < windowSize; i++) {
            localTimeSlices[i] = new LongAdder();
        }
        timeSlices = localTimeSlices;
    }

    /**定位当前时间片下标*/
    private int locationIndex(long time) {
        return (int) ((time / timeMillisPerSlice) % windowSize);
    }

    /**将index赋值给cursor*/
    private void longAdderSet(int index){
        LongAdder indexAdder = new LongAdder();
        indexAdder.add(index);
        cursor = indexAdder;
    }

    /**
     *  滑动时间窗口实现限流
     *  判断是否允许进行访问，未超过阈值的话才会对某个时间片+1
     */
    public boolean trySlideWindowAcquire() {
        long time = System.currentTimeMillis();
        int index = locationIndex(oldTimeShip);
        int sum = 0;
        int oldCursor = cursor.intValue();
        if (oldCursor != index || time- oldTimeShip >timeMillisPerSlice) {
            timeSlices[index].reset();
        }
        for (int i = 0; i < windowSize; i++) {
            sum+= timeSlices[i].intValue();
        }
        // 下标更新，上次请求时间戳更新
        longAdderSet(index);
        oldTimeShip = time;
        // 阈值判断
        if (sum < this.flowRule.getQps()) {
            // System.out.println("当前时间："+ new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time));
            // 未超过阈值才+1
            timeSlices[index].increment();
            return true;
        }

        return false;
    }

    public FlowRule getFlowRule() {
        return flowRule;
    }

    public void setFlowRule(FlowRule flowRule) { this.flowRule = flowRule; }
}
