package com.zxy.product.log.web.controller;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.product.log.api.WhiteDetailService;
import com.zxy.product.log.entity.WhiteRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 作者 冯春 创建于 2019/8/7.
 */
@Controller
@RequestMapping("/white-detail")
public class WhiteDetailController {
    private WhiteDetailService service;
    @Autowired
    public void setService(WhiteDetailService service) {
        this.service = service;
    }
    @RequestMapping(value = "/find", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "memberId", type = String.class, required = true)
    @Param(name = "time", type = String.class)
    @Param(name = "status", type = Integer.class, required = true)
    @JSON("recordCount")
    @JSON("items.(createTime,type,error,passwordType,source,loginStatus)")
    public PagedResult<WhiteRecord> find (RequestContext requestContext){
        PagedResult<WhiteRecord>  list=  service.find(requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),requestContext.get("memberId", String.class),
        requestContext.getOptionalString("time"),requestContext.get("status", Integer.class));
        return  list;
    }
}
