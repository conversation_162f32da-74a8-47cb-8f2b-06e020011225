package com.zxy.product.log.web.domain;

import java.io.Serializable;

/**
 * 限流规则：最大在线人数以及QPS（含核心资源）相关POJO
 * <AUTHOR>
 * @date 2025年01月09日 16:14
 */
public class CommonOnlineQpsFlowRule implements Serializable {
    private static final long serialVersionUID = -6111190347507220335L;

    /**最大在线人数*/
    private Integer maxOnline;

    /**普通资源QPS*/
    private Integer ordinaryQps;

    /**核心资源限流规则*/
    private CoreOnlineQpsFlowRule coreOnlineQpsFlowRule;

    public Integer getMaxOnline() { return maxOnline; }

    public void setMaxOnline(Integer maxOnline) { this.maxOnline = maxOnline; }

    public Integer getOrdinaryQps() { return ordinaryQps; }

    public void setOrdinaryQps(Integer ordinaryQps) { this.ordinaryQps = ordinaryQps; }

    public CoreOnlineQpsFlowRule getCoreOnlineFlowRule() { return coreOnlineQpsFlowRule; }

    public void setCoreOnlineFlowRule(CoreOnlineQpsFlowRule coreOnlineQpsFlowRule) { this.coreOnlineQpsFlowRule = coreOnlineQpsFlowRule; }

    @Override
    public String toString() {
        return "CommonOnlineQpsFlowRule{" +
                "maxOnline=" + maxOnline +
                ", ordinaryQps=" + ordinaryQps +
                ", coreOnlineQpsFlowRule=" + coreOnlineQpsFlowRule +
                '}';
    }
}
