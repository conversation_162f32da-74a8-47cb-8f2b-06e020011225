package com.zxy.product.log.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.product.log.web.flowlimit.FlowLimitKit;
import com.zxy.product.log.web.flowlimit.FlowRule;
import com.zxy.product.log.web.util.ManualRequestTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @data 2024/3/19
 */
@Controller
@RequestMapping("/flow-limit")
public class FlowLimitController {
    private Cache cache;

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create(FlowLimitKit.REDIS_MODULE_NAME);
    }

    /**
     * 查询限流规则
     */
    @RequestMapping(method = RequestMethod.GET, value = "/query-rule")
    @JSON("*")
    public List<FlowRule> queryLimitRule() {
        return FlowLimitKit.queryFlowRule();
    }

    /**
     * 改变（含添加）限流规则
     */
    @RequestMapping(method = RequestMethod.GET, value = "/change-rule")
    @Param(name = "businessType", type = String.class, required = true)
    @Param(name = "qps", type = String.class, required = true)
    @Param(name = "qpsEnable", type = String.class, required = true)
    @Param(name = "whiteBusinessIDs", type = String.class, required = false)
    @Param(name = "tk", type = String.class, required = true)
    @JSON("*")
    public ImmutableMap<String, String> changeLimitRule(RequestContext rc) {

        // 获取入参
        String requestUri = rc.getRequest().getRequestURI();
        String businessType = rc.getString("businessType");
        int qps = rc.getInteger("qps");
        boolean qpsEnable = rc.getBoolean("qpsEnable");
        String whiteBusinessIDs = rc.getOptionalString("whiteBusinessIDs").orElse("");
        String tk = rc.getString("tk");

        // 请求安全性校验
        if (!StringUtils.hasText(tk) || !ManualRequestTokenUtil.checkToken(requestUri,
                businessType+qps+qpsEnable+whiteBusinessIDs, tk)) {
            return ImmutableMap.of("result", "false");
        }

        FlowRule flowRule = new FlowRule();
        flowRule.setBusinessType(businessType);
        flowRule.setQps(qps);
        flowRule.setQpsEnable(qpsEnable);
        flowRule.setWhiteBusinessIDs(whiteBusinessIDs);
        FlowLimitKit.changeFlowRule(flowRule, cache);

        return ImmutableMap.of("result", "true");
    }

    /**
     * 清空已经设置的规则
     */
    @RequestMapping(method = RequestMethod.GET, value = "/clear-rule")
    @Param(name = "tk", type = String.class, required = false)
    @JSON("*")
    public ImmutableMap<String, String> clearLimitRule(RequestContext rc) {
        String requestUri = rc.getRequest().getRequestURI();
        String tk = rc.getOptionalString("tk").orElse("");

        if (StringUtils.hasText(tk) && ManualRequestTokenUtil.checkToken(requestUri, "", tk)) {
            cache.clear(FlowLimitKit.REDIS_KEY);
            return ImmutableMap.of("result", "true");
        }

        return ImmutableMap.of("result", "false");
    }

    /**
     * 根据businessType校验是否允许访问
     */
    @RequestMapping(method = RequestMethod.GET, value = "/validate")
    @Param(name = "businessType", type = String.class, required = true)
    @Param(name = "businessID", type = String.class, required = false)
//    @Permitted 去掉token校验，提升接口并发能力。
    @JSON("*")
    public ImmutableMap<String, String> validateAllowAccess(RequestContext rc) {
        // 获取入参
        String businessType = rc.getString("businessType");
        String businessID = rc.getOptionalString("businessID").orElse("");

        if (FlowLimitKit.validateAllowAccess(businessType, businessID)) {
            return ImmutableMap.of("result", "true");
        };

        return ImmutableMap.of("result", "false");
    }

}
