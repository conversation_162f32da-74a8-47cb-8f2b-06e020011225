package com.zxy.product.log.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.cache.redis.RedisCacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.support.oauth.AccessToken;
import com.zxy.common.serialize.Serializer;
import com.zxy.product.human.api.WhiteService;
import com.zxy.product.human.api.WhiteTemporaryService;
import com.zxy.product.human.content.MessageHeaderContent;
import com.zxy.product.human.content.MessageTypeContent;
import com.zxy.product.human.entity.Member;
import com.zxy.product.human.entity.White;
import com.zxy.product.log.api.OnlineInfoService;
import com.zxy.product.log.api.OnlineStatisticsService;
import com.zxy.product.log.content.CacheKeyConstant;
import com.zxy.product.log.content.ErrorCode;
import com.zxy.product.log.entity.OnlineStatistics;
import com.zxy.product.log.web.util.DateUtil;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/online-info")
public class OnlineInfoController {
    private static final Logger LOGGER = LoggerFactory.getLogger(OnlineInfoController.class);

    private OnlineInfoService onlineInfoService;
    private OnlineStatisticsService onlineStatisticsService;
    private Cache cache;
    private Cache requestIdCache;
    private Cache oauthCache;

    private static final String ONLINE_COUNT = "online-count";
    private static final String DISPLAY_ONLINE_COUNT = "display_online-count";
    private static final String TODAY_LOGIN_COUNT = "today-login-count";
    private static final String ONLINE_STATISTICS = "online-statistics";
    private static final String ROOT_ORGANIZATION_ID = "1";
    private static final String SECRET = "WD@2022";

    private final static Long REQUEST_TIMEOUT_MILLS = 1 * 60 * 1000L;
    private final static int REQUEST_ID_CACHE_TIME = 1 * 60;

    private Redis redis;
    private WhiteService whiteService;
    private WhiteTemporaryService whiteTemporaryService;
    private MessageSender messageSender;

    public static final String OAUTH_PROVIDER_APPLICATION_NAME = "oauth-provider";
    public static final String OAUTH_PROVIDER_MODULE_NAME = "oauth";


    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setOauthRedisCacheService(Redis redis, Serializer ser) {
        RedisCacheService redisCacheService = new RedisCacheService(OAUTH_PROVIDER_APPLICATION_NAME);
        redisCacheService.setRedis(redis);
        redisCacheService.setSerializer(ser);
        this.oauthCache = redisCacheService.create(OAUTH_PROVIDER_MODULE_NAME);
    }

    @Autowired
    public void setWhiteService(WhiteService whiteService) {
        this.whiteService = whiteService;
    }


    @Autowired
    public void setWhiteTemporaryService(WhiteTemporaryService whiteTemporaryService) {
        this.whiteTemporaryService = whiteTemporaryService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setOnlineInfoService(OnlineInfoService onlineInfoService) {
		this.onlineInfoService = onlineInfoService;
	}

    @Autowired
    public void setOnlineStatisticsService(OnlineStatisticsService onlineStatisticsService) {
        this.onlineStatisticsService = onlineStatisticsService;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("online-info");
        this.requestIdCache = cacheService.create("request-id");
    }

	@RequestMapping(value = "/online-count", method = RequestMethod.GET)
    @Param(name = "handler", type = Integer.class)
    @JSON("*")
    public Integer getOnlineCount(RequestContext context) {
        Integer onlineCount = cache.get(ONLINE_COUNT, Integer.class);
        if(onlineCount == null || onlineCount ==0){
            return 35000;
        }
        return onlineCount;
    }

    /**
     * 此方法仅用于驾驶舱数据大屏
     */
    @RequestMapping(value = "/display/online-count", method = RequestMethod.GET)
    @JSON("*")
    @Param(name = "handler", type = Integer.class)
    public Integer displayOnlineCount(RequestContext context) {
        Integer onlineCount = cache.get(ONLINE_COUNT, Integer.class);
        if(onlineCount == null || onlineCount ==0){
            return 35000;
        }
        return onlineCount;
    }

    /**
     * 此方法仅用于驾驶舱数据大屏
     */
    @RequestMapping(value = "/smart-campus/online-count", method = RequestMethod.GET)
    @JSON("*")
    public Integer displayOnlineCountSmartCampus() {
        Integer onlineCount = cache.get(ONLINE_COUNT, Integer.class);
        if(onlineCount == null || onlineCount ==0){
            return 0;
        }
        return onlineCount;
    }

	@RequestMapping(value = "/today-login-count", method = RequestMethod.GET)
    @Param(name = "handler", type = Integer.class)
    @JSON("*")
    public Integer getTodayLoginCount(RequestContext context) {
        return cache.get(TODAY_LOGIN_COUNT, ()->{
            return onlineInfoService.getTodayLoginCount();
        }, 900);
    }



    /**
     * 手动重置在线人数缓存（可指定在线人数）
     */
    @RequestMapping(value = "/reset-online-count", method = RequestMethod.GET)
    @Param(name = "requestId", required = true)
    @Param(name = "timestamp", type = Long.class, required = true)
    @Param(name = "sign", required = true)
    @Param(name = "onlineCount", type = Integer.class) // 指定在线人数
    @JSON("*")
    public Map<String, String> reSetOnlineCount(RequestContext context) {

        // 验证加密
        String message= this.check(context);
        if (!"成功".equals(message)) {
            return ImmutableMap.of("fail", message);
        }

        // 指定在线人数
        if (context.getOptional("onlineCount", Integer.class).isPresent()) {
            Integer onlineCount = context.getOptional("onlineCount", Integer.class).get();
            cache.set(ONLINE_COUNT,onlineCount);
            return ImmutableMap.of("success", "已指定在线人数："+onlineCount);
        }

        int count = onlineInfoService.getOnlineCount(ROOT_ORGANIZATION_ID, Optional.empty(), Optional.empty());
        cache.set(ONLINE_COUNT, count);
        return ImmutableMap.of("success", "已查询在线人数："+count);

    }

    /**
     * 手动强制踢人
     * @num 清理的token数量
     */
    @RequestMapping(value = "/clean-tokens", method = RequestMethod.GET)
    @Param(name = "requestId", required = true)
    @Param(name = "timestamp", type = Long.class, required = true)
    @Param(name = "num", type = Integer.class, required = true)
    @Param(name = "sign", required = true)
    @JSON("*")
    public Map<String, Object> authorize(RequestContext context) {
        // 验证加密
        String message= this.check(context);
        if (!"成功".equals(message)) {
            return ImmutableMap.of("fail", message);
        }
        Integer num = context.getInteger("num");
        if (num <= 0) return ImmutableMap.of("fail", "num <= 0");

        //查询所有白名单账号
        List<String> whiteUserIds = whiteService.find(1, 1000000, new ArrayList<>(), Optional.empty(), Optional.empty(), Optional.empty(), 0)
                .getItems().stream().map(White::getMember).collect(Collectors.toList()).stream().map(Member::getId).collect(Collectors.toList());
        //查询所有临时白名单账号
//        Set<String> whiteUserTempIds = whiteTemporaryService.findMemberIds(ROOT_ORGANIZATION_ID);
        // 把临时的白名单ID和正常的放到一起过滤
//        whiteUserIds.addAll(whiteUserTempIds);
//        LOGGER.error("白名单账号size： " + whiteUserIds.size());
        //获取所有oauth的key
        List<String> oauthKeys = redis.keys("oauth-provider#oauth#????????????????????????????????_SUBJECT", false, false);
        LOGGER.error("oauthKeys's size is ：" + oauthKeys.size() + ",first is：" + oauthKeys.get(0));
        //全部的token+userid
        Map<String, String> allTokenUserMap = new HashMap<>();
        oauthKeys.stream().forEach(key -> {
                    if (key.endsWith("_SUBJECT")) {
                        String lastStr = key.split("#")[2];
                        String token = lastStr.split("_")[0];
                        AccessToken accessToken =  oauthCache.get(token, AccessToken.class);
                        if(accessToken!=null)
                            allTokenUserMap.put(token, oauthCache.get(token, AccessToken.class).getUserId());
                    }
                }
        );
        if (allTokenUserMap.isEmpty()) {
            return ImmutableMap.of("success", "早已被全部清理");
        }
        List<String> onlineUsers = new ArrayList<>(allTokenUserMap.values());
        LOGGER.error("当前在线人的userid size： " + onlineUsers.size());
        //去掉白名单的人
        List<String> clearUsers = onlineUsers.stream().filter(usr -> !whiteUserIds.contains(usr)).collect(Collectors.toList());
        LOGGER.error("去掉白名单后的clearUsers size： " + clearUsers.size());
        //token+剩余到期秒数
        Map<String, Long> expiresInMap = new HashMap<>();
        for (Map.Entry<String, String> m : allTokenUserMap.entrySet()) {
            if (clearUsers.contains(m.getValue()))
                expiresInMap.put(m.getKey(), redis.process(x -> x.ttl("oauth-provider#oauth#" + m.getKey())));
        }
        LOGGER.error("expiresInMap's size is : {},  first key is :{} , value is :{}", expiresInMap.size(),
                expiresInMap.entrySet().iterator().next().getKey(), expiresInMap.entrySet().iterator().next().getValue());
        //按照剩余过期秒数正序排序并且取前num个。
        LinkedHashMap<String, Long> sortedMap = expiresInMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).limit(num)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
        LOGGER.error("sortedMap size :{}",sortedMap.size());
        for (Map.Entry<String, Long> m : sortedMap.entrySet()) {
            AccessToken accessToken = oauthCache.get(m.getKey(),AccessToken.class);
            if (accessToken != null){
                LOGGER.error("sortedMap del key :{} , del time :{},userId :{}", m.getKey(), m.getValue(), accessToken.getUserId());
                messageSender.send(MessageTypeContent.OAUTH_LOGOUT, MessageHeaderContent.MEMBER_ID, accessToken.getUserId(), MessageHeaderContent.TYPE, "1");
                messageSender.send(MessageTypeContent.OAUTH_LOGOUT, MessageHeaderContent.MEMBER_ID, accessToken.getUserId(), MessageHeaderContent.TYPE, "2");
                oauthCache.clear(m.getKey());
                oauthCache.clear(m.getKey() + "_SUBJECT");
            }
        }
        return ImmutableMap.of("success", "已清理"+num+"个");
    }

    @RequestMapping(value = "/online-statistics", method = RequestMethod.GET)
    @Param(name = "day", type = Integer.class)
    @JSON("*")
    public List<OnlineStatistics> getOnlineStatistics(RequestContext context) {
        int day = context.getOptionalInteger("day").orElse(DateUtil.currentDateTime("yyyyMMdd"));
        if (!DateUtil.checkDate(day)) {
            throw new UnprocessableException(ErrorCode.DateCheckFailed);
        }
        return cache.get(ONLINE_STATISTICS + "#" + day, ()->{
            return onlineStatisticsService.list(day);
        }, 900);
    }

    /**
     * 验证加密
     */
    private String check(RequestContext context) {
        String requestId = context.getString("requestId");
        Long timestamp = context.getLong("timestamp");
        String sign = context.getString("sign");
        // 防止重放攻击
        if (Math.abs(System.currentTimeMillis() - timestamp) > REQUEST_TIMEOUT_MILLS) {
            return "timestamp无效";
        }

        // 验证requestId
        if (StringUtils.isEmpty(requestIdCache.get(requestId, String.class))) {
            requestIdCache.set(requestId, requestId, REQUEST_ID_CACHE_TIME);
        } else {
            return "requestId无效";
        }

        // 验签
        StringBuilder sb = new StringBuilder();
        sb.append("requestId").append("=").append(requestId).append("&");
        sb.append("timestamp").append("=").append(timestamp).append("&");
        sb.append("secret").append("=").append(SECRET);
        String _sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes()).toUpperCase();
        if (!sign.equals(_sign)) {
            return "验签失败";
        }

        return "成功";

    }


    @RequestMapping(value = "/test-add-mongo",method = RequestMethod.POST)
    @Param(name = "name") //集合名
    @Param(name = "value")//值
    @Param(name = "valueName")//值名
    @JSON("*")
    public Integer testAddMongo(RequestContext requestContext){
        onlineInfoService.testAddMongo(requestContext.getString("valueName"),
                requestContext.getString("value"),
                requestContext.getString("name"));

        return 1;
    }


    @RequestMapping(value = "/test-query-mongo",method = RequestMethod.GET)
    @Param(name = "name")//集合名
    @Param(name = "valueName")//值名
    @JSON("*")
    public List<String> testQueryMongo(RequestContext requestContext){

       return onlineInfoService.testQueryMongo(
                requestContext.getString("name"),
                requestContext.getString("valueName")
        );
    }

    @RequestMapping(value = "/login-page-statistics", method = RequestMethod.GET)
    @JSON("*")
    public Map<String, Integer> loginPageStatistics() {
        int todayLoginMemberCount = cache.get(CacheKeyConstant.TODAY_LOGIN_MEMBER_COUNT, () -> onlineInfoService.getTodayLoginMemberCount(), 30 * 60);
        int todayLoginCount = cache.get(CacheKeyConstant.TODAY_LOGIN_COUNT, () -> onlineInfoService.getTodayLoginCount(), 30 * 60);
        int yearLoginCount = cache.get(CacheKeyConstant.YEAR_LOGIN_COUNT, () -> 12000000);

        Map<String, Integer> result = new HashMap<>();
        result.put("todayLoginMemberCount", todayLoginMemberCount);
        result.put("todayLoginCount", todayLoginCount);
        result.put("yearLoginCount", yearLoginCount);
        return result;
    }

}
