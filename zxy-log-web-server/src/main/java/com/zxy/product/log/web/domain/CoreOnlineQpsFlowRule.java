package com.zxy.product.log.web.domain;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年01月09日 16:40
 */
public class CoreOnlineQpsFlowRule {
    private static final long serialVersionUID = -4013411731628067938L;

    /**核心业务Id集合（例如反腐的课程Id）*/
    private List<String> coreBusinessCollect;

    /**核心业务Id的最大在线人数*/
    private Integer maxOnline;

    /**核心资源QPS*/
    private Integer coreQps;

    public Integer getMaxOnline() { return maxOnline; }

    public void setMaxOnline(Integer maxOnline) { this.maxOnline = maxOnline; }

    public List<String> getCoreBusinessCollect() { return coreBusinessCollect; }

    public void setCoreBusinessCollect(List<String> coreBusinessCollect) { this.coreBusinessCollect = coreBusinessCollect; }

    public Integer getCoreQps() { return coreQps; }

    public void setCoreQps(Integer coreQps) { this.coreQps = coreQps; }

    @Override
    public String toString() {
        return "CoreOnlineQpsFlowRule{" +
                "coreBusinessCollect=" + coreBusinessCollect +
                ", maxOnline=" + maxOnline +
                ", coreQps=" + coreQps +
                '}';
    }
}
