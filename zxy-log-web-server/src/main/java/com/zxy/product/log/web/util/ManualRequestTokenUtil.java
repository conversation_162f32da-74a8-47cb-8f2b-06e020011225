package com.zxy.product.log.web.util;

import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 针对开发以运维目的手工调用rest接口的验证工具类
 */
public class ManualRequestTokenUtil {
    /** 基础密钥，作为动态密钥的一部分 */
    private static final String BASE_SECRET_KEY = "bd056d1e2194ebcc7235048feec1b58f";

    /** 基础密钥，在构建动态密钥时，拆分的下标 */
    private static final int BASE_SECRET_KEY_SPLIT_INDEX = 5;

    /**
     * 请求token校验
     * @param requestUri 请求的URI
     * @param requestParam 请求中参数（多个参数需要拼接，和生成token时一致）
     * @param requestToken 请求中携带的token
     * @return true 通过，false 不通过
     */
    public static Boolean checkToken(String requestUri, String requestParam, String requestToken) {
        if (!StringUtils.hasText(requestToken)) {
            return false;
        }

        // 基于请求参数和当前的密钥生成token，并校验和请求中的是否一致
        return buildToken(requestUri, requestParam).equals(requestToken);
    }

    /**
     * 基于请求参数和动态密钥生成一个token
     * @return token
     */
    private static String buildToken(String requestUri, String requestParam) {
        // 基于请求参数和当前的密钥生成token
        return DigestUtils.md5DigestAsHex((requestUri + requestParam + buildSecretKey()).getBytes()).toUpperCase();
    }

    /**
     * 获取当前时间戳（精确到分钟）
     * @return 当前时间的分钟级时间戳（例如：1640703000 for "2021-12-31 23:50"）
     */
    private static long getCurrentTimeInMinutesSinceEpoch() {
        return System.currentTimeMillis() / (1000 * 60);
    }

    /**
     * 生成一个密钥，其由基础密钥和当前时间戳(精确到分钟)组成
     * @return 生成的密钥，每分钟都不同
     */
    private static String buildSecretKey(){
        return BASE_SECRET_KEY.substring(0, BASE_SECRET_KEY_SPLIT_INDEX)
                + getCurrentTimeInMinutesSinceEpoch()
                + BASE_SECRET_KEY.substring(BASE_SECRET_KEY_SPLIT_INDEX);
    }

    /**
     * 生成一个token
     */
    /*public static void main(String[] args) {
        // FlowLimitController.changeLimitRule 改变（含添加）限流规则
        String requestUri = "/api/v1/zxy-log/flow-limit/change-rule";
        String businessType = "1"; // 根据实际参数修改
        String qps = "3"; // 根据实际参数修改
        String qpsEnable = "true"; // 根据实际参数修改
        String whiteBusinessIDs = "aaa,bbb,ccc"; // 根据实际参数修改
        String requestParam = businessType + qps + qpsEnable + whiteBusinessIDs;

        // UriInterceptorController.clearLimitRule 清理所有限流规则
        // String requestUri = "/api/v1/zxy-log/flow-limit/clear-rule";
        // String requestParam = ""; // 该接口无参数


        System.out.println(buildToken(requestUri, requestParam));
    }*/
}
