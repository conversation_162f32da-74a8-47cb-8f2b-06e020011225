package com.zxy.product.log.web.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.zxy.common.restful.security.interceptor.PermissionCheckInterceptor;

/**
 * <AUTHOR>
 *
 */
@Configuration
public class WebConfig extends WebMvcConfigurerAdapter {

	private PermissionCheckInterceptor permissionCheckInterceptor;

	@Autowired
    public void setPermissionCheckInterceptor(PermissionCheckInterceptor permissionCheckInterceptor) {
        this.permissionCheckInterceptor = permissionCheckInterceptor;
    }

    @Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(permissionCheckInterceptor);
	}

	@Bean
	public PermissionCheckInterceptor permissionCheckInterceptor(){
	    return new PermissionCheckInterceptor();
	}

}
