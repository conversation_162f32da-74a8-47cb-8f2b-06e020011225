package com.zxy.product.log.web.flowlimit;


import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2024/3/19
 * desc 限流规则类
 */
public class FlowRule implements Serializable {
    /**资源限制类型*/
    private String businessType;

    /**时间窗口内限流数量*/
    private Integer qps;

    /**qps开关 false-关闭 true-开启*/
    private Boolean qpsEnable;

    /**白名单信息*/
    private String whiteBusinessIDs;


    public String getBusinessType() { return businessType; }

    public void setBusinessType(String businessType) { this.businessType = businessType; }

    public Integer getQps() { return qps; }

    public void setQps(Integer qps) { this.qps = qps; }

    public Boolean getQpsEnable() { return qpsEnable; }

    public void setQpsEnable(Boolean qpsEnable) { this.qpsEnable = qpsEnable; }

    public String getWhiteBusinessIDs() { return whiteBusinessIDs; }

    public void setWhiteBusinessIDs(String whiteBusinessIDs) { this.whiteBusinessIDs = whiteBusinessIDs; }

    @Override
    public String toString() {
        return "FlowRule{" +
                "businessType='" + businessType + '\'' +
                ", qps=" + qps +
                ", qpsEnable=" + qpsEnable +
                ", whiteBusinessIDs='" + whiteBusinessIDs + '\'' +
                '}';
    }
}
