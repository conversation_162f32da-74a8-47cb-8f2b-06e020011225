package com.zxy.product.log.web.domain;

/**
 * <AUTHOR>
 * @date 2025年01月09日 16:42
 */
public class FlowRuleConfig {
    private static final long serialVersionUID = -6525610358508912184L;

    /**应用名*/
    private String applicationName;

    /**模块级别开关 0关闭 1开启*/
    private Integer mainSwitch;

    /**限流资源|业务类型*/
    private Integer businessType;

    /**限流规则POJO*/
    private Object flowRule;

    public String getApplicationName() { return applicationName; }

    public void setApplicationName(String applicationName) { this.applicationName = applicationName; }

    public Integer getMainSwitch() { return mainSwitch; }

    public void setMainSwitch(Integer mainSwitch) { this.mainSwitch = mainSwitch; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public Object getFlowRule() { return flowRule; }

    public void setFlowRule(Object flowRule) { this.flowRule = flowRule; }

    @Override
    public String toString() {
        return "FlowRuleConfig{" +
                "applicationName='" + applicationName + '\'' +
                ", mainSwitch=" + mainSwitch +
                ", businessType=" + businessType +
                ", flowRule=" + flowRule +
                '}';
    }
}
