package com.zxy.product.log.web.domain;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年01月09日 16:41
 */
public class CoreQpsFlowRule {
    private static final long serialVersionUID = 1769461895084140454L;

    /**核心业务Id集合（例如反腐的课程Id）*/
    private List<String> coreBusinessCollect;

    /**核心资源QPS*/
    private Integer coreQps;

    public List<String> getCoreBusinessCollect() { return coreBusinessCollect; }

    public void setCoreBusinessCollect(List<String> coreBusinessCollect) { this.coreBusinessCollect = coreBusinessCollect; }

    public Integer getCoreQps() { return coreQps; }

    public void setCoreQps(Integer coreQps) { this.coreQps = coreQps; }

    @Override
    public String toString() {
        return "CoreQpsFlowRule{" +
                "coreBusinessCollect=" + coreBusinessCollect +
                ", coreQps=" + coreQps +
                '}';
    }
}
