package com.zxy.product.log.web.domain;

/**
 * <AUTHOR>
 * @date 2025年01月09日 16:40
 */
public class CommonQpsFlowRule {
    private static final long serialVersionUID = -1558021873755097129L;

    /**普通资源QPS*/
    private Integer ordinaryQps;

    /**核心资源限流规则*/
    private CoreQpsFlowRule coreQpsFlowRule;

    public Integer getOrdinaryQps() { return ordinaryQps; }

    public void setOrdinaryQps(Integer ordinaryQps) { this.ordinaryQps = ordinaryQps; }

    public CoreQpsFlowRule getCoreQpsFlowRule() { return coreQpsFlowRule; }

    public void setCoreQpsFlowRule(CoreQpsFlowRule coreQpsFlowRule) { this.coreQpsFlowRule = coreQpsFlowRule; }

    @Override
    public String toString() {
        return "CommonQpsFlowRule{" +
                "ordinaryQps=" + ordinaryQps +
                ", coreQpsFlowRule=" + coreQpsFlowRule +
                '}';
    }


}
