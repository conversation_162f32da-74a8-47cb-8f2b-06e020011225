package com.zxy.product.log.web.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by tianc on 2019/4/3.
 */
public class DateUtil {
    /**
     * 获取当前日期时间
     * @param pattern 模式
     * @return int值
     */
    public static int currentDateTime(String pattern){
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        Date date = new Date();
        return Integer.parseInt(sdf.format(date));
    }

    /**
     * 获取当天0点的时间戳
     * @return  时间戳
     */
    public static long getTodayZeroTimeStamp() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * 检查年月日是否合法
     * @param day
     * @return
     */
    public static boolean checkDate(int day) {
        if (day < 20170801 || day > currentDateTime("yyyyMMdd")) {
            return false;
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        try {
            Date date = format.parse(day+"");
            if (!format.format(date).equals(day+"")) {
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 将时间转化为时间戳
     * @param day
     * @return
     */
    public static long date2Timestamp(String day){

        return LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();

    }

}
