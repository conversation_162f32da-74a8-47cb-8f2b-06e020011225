package com.zxy.product.log.web.flowlimit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.cache.Cache;
import com.zxy.product.log.web.domain.*;
import com.zxy.product.system.entity.RuleConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.log.content.FlowLimitConstant.CacheKeyRuleConfigSuffix;
import static com.zxy.product.log.content.FlowLimitConstant.FlowLimitEnable;
import static com.zxy.product.log.content.enums.FlowLimitEnum.*;

/**
 * <AUTHOR>
 * @data 2024/3/19
 * desc 限流核心逻辑片
 */
public class FlowLimitKit {
    private static final Logger logger= LoggerFactory.getLogger(FlowLimitKit.class);
    public static final String REDIS_MODULE_NAME = "flow-Limit";
    public static final String REDIS_KEY = "flow-rule";

    /** 业务类型 与 滑动时间窗口限流器  */
    private static final Map<String, FlowLimit> businessTypeWithFlowLimit = new HashMap<>();

    /**
     * 修改(含增加)限流规则，仅更新redis中规则，各web服务依赖定时任务拉取更新(CommonTask)
     * @param flowRule 限流规则
     * @param cache 需要使用FlowLimitKit.REDIS_MODULE_NAME初始化
     */
    public static void changeFlowRule(FlowRule flowRule, Cache cache){
        // 从redis读取当前的规则
        List<FlowRule> flowRuleList = cache.get(FlowLimitKit.REDIS_KEY, List.class);
        if (flowRuleList == null) flowRuleList = new ArrayList<>();

        // 限流规则中businessType只允许有一个
        boolean isNewFlowRule = true;
        for (int i = 0; i < flowRuleList.size(); i++) {
            if (flowRuleList.get(i).getBusinessType().equals(flowRule.getBusinessType())){
                flowRuleList.set(i, flowRule); // 传入的规则为之前已有的，需要修改
                isNewFlowRule = false;
                break;
            }
        }
        // 传入的规则为之前没有的，需要增加
        if (isNewFlowRule) flowRuleList.add(flowRule);

        // 更新redis缓存，目的为让所有pod可加载新规则
        cache.set(FlowLimitKit.REDIS_KEY, flowRuleList);
    }

    /**
     * 加载并封装QPS的限流规则
     * @param cache 需要使用FlowLimitKit.REDIS_MODULE_NAME初始化
     */
    public static void loadFlowRule(Cache cache){
        RuleConfig ruleConfig = cache.get(CacheKeyRuleConfigSuffix, RuleConfig.class);
        logger.info("从缓存中获取的限流规则数据体{}", JSONObject.toJSONString(ruleConfig));
        EchoLimitRuleConfig echoLimitRuleConfig = JSONObject.parseObject(ruleConfig.getValue(), EchoLimitRuleConfig.class);
        Integer mainSwitch = echoLimitRuleConfig.getMainSwitch();
        if(Objects.equals(mainSwitch,FlowLimitEnable)){
            List<FlowRuleConfig> ruleCollect = echoLimitRuleConfig.getRules();
            ruleCollect.forEach(FlowLimitKit::doLoadFlowRule);
        }else{
            businessTypeWithFlowLimit.clear();
        }
    }

    /**
     * 执行加载|转化内存限流对象
     * @param ruleConfig 根据业务类型获取的限流规则数据
     */
    private static void doLoadFlowRule(FlowRuleConfig ruleConfig){
        Integer businessType = ruleConfig.getBusinessType();
        String ruleStr = JSON.toJSONString(ruleConfig.getFlowRule());
        if(keyQpsCollect().contains(businessType)){
            QpsFlowRule rateRule = JSONObject.parseObject(ruleStr, QpsFlowRule.class);
            createAndStoreFlowRule(businessType, rateRule.getRequestQps(), ruleConfig.getMainSwitch(),"");

        }
        if(keyCoreQpsCollect().contains(businessType)){
            CommonQpsFlowRule rateRule = JSONObject.parseObject(ruleStr, CommonQpsFlowRule.class);
            CoreQpsFlowRule coreQpsFlowRule = rateRule.getCoreQpsFlowRule();
            createAndStoreFlowRule(businessType, rateRule.getOrdinaryQps(), ruleConfig.getMainSwitch(),
                    String.join(",", coreQpsFlowRule.getCoreBusinessCollect()));
        }
        if (keyCoreOnlineQpsCollect().contains(businessType)){
            CommonOnlineQpsFlowRule rateRule = JSONObject.parseObject(ruleStr, CommonOnlineQpsFlowRule.class);
            CoreOnlineQpsFlowRule coreOnlineQpsFlowRule = rateRule.getCoreOnlineFlowRule();
            createAndStoreFlowRule(businessType, rateRule.getOrdinaryQps(), ruleConfig.getMainSwitch(),
                    String.join(",", coreOnlineQpsFlowRule.getCoreBusinessCollect()));
        }
    }

    /**
     * 创建并保存限流规则
     * @param businessType 业务类型
     * @param qps 限流规则中的QPS配置数
     * @param mainSwitch 主开关
     * @param whiteBusinessIdCollect 资源白名单（核心资源）
     */
    private static void createAndStoreFlowRule(Integer businessType, int qps, int mainSwitch, String whiteBusinessIdCollect) {
        FlowRule flowRule = new FlowRule();
        flowRule.setBusinessType(String.valueOf(businessType));
        flowRule.setQps(qps);
        flowRule.setQpsEnable(Objects.equals(1, mainSwitch));
        flowRule.setWhiteBusinessIDs(whiteBusinessIdCollect);
        businessTypeWithFlowLimit.put(String.valueOf(businessType), new FlowLimit(flowRule));
    }

    /**
     * 校验业务是否限流
     * @param businessType 业务类型
     * @return 是否允许访问
     */
    public static Boolean validateAllowAccess(String businessType, String businessID){
        // 获取已经存在的限流规则
        FlowLimit flowLimit = businessTypeWithFlowLimit.get(businessType);

        // 没有限流规则 不限流
        if (flowLimit == null) return true;

        // 限流规则关闭 不限流
        FlowRule flowRule = flowLimit.getFlowRule();
        if (flowRule == null || !flowRule.getQpsEnable()) return true;

        // 频次校验
        boolean allowAccess = flowLimit.trySlideWindowAcquire();

        // 白名单校验
        if (!allowAccess && !StringUtils.isEmpty(businessID)) {
            String whiteBusinessIDs = flowRule.getWhiteBusinessIDs();
            if (!StringUtils.isEmpty(whiteBusinessIDs) && whiteBusinessIDs.contains(businessID)) {
                allowAccess = true; // 白名单不限流
            }
        }

        return allowAccess;
    }

    /**
     * 查询所有限流规则
     */
    public static List<FlowRule> queryFlowRule(){
        List<FlowRule> flowRules = businessTypeWithFlowLimit.values().stream().map(FlowLimit::getFlowRule).collect(Collectors.toList());
        logger.info("查询内存中的限流规则数据{}",JSON.toJSONString(flowRules));
        return flowRules;
    }

}
