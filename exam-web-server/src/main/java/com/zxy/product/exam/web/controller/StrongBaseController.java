package com.zxy.product.exam.web.controller;

import com.alibaba.dubbo.common.URL;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.Validator;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.validator.RequiredValidator;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.subAuthenticated.SubAuthenticatedService;
import com.zxy.product.exam.api.CertificateRecordService;
import com.zxy.product.exam.api.MemberService;
import com.zxy.product.exam.api.PaperClassService;
import com.zxy.product.exam.api.StrongBaseService;
import com.zxy.product.exam.api.sequence.business.CertificateNormalCodeGenerator;
import com.zxy.product.exam.content.ErrorCode;
import com.zxy.product.exam.entity.*;
import com.zxy.product.exam.util.DesensitizationUtil;
import com.zxy.product.exam.util.EncryptUtil;
import com.zxy.product.exam.web.util.BrowserUtil;
import com.zxy.product.exam.web.util.UploadUtil;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.system.api.permission.GrantService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;



/**
 * 强基计划-考试功能
 */
@Controller
@RequestMapping("/strong-base")
public class StrongBaseController {

    private static final Logger log = LoggerFactory.getLogger(StrongBaseController.class);

    private StrongBaseService strongBaseService;
    private GrantService grantService;
    private FileService fileService;
    private AttachmentResolver attachmentResolver;
    private MemberService memberService;
    private CertificateRecordService certificateRecordService;
    /** 普通考试证书编号生成器 */
    private CertificateNormalCodeGenerator certificateNormalCodeGenerator;
    private PaperClassService paperClassService;
    private SubAuthenticatedService subAuthenticatedService;

    @Autowired
    public void setSubAuthenticatedService(SubAuthenticatedService subAuthenticatedService) {
        this.subAuthenticatedService = subAuthenticatedService;
    }

    @Autowired
    public void setPaperClassService(PaperClassService paperClassService) {
        this.paperClassService = paperClassService;
    }

    @Autowired
    public void setCertificateNormalCodeGenerator(CertificateNormalCodeGenerator certificateNormalCodeGenerator) {
        this.certificateNormalCodeGenerator = certificateNormalCodeGenerator;
    }

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }


    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setGrantService(GrantService grantService) {
        this.grantService = grantService;
    }

    @Autowired
    public void setStrongBaseService(StrongBaseService strongBaseService) {
        this.strongBaseService = strongBaseService;
    }

    /**
     * 强基计划考试列表查询：
     * 仅展示报名中、未开始状态的普通考试，且已被其他考试或本考试组引用的考试不展示(过滤传过来的考试ids)
     */
    @RequestMapping(method= RequestMethod.POST)
    @JSON("recordCount")
    @JSON("items.(id,name,status,organizationName)")
    @Param(name="page", type=Integer.class, required = true)
    @Param(name="pageSize", type=Integer.class, required = true)
    @Param(name="uri", required = true)
    @Param(name="subId") // 子认证id
    @Param(name="name")
    @Param(name="status", type=Integer.class)
    @Param(name="organizationId")
    @Param(name="examIds")
    @Permitted()
    public PagedResult<Exam> findPagedResult(RequestContext requestContext, Subject<Member> subject) {
        String uri = requestContext.getString("uri");
        Map<String, Set<String>> grantOrganizationPathMap = grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), uri, subject.getRootOrganizationId());
        return strongBaseService.find(
                requestContext.get("page",Integer.class),
                requestContext.get("pageSize",Integer.class),
                grantOrganizationPathMap,
                requestContext.getOptional("name",String.class),
                requestContext.getOptionalInteger("status"),
                requestContext.getOptionalString("organizationId"),
                requestContext.getOptionalString("examIds"),
                requestContext.getOptionalString("subId")
        );
    }


    /**
     * 强基计划-管理-考试证书列表查询
     */
    @RequestMapping(value = "/certificate-list", method = RequestMethod.GET)
    @JSON("recordCount")
    @JSON("items.(id,accessType,issueTime,examId,memberId,score)")
    @JSON("items.exam.(name)")
    @JSON("items.member.(id,name,fullName)")
    @JSON("items.member.organization.(id,name)")
    @Param(name="page", type=Integer.class, required = true)
    @Param(name="pageSize", type=Integer.class, required = true)
    @Param(name="id", required = true) // 子认证专区id
    @Param(name="uri", required = true)
    @Param(name="name")
    @Param(name="fullName")
    @Param(name="organizationId")
    @Param(name="examName")
    @Param(name="accessType") // 获取方式0自考1免试
    @Permitted(perms = "auth-management/auth-child-management")
    public PagedResult<CertificateRecord> findCertificateRecordPagedResult(RequestContext requestContext, Subject<Member> subject) {
        String uri = requestContext.getString("uri");
        Map<String, Set<String>> grantOrganizationPathMap = grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), uri, subject.getRootOrganizationId());
        PagedResult<CertificateRecord> certificateRecordPagedResult = strongBaseService.findCertificateRecordPagedResult(
                requestContext.get("page", Integer.class),
                requestContext.get("pageSize", Integer.class),
                requestContext.get("id", String.class),
                grantOrganizationPathMap,
                requestContext.getOptional("name", String.class),
                requestContext.getOptional("fullName", String.class),
                requestContext.getOptionalString("organizationId"),
                requestContext.getOptionalInteger("accessType"),
                requestContext.getOptionalString("examName")
        );
        if (!org.springframework.util.CollectionUtils.isEmpty(certificateRecordPagedResult.getItems())) {
            certificateRecordPagedResult.getItems().forEach(r -> {
                Member member = r.getMember();
                member.setFullName(EncryptUtil.aesEncrypt(member.getFullName(), null));
                member.setName(DesensitizationUtil.desensitizeEmployeeId(r.getName()));
            });
        }
        return certificateRecordPagedResult;
    }




    /** 强基计划-管理-考试管理-下载导入证书模板 */
    @RequestMapping(value = "/certificate-export" , method = RequestMethod.GET)
    @Param(name = "id", required = true) // 子专区id
    @Param()
    @JSON("*")
    public void exportCloudTemplate(RequestContext requestContext, Subject<Member> subject) throws IOException {
        HttpServletResponse response = BrowserUtil.fileDownloadResponse(requestContext, "免试人员模板.xlsx");
        Writer writer = new ExcelWriter();
        // 导入模板
        writer.sheet("免试导入", new ArrayList<>())
                .field("员工编号", null)
                .field("员工姓名", null)
                .field("考试id", null);
        
        writer.sheet("考试信息", strongBaseService.findExamDataBySubId(requestContext.get("id",String.class)))
                .field("考试名称", Exam::getName)
                .field("考试开始时间", e -> formatDate(e.getStartTime()))
                .field("考试结束时间", e -> formatDate(e.getEndTime()))
                .field("考试id", Exam::getId);
        writer.write(response.getOutputStream());
    }

    /** 考试证书管理-免试人员导入*/
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @Param(name="fileId", required=true)
    @Param(name="uri", required = true)
    @Param(name="subId", required = true)
    @JSON("successCount,failCount,data,errorFileId")
    @JSON("errors.(column,row)")
    @JSON("errors.code.(code)")
    @Permitted
    public Map<String, Object> importData(RequestContext requestContext, Subject<Member> subject) {
        // 定义一个集合用来存放本次导入的所有的证书编号
        List<String> numList = new ArrayList<String>();
        Optional<Attachment> attachment = fileService.get(requestContext.getString("fileId"));
        String subId = requestContext.getString("subId");
        Map<String, Object> m = attachment.map(t -> {
            Reader reader = new DefaultReader()
                    .skipRows(1)
                    //员工编号
                    .setColumn(0, String.class, new RequiredValidator<>())
                    //员工姓名
                    .setColumn(1, String.class, new RequiredValidator<>())
                    //考试id
                    .setColumn(2, String.class, new RequiredValidator<>());

            com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
            fastDFSAttachment.setPath(t.getPath());
            InputStream inputStream = attachmentResolver.resolveToRead(fastDFSAttachment);
            Map<String, Object> resultMap = new HashMap<>();
            try {
                Reader.Result result = reader.read(inputStream);

                // 判断是否超出每次允许导入的最大数量:暂定5000
                if ((result.getCorrectRows().size() + result.getErrorRows().size()) >= CertificateRecord.TEMPLATE_DATA_LIMIT) {
                    throw new UnprocessableException(ErrorCode.CertificateImprotExceedMax);
                }

                if (!result.isCellMatched()) { // 那么就是模板不匹配
                    throw new UnprocessableException(ErrorCode.TemplateError);
                }

                // 有权限的组织path
                String uri = requestContext.getString("uri");
                Map<String, Set<String>> grantOrganizationPathMap = grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), uri, subject.getRootOrganizationId());
                log.info("grantOrganizationPathMap={}",grantOrganizationPathMap);
                // 将excel中的 数据先读取出来
                List<CertificateRecord> tempLists = readExcelResult(result);
                log.info("tempLists={}",tempLists);
                // 以导入填写的员工编号为基础条件查询人员
                List<String> memberNames = tempLists.stream().map(l -> l.getMember().getName()).collect(Collectors.toList());
                List<String> examIds = tempLists.stream().map(l -> l.getExamId()).collect(Collectors.toList());
                List<Member> members = memberService.findByNames(memberNames);
                log.info("members={}",members);
                List<String> memberIds = members.stream().map(Member::getId).collect(Collectors.toList());
                // 查询考试信息map(id,证书模板id)
                Map<String, List<Exam>>  examMap = certificateRecordService.getCertificateIdMap(examIds, subId);
                log.info("examMap={}",examMap);

                // 查询这些人这些考试的证书记录map(考试id#人员id,记录id)
                Map<String,String> existsRecordsMap = certificateRecordService.findByMemberIdsAndExamIds(memberIds,examIds);
                log.info("existsRecordsMap={}",existsRecordsMap);
                // 查询考试的受众范围map(examId,List<item>)
                Map<String,List<AudienceObject>> examItemMap = certificateRecordService.getExamItemMap(examIds);
                log.info("examItemMap={}",examItemMap);

                // 查询考生的受众范围map(memberId,List<item>)
                Map<String,List<AudienceMember>> memberItemMap = certificateRecordService.getMemberItemMap(memberIds);
                log.info("memberItemMap={}",memberItemMap);

                // 准备k-v数据用于校验，避免校验的时候循环读取集合
                Map<String, Member> memberMap = members.stream().filter(e -> e.getName() != null).collect(Collectors.toMap(Member::getName, e -> e, (t1, t2) -> t2));
                log.info("memberMap={}",memberMap);

                // 验证不通过的数据集合
                List<CertificateRecord> errorList = getResultErrors(result);
                log.info("errorList={}",errorList);

                // 验证通过需要新增的数据集合
                List<CertificateRecord> correctInsertList = new ArrayList<>();
                // 数据验证：先准备好需要的数据，纯JAVA逻辑校验业务数据，循环中不与数据库交互
                tempLists.forEach(tl -> {
                    try {
                        validateBusinessData(tl, examMap, existsRecordsMap, examItemMap, memberItemMap ,memberMap,
                                errorList, correctInsertList, grantOrganizationPathMap);
                    } catch (Exception e) {
//                        e.printStackTrace();
                        log.info("e={}",e);
                        throw new UnprocessableException(ErrorCode.CertificateCodeGeneratError); // 证书编号生成失败
                    }
                });
                // 批量新增成功数据
                certificateRecordService.batchInsert(correctInsertList);
                log.info("correctInsertList={}",correctInsertList);

                // 返回数据
                resultMap.put("successCount", correctInsertList.size()); // 成功数量
                resultMap.put("failCount", errorList.size()); // 失败数量
                resultMap.put("errorFileId", createErrorTempFile(errorList)); // 失败记录下载文件id
                resultMap.put("errors", createErrorList(errorList)); // 错误信息：行-列-错误编码
                log.info("errorList={}",errorList);

            } catch (IOException e) {
                log.info("e={}",e);
//                e.printStackTrace();
                throw new UnprocessableException(ErrorCode.TemplateError); // 模板错误
            }
            return resultMap;
        }).get();

        return m;
    }

    /** 错误信息：行-列-错误编码 */
    private List<Map<String, Object>> createErrorList(List<CertificateRecord> errorList) {
        List<Map<String, Object>> errors = new ArrayList<>();
        errorList.forEach(error -> {
            for(RowError e : error.getErrors() ){
                Map<String, Object> map = new HashMap<>();
                map.put("row", e.getRow());
                map.put("column", e.getColumn());
                map.put("code", e.getCode() == null ? "" : e.getCode());
                errors.add(map);
            }
        });
        return errors;
    }


    /** 生成导入的错误临时文件 */
    private String createErrorTempFile(List<CertificateRecord> errorList) throws IOException {
        Writer writer = new ExcelWriter();
        if (errorList !=null && !errorList.isEmpty()) {

            // 错误数据
            writer.sheet("导入有误的数据", errorList)
                    .field("员工编号", (e) -> e.getMember()!=null?e.getMember().getName():"")
                    .field("员工姓名", (e) -> e.getMember()!=null?e.getMember().getFullName():"")
                    .field("考试id", (e) -> e.getExamId());
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            try {
                writer.write(os);
            } catch (IOException e) {
                e.printStackTrace();
            }
            return uploadTempFile(new ByteArrayInputStream(os.toByteArray()));
        }

        return null;
    }

    /** 输出流转文件，再转输入流上传到文件系统，返回文件id  */
    private String uploadTempFile(InputStream is) throws IOException {
        MultipartFile mfile = UploadUtil.transferTo(is, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "导出错误数据.xlsx", is.available());
        com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(null, mfile, Optional.empty());
        String[] filename = new String[]{ restfulAttachment.getFilename() };
        String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, new String[]{"xlsx"}, path, size);
        return result.get(0).getId();
    }

    /** 生成导入的错误临时文件 */
    private String createCloudErrorTempFile(List<CertificateRecord> errorList, List<Profession> professions) throws IOException {
        Writer writer = new ExcelWriter();
        if (errorList !=null && !errorList.isEmpty()) {
            // 错误数据
            writer.sheet("导入有误的数据", errorList)
                    .field(CertificateRecord.TEMPLATE_MEMBER_NAME, (e) -> e.getMember().getName())
                    .field(CertificateRecord.TEMPLATE_LEVEL, (e) -> e.getProfessionLevel().getLevelName())
                    .field(CertificateRecord.TEMPLATE_PROFESSION, (e) -> e.getProfession() == null ? "" : e.getProfession().getName())
                    .field(CertificateRecord.TEMPLATE_SCORE, (e) -> e.getTextScore())
                    .field(CertificateRecord.TEMPLATE_REASON, (e) -> e.getReason());
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            try {
                // 等级-专业-下拉数据
                writer.write(os, workbook -> {
                    org.apache.poi.ss.usermodel.Sheet s = workbook.getSheetAt(0);
                    String[] types = {"初级","中级"};
                    writer.setSelectionContants(s, types, 1, 5000, CertificateRecord.TEMPLATE_PROFESSION_COLUMN, CertificateRecord.TEMPLATE_PROFESSION_COLUMN);
                    String[] profession = professions.stream().map(eq -> eq.getName()).toArray(String[]::new);
                    writer.setSelectionContants(s, profession, 1, 5000, CertificateRecord.TEMPLATE_SUB_PROFESSION_COLUMN, CertificateRecord.TEMPLATE_SUB_PROFESSION_COLUMN);
                });

            } catch (IOException e) {
                e.printStackTrace();
            }
            // 将临时文件写入文件系统
            return uploadTempFile(new ByteArrayInputStream(os.toByteArray()));
        }

        return null;
    }


    private void validateBusinessData(CertificateRecord tl, Map<String, List<Exam>> examMap,
                                      Map<String, String> existsRecordsMap, Map<String, List<AudienceObject>> examItemMap,
                                      Map<String, List<AudienceMember>> memberItemMap, Map<String, Member> memberMap,
                                      List<CertificateRecord> errorList, List<CertificateRecord> correctInsertList,
                                      Map<String, Set<String>> grantOrganizationPathMap) throws Exception{
        Set<String> organizationIdSet = grantOrganizationPathMap.get(com.zxy.product.system.entity.Organization.NOT_INCLUDE_KEY);
        log.info("organizationIdSet={}",organizationIdSet);
        Set<String> pathSet = grantOrganizationPathMap.get(com.zxy.product.system.entity.Organization.INCLUDE_KEY);
        log.info("pathSet={}",pathSet);

        List<RowError> errors = tl.getErrors();
        log.info("errors={}",errors);

        Member member = tl.getMember();
        log.info("member={}",member);

        // 通过员工编号查找员工
        Member targetMember = memberMap.getOrDefault(member.getName(),null);
        log.info("targetMember={}",targetMember);

        List<Exam> certificateIds = examMap.getOrDefault(tl.getExamId(), null);
        log.info("certificateIds={}",certificateIds);

        if ( targetMember == null ) {
                // 员工不存在
                errors.add(new RowError(tl.getRow(), 0, ErrorCode.MemberNameNotExist.getCode()));
            } else if (targetMember.getFullName() !=null && !targetMember.getFullName().equals(member.getFullName())) {
                // 员工姓名和员工编号不匹配
                errors.add(new RowError(tl.getRow(), 0, ErrorCode.MemberFullNameNoMatch.getCode()));
            } else if (certificateIds == null) {
                // 考试不存在
                errors.add(new RowError(tl.getRow(), 2, ErrorCode.ExamsDonExist.getCode()));
            } else if (certificateIds.get(0).getHasCert() == null || certificateIds.get(0).getHasCert() == 0 || certificateIds.get(0).getCertificateId()==null) {
                // 考试未配置证书号
                errors.add(new RowError(tl.getRow(), 2, ErrorCode.NoCertificateIsConfiguredForTheExam.getCode()));
            } else if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(existsRecordsMap.get(tl.getExamId()+"#"+targetMember.getId()))) {
                // 证书已存在
                errors.add(new RowError(tl.getRow(), 0, ErrorCode.CertificateAlreadyExists.getCode()));
            } else if (examItemMap.get(tl.getExamId()) == null
                || memberItemMap.get(targetMember.getId()) == null
                || CollectionUtils.isEmpty(examItemMap.get(tl.getExamId()).stream().map(AudienceObject::getItemId).collect(Collectors.toList()))
                || CollectionUtils.isEmpty(memberItemMap.get(targetMember.getId()).stream().map(AudienceMember::getItemId).collect(Collectors.toList()))
                || !CollectionUtils.containsAny(examItemMap.get(tl.getExamId()).stream().map(AudienceObject::getItemId).collect(Collectors.toList()), memberItemMap.get(targetMember.getId()).stream().map(AudienceMember::getItemId).collect(Collectors.toList()))) {
                // 考生不在考试受众范围内
                errors.add(new RowError(tl.getRow(), 0, ErrorCode.EmployeesAreNotPartOfTheTestIsAudience.getCode()));
            } else if ((!CollectionUtils.isEmpty(pathSet) || !CollectionUtils.isEmpty(organizationIdSet)) && targetMember.getOrganization() != null) {
                String orgId = targetMember.getOrganization().getId();
                String path = targetMember.getOrganization().getPath();
                if (StringUtils.isEmpty(path)) {
                    errors.add(new RowError(tl.getRow(), 0, ErrorCode.CertificateMemberNotGrant.getCode()));
                }
                if (pathSet.isEmpty() && !organizationIdSet.contains(orgId)) {
                    // 员工不在当前用户权限范围内
                    errors.add(new RowError(tl.getRow(), 0, ErrorCode.CertificateMemberNotGrant.getCode()));
                } else {
                    if (pathSet.stream().filter(s -> !StringUtils.isEmpty(path) && path.startsWith(s)).count() == 0) {
                        // 员工不在当前用户权限范围内
                        errors.add(new RowError(tl.getRow(), 0, ErrorCode.CertificateMemberNotGrant.getCode()));
                    }
                    if (!organizationIdSet.isEmpty() && !organizationIdSet.contains(orgId)) {
                        // 员工不在当前用户权限范围内
                        errors.add(new RowError(tl.getRow(), 0, ErrorCode.CertificateMemberNotGrant.getCode()));
                    }
                }
            }
            else {
                // 员工id
                tl.setMemberId(targetMember.getId());
            }

            if (errors.size() == 0) {
                tl.forInsert();
                // 该条数据验证通过
                String certificateId = certificateIds.get(0).getCertificateId();
                tl.setTemplateId(certificateId);
                tl.setAccessType(CertificateRecord.ACCESS_TYPE_IMPORT);
                tl.setCloud(CertificateRecord.IS_CLOUD_NO);
                tl.setGrid(CertificateRecord.IS_GRID_NO);
                tl.setPassStatus(CertificateRecord.PASS_STATUS_PASS);
                tl.setIssueTime(System.currentTimeMillis());
                tl.setValidDate(null);
                tl.setMemberId(targetMember.getId());
                tl.setName(certificateIds.get(0).getName());

                Instant instant = new Date(System.currentTimeMillis()).toInstant();
                LocalDate currentLocalDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();
                String dateFormat = currentLocalDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String code;
                if (com.alibaba.dubbo.common.utils.StringUtils.isNotEmpty(certificateId) && certificateId.contains(CertificateRecord.QJ_CERTIFICATE) && certificateId.split("-").length > 1) {
                    // 强基计划个性化证书编码
                    code = certificateNormalCodeGenerator.getCode(certificateId.split("-")[1]+"000", dateFormat, "");
                } else {
                    // 普通考试，走普通考试编号规则
                    code = certificateNormalCodeGenerator.getCode(CertificateRecord.CERTIFACATE_TYPE_EXAM, dateFormat, "000");
                }
                tl.setNum(code);
                tl.setIsCurrent(CertificateRecord.IS_CURRENT_YES);
                // 加入到验证通过新增集合
                correctInsertList.add(tl);
                // 加入到已存在集合
                existsRecordsMap.put(tl.getExamId()+"#"+tl.getMemberId(), tl.getId());
            } else {
                // 该条数据验证失败
                errorList.add(tl);
            }


    }

    /** reader校验规则中的校验失败记录 */
    private List<CertificateRecord> getResultErrors(Reader.Result result) {
        List<CertificateRecord> list = new ArrayList<>();
        List<Reader.Row> errorRows = result.getErrorRows();
        List<Validator.DataError> errors = result.getErrors();
        Map<Integer, List<Validator.DataError>> rowErrorsMap = errors.stream().collect(Collectors.groupingBy(e -> e.getRow()));
        if(errorRows != null && errorRows.size() > 0){
            for(int i = 0; i < errorRows.size(); i++ ){
                CertificateRecord record = new CertificateRecord();
                // 错误记录
                Reader.Row item = errorRows.get(i);
                List<Validator.DataError> dataErrors = rowErrorsMap.get(item.getIndex());
                List<RowError> rowErrors = dataErrors.stream().map(dataError ->{
                    RowError error = new RowError();
                    if(!StringUtils.isEmpty(dataError.getCode())){
                        error.setCode(dataError.getCode().getCode());
                    }
                    if(dataError.getColumn() !=-1){
                        error.setColumn(dataError.getColumn());
                    }
                    if(dataError.getRow() !=-1){
                        error.setRow(dataError.getRow());
                    }
                    return error;
                }).collect(Collectors.toList());

                record.setErrors(rowErrors);
                record.setRow(item.getIndex());
                Member member = new Member();
                member.setName(item.get(0, String.class));
                member.setFullName(item.get(1, String.class));
                record.setMember(member);
                record.setExamId(item.get(2, String.class));
                list.add(record);
            }
        }
        return list;
    }

    /** 将excel数据筛选后读取到List */
    private List<CertificateRecord> readExcelResult(Reader.Result result) {
        List<CertificateRecord> records = result.getCorrectRows().stream().map(row ->{
            CertificateRecord record = new CertificateRecord();
            record.forInsert();
            record.setRow(row.getIndex());
            Member member = new Member();
            member.setName(row.get(0, String.class));
            member.setFullName(row.get(1, String.class));
            record.setMember(member);
            record.setExamId(row.get(2, String.class));
            int score = CertificateRecord.TEMPLATE_DATA_SCORE_DEFAULT;
            record.setScore(score);
            // 成绩级别，0不及格 1及格 2良好 3优秀
            Integer scoreLevel = CertificateRecord.SCORE_LEVEL_NOT_PASS;
            if(score >= CertificateRecord.SCORE_EXCELLENT) { // 优秀
                scoreLevel = CertificateRecord.SCORE_LEVEL_EXCELLENT;
            } else if ( score < CertificateRecord.SCORE_EXCELLENT && score >= CertificateRecord.SCORE_GOOD) { // 良好
                scoreLevel = CertificateRecord.SCORE_LEVEL_GOOD;
            } else if ( score < CertificateRecord.SCORE_GOOD && score >= CertificateRecord.SCORE_PASS) { // 及格
                scoreLevel = CertificateRecord.SCORE_LEVEL_PASS;
            }
            record.setScoreLevel(scoreLevel);
            // 发放原因
            record.setReason("强基计划考试证书导入");
            return record;
        }).collect(Collectors.toList());

        // 时间处理，保证导入顺序一致
        Long currentTimeMillis = System.currentTimeMillis();
        for (CertificateRecord record : records) {
            record.setCreateTime(currentTimeMillis --);
        }
        return records;
    }



    /** 强基计划-管理-考试管理-取消成绩（删除证书） */
    @RequestMapping(value = "/del-certificate/{id}" , method = RequestMethod.DELETE)
    @Param(name = "id", required = true) // 证书记录id
    @Param(name = "subId", required = true) // 子认证专区id
    @Param(name = "reason", required = true) // 原因描述
    @Param(name = "attachmentId") // 证明材料id
    @Param(name = "attachmentName") // 证明材料名称
    @Param()
    @JSON("*")
    public Map<String, String> deleteCertificate(RequestContext requestContext, Subject<Member> subject){
        String memberId = strongBaseService.deleteCertificate(
                subject.getCurrentUserId(),
                requestContext.get("id",String.class),
                requestContext.get("subId",String.class),
                requestContext.get("reason",String.class),
                requestContext.getOptional("attachmentId",String.class).orElse(null),
                requestContext.getOptional("attachmentName",String.class).orElse(null)
        );
        if (!StringUtils.isEmpty(memberId)) {
            subAuthenticatedService.deleteCertificate(Arrays.asList(memberId),requestContext.get("subId",String.class));
        }
        return ImmutableMap.of("id", memberId);
    }


    /**
     * 强基计划-管理-取消成绩（删除考试证书）记录列表
     */
    @RequestMapping(value = "/cancel-list", method = RequestMethod.GET)
    @JSON("recordCount")
    @JSON("items.(id,publishTime,cancelTime,reason,attachmentId,attachmentName)")
    @JSON("items.member.(id,name,fullName)")
    @JSON("items.operatorMember.(id,name,fullName)")
    @JSON("items.member.organization.(id,name)")
    @Param(name="page", type=Integer.class, required = true)
    @Param(name="pageSize", type=Integer.class, required = true)
    @Param(name="id", required = true) // 子认证专区id
    @Param(name="uri", required = true)
    @Param(name="name")
    @Param(name="fullName")
    @Param(name="organizationId")
    @Param(name="publishTimeStart", type=Long.class) // 发证日期开始时间戳
    @Param(name="publishTimeEnd", type=Long.class) // 发证日期结束时间戳
    @Param(name="cancelTimeStart", type=Long.class) // 删除日期开始时间戳
    @Param(name="cancelTimeEnd", type=Long.class) // 删除日期结束时间戳
    @Permitted()
    public PagedResult<SubAuthenticatedCancelCertificateRecord> findCancelPagedResult(RequestContext requestContext, Subject<Member> subject) {
        String uri = requestContext.getString("uri");
        Map<String, Set<String>> grantOrganizationPathMap = grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), uri, subject.getRootOrganizationId());
        return strongBaseService.findCancelPagedResult(
                requestContext.get("page",Integer.class),
                requestContext.get("pageSize",Integer.class),
                requestContext.get("id",String.class),
                grantOrganizationPathMap,
                requestContext.getOptional("name",String.class),
                requestContext.getOptional("fullName",String.class),
                requestContext.getOptionalString("organizationId"),
                requestContext.getOptionalLong("publishTimeStart"),
                requestContext.getOptionalLong("publishTimeEnd"),
                requestContext.getOptionalLong("cancelTimeStart"),
                requestContext.getOptionalLong("cancelTimeEnd")
        );
    }



    /**
     * 强基计划-管理-考试记录列表查询
     */
    @RequestMapping(value = "/exam-record-list", method = RequestMethod.GET)
    @JSON("recordCount")
    @JSON("items.(id,startTime,examTimes,score,status)")
    @JSON("items.exam.(id,name)")
    @JSON("items.member.(id,name,fullName)")
    @JSON("items.member.organization.(id,name)")
    @Param(name="page", type=Integer.class, required = true)
    @Param(name="pageSize", type=Integer.class, required = true)
    @Param(name="id", required = true) // 子认证专区id
    @Param(name="uri", required = true)
    @Param(name="year", required = true) //年份
    @Param(name="name")
    @Param(name="fullName")
    @Param(name="organizationId")
    @Param(name="examName")
    @Permitted()
    public PagedResult<ExamRecord> findExamRecordPagedResult(RequestContext requestContext, Subject<Member> subject) {
        String uri = requestContext.getString("uri");
        Map<String, Set<String>> grantOrganizationPathMap = grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), uri, subject.getRootOrganizationId());
        return strongBaseService.findExamRecordPagedResult(
                requestContext.get("page",Integer.class),
                requestContext.get("pageSize",Integer.class),
                requestContext.get("id",String.class),
                grantOrganizationPathMap,
                requestContext.getOptional("name",String.class),
                requestContext.getOptional("fullName",String.class),
                requestContext.getOptionalString("organizationId"),
                requestContext.getOptionalString("examName"),
                requestContext.getString("year")
        );
    }



    /**
     * 学员端-查询子认证考试列表信息
     */
    /**
     * *
        迁移到exam-stu

    @RequestMapping(value = "/front-exam-list", method = RequestMethod.GET)
    @JSON("exams.(id,name,startTime,endTime,allowExamTimes)")
    @JSON("remainCount")
    @Param(name="subId", required = true) // 子认证专区id
    @Param(name="contentId", required = true) // 考试组id
    @Param(name="businessType", type=Integer.class, required = true) // 1:模拟考试。2：认证考试
    @Permitted()
    public Map<String, Object> findFrontExamList(RequestContext requestContext, Subject<Member> subject) {
            return strongBaseService.findFrontExamList(
            requestContext.get("subId",String.class),
            requestContext.get("contentId",String.class),
            requestContext.get("businessType",Integer.class),
            subject.getCurrentUserId()
        );
    }
     */

    /**
     * 管理-考试记录-导出
     * @param requestContext
     * @param subject
     * @throws IOException
     */
    @RequestMapping(value = "/export-exam-records" , method = RequestMethod.GET)
    @Param(name="id", required = true) // 子认证专区id
    @Param(name="uri", required = true)
    @Param(name="year", required = true) //年份
    @Param(name="ids")
    @Param(name="name")
    @Param(name="fullName")
    @Param(name="organizationId")
    @Param(name="examName")
    public void exportExamRecordsForSubject(RequestContext requestContext, Subject<Member> subject) throws IOException {

        HttpServletResponse response = requestContext.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="
                + parseFileName("考试记录",requestContext.getRequest().getHeader("User-Agent")) + ".xlsx");

        Writer writer = new ExcelWriter();

        List<ExamRecord> list;
        if (requestContext.getOptional("ids",String.class).isPresent()) {
            List<String> ids = Arrays.asList(requestContext.getOptional("ids", String.class).get().split(","));
            list = strongBaseService.findExamRecords(ids,requestContext.getString("year"));
        }else {
            String uri = requestContext.getString("uri");
            Map<String, Set<String>> grantOrganizationPathMap = grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), uri, subject.getRootOrganizationId());
            list =  strongBaseService.findExamRecordList(
                    requestContext.get("id",String.class),
                    grantOrganizationPathMap,
                    requestContext.getOptional("name",String.class),
                    requestContext.getOptional("fullName",String.class),
                    requestContext.getOptionalString("organizationId"),
                    requestContext.getOptionalString("examName"),
                    requestContext.getString("year")
            );
        }
        int i = 0;
        for (ExamRecord examRecord : list) {
            examRecord.setIndex(++i);
        }
        writer.sheet("考试管理导出", list)
                .field(ExamRecord.TEMPLATE_INDEX, ExamRecord::getIndex)
                .field(ExamRecord.TEMPLATE_MEMBER_NAME, e -> e.getMember()!=null?e.getMember().getName():"")
                .field(ExamRecord.TEMPLATE_FULL_NAME, e -> e.getMember()!=null?e.getMember().getFullName():"")
                .field(ExamRecord.TEMPLATE_ORGANIZATION, e -> (e.getMember()!=null&&e.getMember().getOrganization()!=null)?e.getMember().getOrganization().getName():"")
                .field("考试名称", e -> e.getExam()!=null?e.getExam().getName():"")
                .field("考试时间", e -> getTimeStr(e.getStartTime()))
                .field(ExamRecord.TEMPLATE_FIRST_X_TIMES, e -> e.getExamTimes()!=null?(e.getExamTimes()):"-")
                .field("成绩", e -> (e.getStatus()!=null&& ExamRecord.STATUS_NULLIFY==e.getStatus())?"作废":getExportScore(e.getScore()));
        writer.write(response.getOutputStream());
    }

    private String getExportScore(Integer score) {
        return score == null ? "" : changeNumDecimal(((float)score / 100) + "");
    }

    private String changeNumDecimal(String num) {
        if (num != null && !num.equals("")) {
            String[] attr = num.split("\\.");
            return attr[1].equals("0") ? attr[0] : num;
        }
        return "";
    }

    private String getTimeStr(Long time) {
        return Optional.ofNullable(time).map(t -> {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            return df.format(new Date(t));
        }).orElse("");
    }


    private String parseFileName(String fileName, String agent) throws UnsupportedEncodingException {
        if (BrowserUtil.isMSBrowser(agent)) {
            return URL.encode(fileName);
        }
        return new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
    }

    private String formatDate(Long date) {
        SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(date);
    }


    /**
     * 根据考试id查询试卷信息
     */
    @RequestMapping(value = "/paper", method = RequestMethod.GET)
    @JSON("id,type")
    @Param(name="examId", required = true) // 考试id
    @Permitted()
    public PaperClass findPaperByExamId(RequestContext requestContext, Subject<Member> subject) {
        return paperClassService.getPaperByExamId(
                requestContext.get("examId",String.class)
        );
    }



}
