package com.zxy.product.exam.web.controller.intelligence;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.exam.api.DigitalIntelligenceResultService;
import com.zxy.product.exam.dto.CourseInfoDto;
import com.zxy.product.exam.entity.Member;
import com.zxy.product.exam.entity.szfn.DigitalIntelligenceResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @Auther: xxh
 * @Date: 2025/7/30 - 07 - 30 - 14:16
 * @Description: com.zxy.product.exam.web.controller.intelligence
 * @version: 1.0
 */
@Controller
@RequestMapping("/digital-intelligence")
public class DigitalIntelligenceResultController {

    private DigitalIntelligenceResultService digitalIntelligenceResultService;
    private CourseInfoService courseInfoService;

    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setDigitalIntelligenceResultService(DigitalIntelligenceResultService digitalIntelligenceResultService) {
        this.digitalIntelligenceResultService = digitalIntelligenceResultService;
    }

    @RequestMapping(value = "/getDetail", method = RequestMethod.GET)
    @Param()
    @JSON("name,memberId,abilityTags,strengthEnhancement,skillImprovement,shortTermPlan,mediumTermPlan,recommendedCoursesId")
    @JSON("courseInfos.(id,name,description,coverPath)")
    public DigitalIntelligenceResult getWithScoreDetail(RequestContext requestContext, Subject<Member> subject) {
        String currentUserId = subject.getCurrentUserId();
        Optional<DigitalIntelligenceResult> result = digitalIntelligenceResultService.getByMemberId(currentUserId);
        if(result.isPresent()){
            String recommendedCoursesId = result.get().getRecommendedCoursesId();
            if(!StringUtils.isBlank(recommendedCoursesId)){
                List<CourseInfo> courseInfos = courseInfoService.findByIds(Arrays.asList(recommendedCoursesId.split(CommonConstant.SEPARATOR_COMMA)),currentUserId);
                List<CourseInfoDto> dtos = new ArrayList<>();
                courseInfos.forEach(item->{
                    CourseInfoDto courseInfoDto = new CourseInfoDto();
                    courseInfoDto.setId(item.getId());
                    courseInfoDto.setName(item.getName());
                    courseInfoDto.setDescription(item.getDescription());
                    courseInfoDto.setCoverPath(item.getCoverPath());
                    dtos.add(courseInfoDto);
                });
                result.get().setCourseInfos(dtos);
            }
        }
        return result.orElse(new DigitalIntelligenceResult());
    }


}
