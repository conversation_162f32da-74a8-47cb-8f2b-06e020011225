<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zxy.product</groupId>
        <artifactId>exam</artifactId>
        <version>cmu-9.6.0</version>
    </parent>
    <artifactId>exam-web-server</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>web-server-parent</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-stu-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>system-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>human-resource-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>course-study-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>train-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>org.csource</groupId>
            <artifactId>fastdfs-client-java</artifactId>
        </dependency>
         <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 暂时加在exam项目，应该要另外新建common  -->
<!--        <dependency>-->
<!--            <groupId>io.springfox</groupId>-->
<!--            <artifactId>springfox-swagger2</artifactId>-->
<!--            <version>2.7.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.4</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>io.springfox.ui</groupId>-->
<!--            <artifactId>springfox-swagger-ui-rfc6570</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
            <version>5.8.23</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>common-encrypt</artifactId>
            <version>CMU-0.1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.3.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
