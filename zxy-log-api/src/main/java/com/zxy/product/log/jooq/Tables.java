/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq;


import com.zxy.product.log.jooq.tables.*;

import javax.annotation.Generated;


/**
 * Convenience access to all tables in zxy-log
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * The table <code>zxy-log.t_audit_log</code>.
     */
    public static final AuditLog AUDIT_LOG = com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG;

    /**
     * 登录日至表
     */
    public static final LoginLog LOGIN_LOG = com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG;

    /**
     * 在线人数统计表
     */
    public static final OnlineStatistics ONLINE_STATISTICS = com.zxy.product.log.jooq.tables.OnlineStatistics.ONLINE_STATISTICS;

    /**
     * 白名单登陆异常记录
     */
    public static final WhiteRecord WHITE_RECORD = com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD;

    /**
     * The table <code>zxy-log.t_user_behavior</code>.
     */
    public static final UserBehavior USER_BEHAVIOR = com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR;

    /**
     * 人员表
     */
    public static final Member MEMBER = com.zxy.product.log.jooq.tables.Member.MEMBER;

    /**
     * 资源浏览统计表
     */
    public static final ResourceVisit RESOURCE_VISIT = com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT;

    /**
     * 用户活跃表
     */
    public static final UserActive USER_ACTIVE = com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE;

    /**
     * 用户登录每日统计表
     */
    public static final LoginCountDay LOGIN_COUNT_DAY = com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY;

    /**
     * 各组织日登陆统计表
     */
    public static final OrganizationLoginCountDay ORGANIZATION_LOGIN_COUNT_DAY = com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY;

    public static final MemberDetail MEMBER_DETAIL = com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL;

    public static final Organization ORGANIZATION = com.zxy.product.log.jooq.tables.Organization.ORGANIZATION;

    public static final OrganizationDetail ORGANIZATION_DETAIL = com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 热门学习资源表
     */
    public static final HotResourceVisit HOT_RESOURCE_VISIT = com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT;

    /**
     * otter同步课程表
     */
    public static final CourseInfo COURSE_INFO = com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO;
}
