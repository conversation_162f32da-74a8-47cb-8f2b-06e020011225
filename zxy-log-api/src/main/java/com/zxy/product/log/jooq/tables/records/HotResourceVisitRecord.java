/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.HotResourceVisit;
import com.zxy.product.log.jooq.tables.interfaces.IHotResourceVisit;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 热门学习资源表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HotResourceVisitRecord extends UpdatableRecordImpl<HotResourceVisitRecord> implements Record10<String, String, String, Integer, String, String, Integer, Integer, Long, Timestamp>, IHotResourceVisit {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_business_id</code>. 资源ID
     */
    @Override
    public void setBusinessId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_business_id</code>. 资源ID
     */
    @Override
    public String getBusinessId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_business_name</code>. 资源名称
     */
    @Override
    public void setBusinessName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_business_name</code>. 资源名称
     */
    @Override
    public String getBusinessName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_business_type</code>. 资源类型:1 课程,2 专题,3 直播
     */
    @Override
    public void setBusinessType(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_business_type</code>. 资源类型:1 课程,2 专题,3 直播
     */
    @Override
    public Integer getBusinessType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_organization_id</code>. 资源所属组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_organization_id</code>. 资源所属组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_organization_name</code>. 资源所属组织名称
     */
    @Override
    public void setOrganizationName(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_organization_name</code>. 资源所属组织名称
     */
    @Override
    public String getOrganizationName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_day30_visit</code>. 资源近30天访问次数
     */
    @Override
    public void setDay30Visit(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_day30_visit</code>. 资源近30天访问次数
     */
    @Override
    public Integer getDay30Visit() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_status</code>. 状态 显示0，隐藏1
     */
    @Override
    public void setStatus(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_status</code>. 状态 显示0，隐藏1
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, Integer, String, String, Integer, Integer, Long, Timestamp> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, Integer, String, String, Integer, Integer, Long, Timestamp> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.DAY30_VISIT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field10() {
        return HotResourceVisit.HOT_RESOURCE_VISIT.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getBusinessId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getBusinessName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getBusinessType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getOrganizationName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getDay30Visit();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value10() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value2(String value) {
        setBusinessId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value3(String value) {
        setBusinessName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value4(Integer value) {
        setBusinessType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value5(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value6(String value) {
        setOrganizationName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value7(Integer value) {
        setDay30Visit(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value8(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value9(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord value10(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisitRecord values(String value1, String value2, String value3, Integer value4, String value5, String value6, Integer value7, Integer value8, Long value9, Timestamp value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IHotResourceVisit from) {
        setId(from.getId());
        setBusinessId(from.getBusinessId());
        setBusinessName(from.getBusinessName());
        setBusinessType(from.getBusinessType());
        setOrganizationId(from.getOrganizationId());
        setOrganizationName(from.getOrganizationName());
        setDay30Visit(from.getDay30Visit());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IHotResourceVisit> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached HotResourceVisitRecord
     */
    public HotResourceVisitRecord() {
        super(HotResourceVisit.HOT_RESOURCE_VISIT);
    }

    /**
     * Create a detached, initialised HotResourceVisitRecord
     */
    public HotResourceVisitRecord(String id, String businessId, String businessName, Integer businessType, String organizationId, String organizationName, Integer day30Visit, Integer status, Long createTime, Timestamp modifyDate) {
        super(HotResourceVisit.HOT_RESOURCE_VISIT);

        set(0, id);
        set(1, businessId);
        set(2, businessName);
        set(3, businessType);
        set(4, organizationId);
        set(5, organizationName);
        set(6, day30Visit);
        set(7, status);
        set(8, createTime);
        set(9, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.HotResourceVisitEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.HotResourceVisitEntity pojo = (com.zxy.product.log.jooq.tables.pojos.HotResourceVisitEntity)source;
        pojo.into(this);
        return true;
    }
}
