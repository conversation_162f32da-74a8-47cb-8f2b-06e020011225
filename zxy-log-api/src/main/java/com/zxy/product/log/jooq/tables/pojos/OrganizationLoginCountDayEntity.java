/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IOrganizationLoginCountDay;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 各组织日登陆统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationLoginCountDayEntity extends BaseEntity implements IOrganizationLoginCountDay {

    private static final long serialVersionUID = 1L;

    private Integer   day;
    private Integer   month;
    private Integer   year;
    private Integer   memberNum;
    private Integer   memberTime;
    private Integer   organizationNum;
    private String    organizationId;
    private Timestamp modifyDate;

    public OrganizationLoginCountDayEntity() {}

    public OrganizationLoginCountDayEntity(OrganizationLoginCountDayEntity value) {
        this.day = value.day;
        this.month = value.month;
        this.year = value.year;
        this.memberNum = value.memberNum;
        this.memberTime = value.memberTime;
        this.organizationNum = value.organizationNum;
        this.organizationId = value.organizationId;
        this.modifyDate = value.modifyDate;
    }

    public OrganizationLoginCountDayEntity(
        String    id,
        Integer   day,
        Integer   month,
        Integer   year,
        Integer   memberNum,
        Integer   memberTime,
        Integer   organizationNum,
        String    organizationId,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.day = day;
        this.month = month;
        this.year = year;
        this.memberNum = memberNum;
        this.memberTime = memberTime;
        this.organizationNum = organizationNum;
        this.organizationId = organizationId;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Integer getDay() {
        return this.day;
    }

    @Override
    public void setDay(Integer day) {
        this.day = day;
    }

    @Override
    public Integer getMonth() {
        return this.month;
    }

    @Override
    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public Integer getYear() {
        return this.year;
    }

    @Override
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public Integer getMemberNum() {
        return this.memberNum;
    }

    @Override
    public void setMemberNum(Integer memberNum) {
        this.memberNum = memberNum;
    }

    @Override
    public Integer getMemberTime() {
        return this.memberTime;
    }

    @Override
    public void setMemberTime(Integer memberTime) {
        this.memberTime = memberTime;
    }

    @Override
    public Integer getOrganizationNum() {
        return this.organizationNum;
    }

    @Override
    public void setOrganizationNum(Integer organizationNum) {
        this.organizationNum = organizationNum;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("OrganizationLoginCountDayEntity (");

        sb.append(getId());
        sb.append(", ").append(day);
        sb.append(", ").append(month);
        sb.append(", ").append(year);
        sb.append(", ").append(memberNum);
        sb.append(", ").append(memberTime);
        sb.append(", ").append(organizationNum);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOrganizationLoginCountDay from) {
        setId(from.getId());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setMemberNum(from.getMemberNum());
        setMemberTime(from.getMemberTime());
        setOrganizationNum(from.getOrganizationNum());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOrganizationLoginCountDay> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends OrganizationLoginCountDayEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.OrganizationLoginCountDayRecord r = new com.zxy.product.log.jooq.tables.records.OrganizationLoginCountDayRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ID, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.DAY) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.DAY, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.DAY));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MONTH) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MONTH, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MONTH));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.YEAR) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.YEAR, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.YEAR));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_NUM) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_NUM, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_NUM));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_TIME) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_TIME, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MEMBER_TIME));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_NUM) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_NUM, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_NUM));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MODIFY_DATE, record.getValue(com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
