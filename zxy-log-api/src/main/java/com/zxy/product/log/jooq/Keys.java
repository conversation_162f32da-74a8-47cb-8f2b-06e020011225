/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq;


import com.zxy.product.log.jooq.tables.*;
import com.zxy.product.log.jooq.tables.records.*;
import org.jooq.UniqueKey;
import org.jooq.impl.AbstractKeys;

import javax.annotation.Generated;


/**
 * A class modelling foreign key relationships between tables of the <code>zxy-log</code> 
 * schema
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // IDENTITY definitions
    // -------------------------------------------------------------------------


    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<AuditLogRecord> KEY_T_AUDIT_LOG_PRIMARY = UniqueKeys0.KEY_T_AUDIT_LOG_PRIMARY;
    public static final UniqueKey<LoginLogRecord> KEY_T_LOGIN_LOG_PRIMARY = UniqueKeys0.KEY_T_LOGIN_LOG_PRIMARY;
    public static final UniqueKey<OnlineStatisticsRecord> KEY_T_ONLINE_STATISTICS_PRIMARY = UniqueKeys0.KEY_T_ONLINE_STATISTICS_PRIMARY;
    public static final UniqueKey<WhiteRecordRecord> KEY_T_WHITE_RECORD_PRIMARY = UniqueKeys0.KEY_T_WHITE_RECORD_PRIMARY;
    public static final UniqueKey<UserBehaviorRecord> KEY_T_USER_BEHAVIOR_PRIMARY = UniqueKeys0.KEY_T_USER_BEHAVIOR_PRIMARY;
    public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = UniqueKeys0.KEY_T_MEMBER_PRIMARY;
    public static final UniqueKey<ResourceVisitRecord> KEY_T_RESOURCE_VISIT_PRIMARY = UniqueKeys0.KEY_T_RESOURCE_VISIT_PRIMARY;
    public static final UniqueKey<DeleteDataLogRecord> KEY_T_DELETE_DATA_LOG_PRIMARY = UniqueKeys0.KEY_T_DELETE_DATA_LOG_PRIMARY;
    public static final UniqueKey<UserActiveRecord> KEY_T_USER_ACTIVE_PRIMARY = UniqueKeys0.KEY_T_USER_ACTIVE_PRIMARY;
    public static final UniqueKey<LoginCountDayRecord> KEY_T_LOGIN_COUNT_DAY_PRIMARY = UniqueKeys0.KEY_T_LOGIN_COUNT_DAY_PRIMARY;
    public static final UniqueKey<MemberDetailRecord> KEY_T_MEMBER_DETAIL_PRIMARY = UniqueKeys0.KEY_T_MEMBER_DETAIL_PRIMARY;
    public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_PRIMARY;
    public static final UniqueKey<OrganizationLoginCountDayRecord> KEY_T_ORGANIZATION_LOGIN_COUNT_DAY_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_LOGIN_COUNT_DAY_PRIMARY;
    public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = UniqueKeys0.KEY_T_ORGANIZATION_DETAIL_PRIMARY;
    public static final UniqueKey<HotResourceVisitRecord> KEY_T_HOT_RESOURCE_VISIT_PRIMARY = UniqueKeys0.KEY_T_HOT_RESOURCE_VISIT_PRIMARY;
    public static final UniqueKey<CourseInfoRecord> KEY_T_COURSE_INFO_PRIMARY = UniqueKeys0.KEY_T_COURSE_INFO_PRIMARY;
    // -------------------------------------------------------------------------
    // FOREIGN KEY definitions
    // -------------------------------------------------------------------------


    // -------------------------------------------------------------------------
    // [#1459] distribute members to avoid static initialisers > 64kb
    // -------------------------------------------------------------------------

    private static class UniqueKeys0 extends AbstractKeys {
        public static final UniqueKey<AuditLogRecord> KEY_T_AUDIT_LOG_PRIMARY = createUniqueKey(AuditLog.AUDIT_LOG, "KEY_t_audit_log_PRIMARY", AuditLog.AUDIT_LOG.ID);
        public static final UniqueKey<LoginLogRecord> KEY_T_LOGIN_LOG_PRIMARY = createUniqueKey(LoginLog.LOGIN_LOG, "KEY_t_login_log_PRIMARY", LoginLog.LOGIN_LOG.ID);
        public static final UniqueKey<OnlineStatisticsRecord> KEY_T_ONLINE_STATISTICS_PRIMARY = createUniqueKey(OnlineStatistics.ONLINE_STATISTICS, "KEY_t_online_statistics_PRIMARY", OnlineStatistics.ONLINE_STATISTICS.ID);
        public static final UniqueKey<WhiteRecordRecord> KEY_T_WHITE_RECORD_PRIMARY = createUniqueKey(WhiteRecord.WHITE_RECORD, "KEY_t_white_record_PRIMARY", WhiteRecord.WHITE_RECORD.ID);
        public static final UniqueKey<UserBehaviorRecord> KEY_T_USER_BEHAVIOR_PRIMARY = createUniqueKey(UserBehavior.USER_BEHAVIOR, "KEY_t_user_behavior_PRIMARY", UserBehavior.USER_BEHAVIOR.ID);
        public static final UniqueKey<MemberRecord> KEY_T_MEMBER_PRIMARY = createUniqueKey(Member.MEMBER, "KEY_t_member_PRIMARY", Member.MEMBER.ID);
        public static final UniqueKey<ResourceVisitRecord> KEY_T_RESOURCE_VISIT_PRIMARY = createUniqueKey(ResourceVisit.RESOURCE_VISIT, "KEY_t_resource_visit_PRIMARY", ResourceVisit.RESOURCE_VISIT.ID);
        public static final UniqueKey<DeleteDataLogRecord> KEY_T_DELETE_DATA_LOG_PRIMARY = createUniqueKey(DeleteDataLog.DELETE_DATA_LOG, "KEY_t_delete_data_log_PRIMARY", DeleteDataLog.DELETE_DATA_LOG.ID);
        public static final UniqueKey<UserActiveRecord> KEY_T_USER_ACTIVE_PRIMARY = createUniqueKey(UserActive.USER_ACTIVE, "KEY_t_user_active_PRIMARY", UserActive.USER_ACTIVE.ID);
        public static final UniqueKey<LoginCountDayRecord> KEY_T_LOGIN_COUNT_DAY_PRIMARY = createUniqueKey(LoginCountDay.LOGIN_COUNT_DAY, "KEY_t_login_count_day_PRIMARY", LoginCountDay.LOGIN_COUNT_DAY.ID);
        public static final UniqueKey<MemberDetailRecord> KEY_T_MEMBER_DETAIL_PRIMARY = createUniqueKey(MemberDetail.MEMBER_DETAIL, "KEY_t_member_detail_PRIMARY", MemberDetail.MEMBER_DETAIL.ID);
        public static final UniqueKey<OrganizationRecord> KEY_T_ORGANIZATION_PRIMARY = createUniqueKey(Organization.ORGANIZATION, "KEY_t_organization_PRIMARY", Organization.ORGANIZATION.ID);
        public static final UniqueKey<OrganizationLoginCountDayRecord> KEY_T_ORGANIZATION_LOGIN_COUNT_DAY_PRIMARY = createUniqueKey(OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY, "KEY_t_organization_login_count_day_PRIMARY", OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY.ID);
        public static final UniqueKey<OrganizationDetailRecord> KEY_T_ORGANIZATION_DETAIL_PRIMARY = createUniqueKey(OrganizationDetail.ORGANIZATION_DETAIL, "KEY_t_organization_detail_PRIMARY", OrganizationDetail.ORGANIZATION_DETAIL.ID);
        public static final UniqueKey<HotResourceVisitRecord> KEY_T_HOT_RESOURCE_VISIT_PRIMARY = createUniqueKey(HotResourceVisit.HOT_RESOURCE_VISIT, "KEY_t_hot_resource_visit_PRIMARY", HotResourceVisit.HOT_RESOURCE_VISIT.ID);
        public static final UniqueKey<CourseInfoRecord> KEY_T_COURSE_INFO_PRIMARY = createUniqueKey(CourseInfo.COURSE_INFO, "KEY_t_course_info_PRIMARY", CourseInfo.COURSE_INFO.ID);
    }
}
