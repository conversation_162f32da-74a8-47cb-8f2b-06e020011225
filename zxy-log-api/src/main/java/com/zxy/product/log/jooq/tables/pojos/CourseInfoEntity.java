/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.ICourseInfo;

import javax.annotation.Generated;


/**
 * otter同步课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfoEntity extends BaseEntity implements ICourseInfo {

    private static final long serialVersionUID = 1L;

    private String  name;
    private Integer businessType;
    private Integer status;
    private String  organizationId;
    private Integer deleteFlag;

    public CourseInfoEntity() {}

    public CourseInfoEntity(CourseInfoEntity value) {
        this.name = value.name;
        this.businessType = value.businessType;
        this.status = value.status;
        this.organizationId = value.organizationId;
        this.deleteFlag = value.deleteFlag;
    }

    public CourseInfoEntity(
        String  id,
        String  name,
        Integer businessType,
        Integer status,
        String  organizationId,
        Integer deleteFlag,
        Long    createTime
    ) {
        super.setId(id);
        this.name = name;
        this.businessType = businessType;
        this.status = status;
        this.organizationId = organizationId;
        this.deleteFlag = deleteFlag;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Integer getBusinessType() {
        return this.businessType;
    }

    @Override
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseInfoEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(businessType);
        sb.append(", ").append(status);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseInfo from) {
        setId(from.getId());
        setName(from.getName());
        setBusinessType(from.getBusinessType());
        setStatus(from.getStatus());
        setOrganizationId(from.getOrganizationId());
        setDeleteFlag(from.getDeleteFlag());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseInfo> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseInfoEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.CourseInfoRecord r = new com.zxy.product.log.jooq.tables.records.CourseInfoRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.ID, record.getValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.NAME, record.getValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.BUSINESS_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.BUSINESS_TYPE, record.getValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.BUSINESS_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.STATUS, record.getValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.ORGANIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.DELETE_FLAG.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.DELETE_FLAG, record.getValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.DELETE_FLAG));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO.CREATE_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
