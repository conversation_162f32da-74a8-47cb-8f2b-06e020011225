/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IMemberDetail;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberDetailEntity extends BaseEntity implements IMemberDetail {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  incumbencyStatus;
    private Long    entryDate;
    private String  headPortrait;
    private String  headPortraitPath;
    private String  customerType;
    private Long    expiryDate;
    private String  nationalityId;
    private String  ethnicityId;
    private String  politicalizationId;
    private String  educationId;
    private String  credentialType;
    private String  credentialValue;
    private Integer isLeader;
    private Long    joinDate;
    private Long    bornDate;
    private String  school;
    private Long    graduateDate;
    private String  summary;

    public MemberDetailEntity() {}

    public MemberDetailEntity(MemberDetailEntity value) {
        this.memberId = value.memberId;
        this.incumbencyStatus = value.incumbencyStatus;
        this.entryDate = value.entryDate;
        this.headPortrait = value.headPortrait;
        this.headPortraitPath = value.headPortraitPath;
        this.customerType = value.customerType;
        this.expiryDate = value.expiryDate;
        this.nationalityId = value.nationalityId;
        this.ethnicityId = value.ethnicityId;
        this.politicalizationId = value.politicalizationId;
        this.educationId = value.educationId;
        this.credentialType = value.credentialType;
        this.credentialValue = value.credentialValue;
        this.isLeader = value.isLeader;
        this.joinDate = value.joinDate;
        this.bornDate = value.bornDate;
        this.school = value.school;
        this.graduateDate = value.graduateDate;
        this.summary = value.summary;
    }

    public MemberDetailEntity(
        String  id,
        Long    createTime,
        String  memberId,
        String  incumbencyStatus,
        Long    entryDate,
        String  headPortrait,
        String  headPortraitPath,
        String  customerType,
        Long    expiryDate,
        String  nationalityId,
        String  ethnicityId,
        String  politicalizationId,
        String  educationId,
        String  credentialType,
        String  credentialValue,
        Integer isLeader,
        Long    joinDate,
        Long    bornDate,
        String  school,
        Long    graduateDate,
        String  summary
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.memberId = memberId;
        this.incumbencyStatus = incumbencyStatus;
        this.entryDate = entryDate;
        this.headPortrait = headPortrait;
        this.headPortraitPath = headPortraitPath;
        this.customerType = customerType;
        this.expiryDate = expiryDate;
        this.nationalityId = nationalityId;
        this.ethnicityId = ethnicityId;
        this.politicalizationId = politicalizationId;
        this.educationId = educationId;
        this.credentialType = credentialType;
        this.credentialValue = credentialValue;
        this.isLeader = isLeader;
        this.joinDate = joinDate;
        this.bornDate = bornDate;
        this.school = school;
        this.graduateDate = graduateDate;
        this.summary = summary;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getIncumbencyStatus() {
        return this.incumbencyStatus;
    }

    @Override
    public void setIncumbencyStatus(String incumbencyStatus) {
        this.incumbencyStatus = incumbencyStatus;
    }

    @Override
    public Long getEntryDate() {
        return this.entryDate;
    }

    @Override
    public void setEntryDate(Long entryDate) {
        this.entryDate = entryDate;
    }

    @Override
    public String getHeadPortrait() {
        return this.headPortrait;
    }

    @Override
    public void setHeadPortrait(String headPortrait) {
        this.headPortrait = headPortrait;
    }

    @Override
    public String getHeadPortraitPath() {
        return this.headPortraitPath;
    }

    @Override
    public void setHeadPortraitPath(String headPortraitPath) {
        this.headPortraitPath = headPortraitPath;
    }

    @Override
    public String getCustomerType() {
        return this.customerType;
    }

    @Override
    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    @Override
    public Long getExpiryDate() {
        return this.expiryDate;
    }

    @Override
    public void setExpiryDate(Long expiryDate) {
        this.expiryDate = expiryDate;
    }

    @Override
    public String getNationalityId() {
        return this.nationalityId;
    }

    @Override
    public void setNationalityId(String nationalityId) {
        this.nationalityId = nationalityId;
    }

    @Override
    public String getEthnicityId() {
        return this.ethnicityId;
    }

    @Override
    public void setEthnicityId(String ethnicityId) {
        this.ethnicityId = ethnicityId;
    }

    @Override
    public String getPoliticalizationId() {
        return this.politicalizationId;
    }

    @Override
    public void setPoliticalizationId(String politicalizationId) {
        this.politicalizationId = politicalizationId;
    }

    @Override
    public String getEducationId() {
        return this.educationId;
    }

    @Override
    public void setEducationId(String educationId) {
        this.educationId = educationId;
    }

    @Override
    public String getCredentialType() {
        return this.credentialType;
    }

    @Override
    public void setCredentialType(String credentialType) {
        this.credentialType = credentialType;
    }

    @Override
    public String getCredentialValue() {
        return this.credentialValue;
    }

    @Override
    public void setCredentialValue(String credentialValue) {
        this.credentialValue = credentialValue;
    }

    @Override
    public Integer getIsLeader() {
        return this.isLeader;
    }

    @Override
    public void setIsLeader(Integer isLeader) {
        this.isLeader = isLeader;
    }

    @Override
    public Long getJoinDate() {
        return this.joinDate;
    }

    @Override
    public void setJoinDate(Long joinDate) {
        this.joinDate = joinDate;
    }

    @Override
    public Long getBornDate() {
        return this.bornDate;
    }

    @Override
    public void setBornDate(Long bornDate) {
        this.bornDate = bornDate;
    }

    @Override
    public String getSchool() {
        return this.school;
    }

    @Override
    public void setSchool(String school) {
        this.school = school;
    }

    @Override
    public Long getGraduateDate() {
        return this.graduateDate;
    }

    @Override
    public void setGraduateDate(Long graduateDate) {
        this.graduateDate = graduateDate;
    }

    @Override
    public String getSummary() {
        return this.summary;
    }

    @Override
    public void setSummary(String summary) {
        this.summary = summary;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("MemberDetailEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(memberId);
        sb.append(", ").append(incumbencyStatus);
        sb.append(", ").append(entryDate);
        sb.append(", ").append(headPortrait);
        sb.append(", ").append(headPortraitPath);
        sb.append(", ").append(customerType);
        sb.append(", ").append(expiryDate);
        sb.append(", ").append(nationalityId);
        sb.append(", ").append(ethnicityId);
        sb.append(", ").append(politicalizationId);
        sb.append(", ").append(educationId);
        sb.append(", ").append(credentialType);
        sb.append(", ").append(credentialValue);
        sb.append(", ").append(isLeader);
        sb.append(", ").append(joinDate);
        sb.append(", ").append(bornDate);
        sb.append(", ").append(school);
        sb.append(", ").append(graduateDate);
        sb.append(", ").append(summary);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMemberDetail from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setMemberId(from.getMemberId());
        setIncumbencyStatus(from.getIncumbencyStatus());
        setEntryDate(from.getEntryDate());
        setHeadPortrait(from.getHeadPortrait());
        setHeadPortraitPath(from.getHeadPortraitPath());
        setCustomerType(from.getCustomerType());
        setExpiryDate(from.getExpiryDate());
        setNationalityId(from.getNationalityId());
        setEthnicityId(from.getEthnicityId());
        setPoliticalizationId(from.getPoliticalizationId());
        setEducationId(from.getEducationId());
        setCredentialType(from.getCredentialType());
        setCredentialValue(from.getCredentialValue());
        setIsLeader(from.getIsLeader());
        setJoinDate(from.getJoinDate());
        setBornDate(from.getBornDate());
        setSchool(from.getSchool());
        setGraduateDate(from.getGraduateDate());
        setSummary(from.getSummary());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMemberDetail> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends MemberDetailEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.MemberDetailRecord r = new com.zxy.product.log.jooq.tables.records.MemberDetailRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ID, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.MEMBER_ID, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.INCUMBENCY_STATUS) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.INCUMBENCY_STATUS, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.INCUMBENCY_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ENTRY_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ENTRY_DATE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ENTRY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT_PATH) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT_PATH, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.HEAD_PORTRAIT_PATH));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CUSTOMER_TYPE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CUSTOMER_TYPE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CUSTOMER_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.EXPIRY_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.EXPIRY_DATE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.EXPIRY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.NATIONALITY_ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.NATIONALITY_ID, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.NATIONALITY_ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ETHNICITY_ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ETHNICITY_ID, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.ETHNICITY_ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.POLITICALIZATION_ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.POLITICALIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.POLITICALIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.EDUCATION_ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.EDUCATION_ID, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.EDUCATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREDENTIAL_TYPE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREDENTIAL_TYPE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREDENTIAL_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREDENTIAL_VALUE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREDENTIAL_VALUE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.CREDENTIAL_VALUE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.IS_LEADER) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.IS_LEADER, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.IS_LEADER));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.JOIN_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.JOIN_DATE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.JOIN_DATE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.BORN_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.BORN_DATE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.BORN_DATE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.SCHOOL) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.SCHOOL, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.SCHOOL));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.GRADUATE_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.GRADUATE_DATE, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.GRADUATE_DATE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.SUMMARY) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.SUMMARY, record.getValue(com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL.SUMMARY));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
