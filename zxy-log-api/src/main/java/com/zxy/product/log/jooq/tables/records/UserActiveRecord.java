/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.UserActive;
import com.zxy.product.log.jooq.tables.interfaces.IUserActive;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户活跃表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserActiveRecord extends UpdatableRecordImpl<UserActiveRecord> implements Record10<String, String, Integer, Integer, Inte<PERSON>, Inte<PERSON>, Integer, Integer, <PERSON>, Timestamp>, IUserActive {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_user_active.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_member_id</code>. 人员id
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_member_id</code>. 人员id
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_login_times</code>. 登录次数
     */
    @Override
    public void setLoginTimes(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_login_times</code>. 登录次数
     */
    @Override
    public Integer getLoginTimes() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_year_info</code>. 年
     */
    @Override
    public void setYearInfo(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_year_info</code>. 年
     */
    @Override
    public Integer getYearInfo() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_month_info</code>. 月
     */
    @Override
    public void setMonthInfo(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_month_info</code>. 月
     */
    @Override
    public Integer getMonthInfo() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_day_info</code>. 日
     */
    @Override
    public void setDayInfo(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_day_info</code>. 日
     */
    @Override
    public Integer getDayInfo() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_quarter_info</code>. 季度
     */
    @Override
    public void setQuarterInfo(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_quarter_info</code>. 季度
     */
    @Override
    public Integer getQuarterInfo() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_week_info</code>. 周
     */
    @Override
    public void setWeekInfo(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_week_info</code>. 周
     */
    @Override
    public Integer getWeekInfo() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_user_active.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_user_active.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long, Timestamp> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long, Timestamp> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return UserActive.USER_ACTIVE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return UserActive.USER_ACTIVE.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return UserActive.USER_ACTIVE.LOGIN_TIMES;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return UserActive.USER_ACTIVE.YEAR_INFO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return UserActive.USER_ACTIVE.MONTH_INFO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return UserActive.USER_ACTIVE.DAY_INFO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return UserActive.USER_ACTIVE.QUARTER_INFO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return UserActive.USER_ACTIVE.WEEK_INFO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return UserActive.USER_ACTIVE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field10() {
        return UserActive.USER_ACTIVE.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getLoginTimes();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getYearInfo();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getMonthInfo();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getDayInfo();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getQuarterInfo();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getWeekInfo();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value10() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value3(Integer value) {
        setLoginTimes(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value4(Integer value) {
        setYearInfo(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value5(Integer value) {
        setMonthInfo(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value6(Integer value) {
        setDayInfo(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value7(Integer value) {
        setQuarterInfo(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value8(Integer value) {
        setWeekInfo(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value9(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord value10(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActiveRecord values(String value1, String value2, Integer value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8, Long value9, Timestamp value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IUserActive from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setLoginTimes(from.getLoginTimes());
        setYearInfo(from.getYearInfo());
        setMonthInfo(from.getMonthInfo());
        setDayInfo(from.getDayInfo());
        setQuarterInfo(from.getQuarterInfo());
        setWeekInfo(from.getWeekInfo());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IUserActive> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserActiveRecord
     */
    public UserActiveRecord() {
        super(UserActive.USER_ACTIVE);
    }

    /**
     * Create a detached, initialised UserActiveRecord
     */
    public UserActiveRecord(String id, String memberId, Integer loginTimes, Integer yearInfo, Integer monthInfo, Integer dayInfo, Integer quarterInfo, Integer weekInfo, Long createTime, Timestamp modifyDate) {
        super(UserActive.USER_ACTIVE);

        set(0, id);
        set(1, memberId);
        set(2, loginTimes);
        set(3, yearInfo);
        set(4, monthInfo);
        set(5, dayInfo);
        set(6, quarterInfo);
        set(7, weekInfo);
        set(8, createTime);
        set(9, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.UserActiveEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.UserActiveEntity pojo = (com.zxy.product.log.jooq.tables.pojos.UserActiveEntity)source;
        pojo.into(this);
        return true;
    }
}
