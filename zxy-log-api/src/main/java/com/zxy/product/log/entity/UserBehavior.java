package com.zxy.product.log.entity;

import com.zxy.product.log.jooq.tables.pojos.UserBehaviorEntity;

public class UserBehavior extends UserBehaviorEntity {
    /**
     * type类型 9：点击，10：发现点击
     */
    public static final String CLICK="9";//点击
    public static final String DISCOVER_CLICK="10";//发现点击

    /**
     * contentType内容类型 1：专题，2：课程，3：知识，4：直播，5：话题，6：文章，7：问题，8：调研，9：考试，10：班级，11：培训班
     */
    public static final String SUBJECT="1";//专题
    public static final String COURSE="2";//课程
    public static final String KNOWLEDGE="3";//知识
    public static final String GENSEE="4";//直播
    public static final String TOPIC="5";//话题
    public static final String ARTICLE="6";//文章
    public static final String QUESTION="7";//问题
    public static final String RESEARCH="8";//调研
    public static final String EXAM="9";//考试
    public static final String CLASS="10";//班级
    public static final String TRAIN="11";//培训班

    /**
     * 客户端类型：0：PC；1：APP
     */
    public static final String CLIENT_TYPE_PC = "0"; // PC
    public static final String CLIENT_TYPE_APP = "1"; // APP

    /**
     * 状态
     */
    public static final String DELETE_FLASE = "0"; //状态：未删除
    public static final String DELETE_TRUE = "1"; //状态，已删除




}
