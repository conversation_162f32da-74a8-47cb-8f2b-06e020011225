/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.OrganizationLoginCountDayRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 各组织日登陆统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationLoginCountDay extends TableImpl<OrganizationLoginCountDayRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_organization_login_count_day</code>
     */
    public static final OrganizationLoginCountDay ORGANIZATION_LOGIN_COUNT_DAY = new OrganizationLoginCountDay();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<OrganizationLoginCountDayRecord> getRecordType() {
        return OrganizationLoginCountDayRecord.class;
    }

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_id</code>. 主键
     */
    public final TableField<OrganizationLoginCountDayRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_day</code>. 日
     */
    public final TableField<OrganizationLoginCountDayRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER, this, "日");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_month</code>. 月
     */
    public final TableField<OrganizationLoginCountDayRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER, this, "月");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_year</code>. 年
     */
    public final TableField<OrganizationLoginCountDayRecord, Integer> YEAR = createField("f_year", org.jooq.impl.SQLDataType.INTEGER, this, "年");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_member_num</code>. 登录人数
     */
    public final TableField<OrganizationLoginCountDayRecord, Integer> MEMBER_NUM = createField("f_member_num", org.jooq.impl.SQLDataType.INTEGER, this, "登录人数");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_member_time</code>. 登录人次
     */
    public final TableField<OrganizationLoginCountDayRecord, Integer> MEMBER_TIME = createField("f_member_time", org.jooq.impl.SQLDataType.INTEGER, this, "登录人次");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_organization_num</code>. 各组织可用总人数
     */
    public final TableField<OrganizationLoginCountDayRecord, Integer> ORGANIZATION_NUM = createField("f_organization_num", org.jooq.impl.SQLDataType.INTEGER, this, "各组织可用总人数");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_organization_id</code>. 组织id
     */
    public final TableField<OrganizationLoginCountDayRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "组织id");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_create_time</code>. 创建时间
     */
    public final TableField<OrganizationLoginCountDayRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>zxy-log.t_organization_login_count_day.f_modify_date</code>. 修改时间
     */
    public final TableField<OrganizationLoginCountDayRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>zxy-log.t_organization_login_count_day</code> table reference
     */
    public OrganizationLoginCountDay() {
        this("t_organization_login_count_day", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_organization_login_count_day</code> table reference
     */
    public OrganizationLoginCountDay(String alias) {
        this(alias, ORGANIZATION_LOGIN_COUNT_DAY);
    }

    private OrganizationLoginCountDay(String alias, Table<OrganizationLoginCountDayRecord> aliased) {
        this(alias, aliased, null);
    }

    private OrganizationLoginCountDay(String alias, Table<OrganizationLoginCountDayRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "各组织日登陆统计表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<OrganizationLoginCountDayRecord> getPrimaryKey() {
        return Keys.KEY_T_ORGANIZATION_LOGIN_COUNT_DAY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<OrganizationLoginCountDayRecord>> getKeys() {
        return Arrays.<UniqueKey<OrganizationLoginCountDayRecord>>asList(Keys.KEY_T_ORGANIZATION_LOGIN_COUNT_DAY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationLoginCountDay as(String alias) {
        return new OrganizationLoginCountDay(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public OrganizationLoginCountDay rename(String name) {
        return new OrganizationLoginCountDay(name, null);
    }
}
