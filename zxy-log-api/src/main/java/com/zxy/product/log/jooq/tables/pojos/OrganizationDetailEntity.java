/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IOrganizationDetail;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationDetailEntity extends BaseEntity implements IOrganizationDetail {

    private static final long serialVersionUID = 1L;

    private String root;
    private String sub;

    public OrganizationDetailEntity() {}

    public OrganizationDetailEntity(OrganizationDetailEntity value) {
        this.root = value.root;
        this.sub = value.sub;
    }

    public OrganizationDetailEntity(
        String id,
        String root,
        String sub,
        Long   createTime
    ) {
        super.setId(id);
        this.root = root;
        this.sub = sub;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getRoot() {
        return this.root;
    }

    @Override
    public void setRoot(String root) {
        this.root = root;
    }

    @Override
    public String getSub() {
        return this.sub;
    }

    @Override
    public void setSub(String sub) {
        this.sub = sub;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("OrganizationDetailEntity (");

        sb.append(getId());
        sb.append(", ").append(root);
        sb.append(", ").append(sub);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOrganizationDetail from) {
        setId(from.getId());
        setRoot(from.getRoot());
        setSub(from.getSub());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOrganizationDetail> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends OrganizationDetailEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.OrganizationDetailRecord r = new com.zxy.product.log.jooq.tables.records.OrganizationDetailRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.ID, record.getValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.ROOT) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.ROOT, record.getValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.ROOT));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.SUB) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.SUB, record.getValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.SUB));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
