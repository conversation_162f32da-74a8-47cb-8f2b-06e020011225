/*
 * This file is generated by j<PERSON><PERSON><PERSON>.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.LoginCountDay;
import com.zxy.product.log.jooq.tables.interfaces.ILoginCountDay;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record16;
import org.jooq.Row16;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 用户登录每日统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LoginCountDayRecord extends UpdatableRecordImpl<LoginCountDayRecord> implements Record16<String, Integer, Inte<PERSON>, Inte<PERSON>, <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Timestamp>, ILoginCountDay {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_day</code>. 日
     */
    @Override
    public void setDay(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_day</code>. 日
     */
    @Override
    public Integer getDay() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_month</code>. 月
     */
    @Override
    public void setMonth(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_month</code>. 月
     */
    @Override
    public Integer getMonth() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_year</code>. 年
     */
    @Override
    public void setYear(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_year</code>. 年
     */
    @Override
    public Integer getYear() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_member_num</code>. 登录人数
     */
    @Override
    public void setMemberNum(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_member_num</code>. 登录人数
     */
    @Override
    public Integer getMemberNum() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_member_time</code>. 登录人次
     */
    @Override
    public void setMemberTime(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_member_time</code>. 登录人次
     */
    @Override
    public Integer getMemberTime() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_system_num</code>. 系统总人数
     */
    @Override
    public void setSystemNum(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_system_num</code>. 系统总人数
     */
    @Override
    public Integer getSystemNum() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_male_num</code>. 男登录人数
     */
    @Override
    public void setMaleNum(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_male_num</code>. 男登录人数
     */
    @Override
    public Integer getMaleNum() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_female_num</code>. 女登录人数
     */
    @Override
    public void setFemaleNum(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_female_num</code>. 女登录人数
     */
    @Override
    public Integer getFemaleNum() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_zero_two</code>. 入职年限0~2年登录人数
     */
    @Override
    public void setEntryYearZeroTwo(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_zero_two</code>. 入职年限0~2年登录人数
     */
    @Override
    public Integer getEntryYearZeroTwo() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_three_five</code>. 入职年限3~5年登录人数
     */
    @Override
    public void setEntryYearThreeFive(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_three_five</code>. 入职年限3~5年登录人数
     */
    @Override
    public Integer getEntryYearThreeFive() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_six_ten</code>. 入职年限6~10年登录人数
     */
    @Override
    public void setEntryYearSixTen(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_six_ten</code>. 入职年限6~10年登录人数
     */
    @Override
    public Integer getEntryYearSixTen() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_ten</code>. 入职年限10年以上登录人数
     */
    @Override
    public void setEntryYearTen(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_ten</code>. 入职年限10年以上登录人数
     */
    @Override
    public Integer getEntryYearTen() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_political_outlook</code>. 政治面貌(中共党员（含预备党员）)登录人数
     */
    @Override
    public void setPoliticalOutlook(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_political_outlook</code>. 政治面貌(中共党员（含预备党员）)登录人数
     */
    @Override
    public Integer getPoliticalOutlook() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(15, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(15);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record16 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row16<String, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Long, Timestamp> fieldsRow() {
        return (Row16) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row16<String, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer, Long, Timestamp> valuesRow() {
        return (Row16) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return LoginCountDay.LOGIN_COUNT_DAY.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field2() {
        return LoginCountDay.LOGIN_COUNT_DAY.DAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return LoginCountDay.LOGIN_COUNT_DAY.MONTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return LoginCountDay.LOGIN_COUNT_DAY.YEAR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return LoginCountDay.LOGIN_COUNT_DAY.MEMBER_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return LoginCountDay.LOGIN_COUNT_DAY.MEMBER_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return LoginCountDay.LOGIN_COUNT_DAY.SYSTEM_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return LoginCountDay.LOGIN_COUNT_DAY.MALE_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return LoginCountDay.LOGIN_COUNT_DAY.FEMALE_NUM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_ZERO_TWO;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_THREE_FIVE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_SIX_TEN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field13() {
        return LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_TEN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field14() {
        return LoginCountDay.LOGIN_COUNT_DAY.POLITICAL_OUTLOOK;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field15() {
        return LoginCountDay.LOGIN_COUNT_DAY.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field16() {
        return LoginCountDay.LOGIN_COUNT_DAY.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value2() {
        return getDay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getMonth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getYear();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getMemberNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getMemberTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getSystemNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getMaleNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getFemaleNum();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getEntryYearZeroTwo();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getEntryYearThreeFive();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getEntryYearSixTen();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value13() {
        return getEntryYearTen();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value14() {
        return getPoliticalOutlook();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value15() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value16() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value2(Integer value) {
        setDay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value3(Integer value) {
        setMonth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value4(Integer value) {
        setYear(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value5(Integer value) {
        setMemberNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value6(Integer value) {
        setMemberTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value7(Integer value) {
        setSystemNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value8(Integer value) {
        setMaleNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value9(Integer value) {
        setFemaleNum(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value10(Integer value) {
        setEntryYearZeroTwo(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value11(Integer value) {
        setEntryYearThreeFive(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value12(Integer value) {
        setEntryYearSixTen(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value13(Integer value) {
        setEntryYearTen(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value14(Integer value) {
        setPoliticalOutlook(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value15(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord value16(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDayRecord values(String value1, Integer value2, Integer value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8, Integer value9, Integer value10, Integer value11, Integer value12, Integer value13, Integer value14, Long value15, Timestamp value16) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILoginCountDay from) {
        setId(from.getId());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setMemberNum(from.getMemberNum());
        setMemberTime(from.getMemberTime());
        setSystemNum(from.getSystemNum());
        setMaleNum(from.getMaleNum());
        setFemaleNum(from.getFemaleNum());
        setEntryYearZeroTwo(from.getEntryYearZeroTwo());
        setEntryYearThreeFive(from.getEntryYearThreeFive());
        setEntryYearSixTen(from.getEntryYearSixTen());
        setEntryYearTen(from.getEntryYearTen());
        setPoliticalOutlook(from.getPoliticalOutlook());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILoginCountDay> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LoginCountDayRecord
     */
    public LoginCountDayRecord() {
        super(LoginCountDay.LOGIN_COUNT_DAY);
    }

    /**
     * Create a detached, initialised LoginCountDayRecord
     */
    public LoginCountDayRecord(String id, Integer day, Integer month, Integer year, Integer memberNum, Integer memberTime, Integer systemNum, Integer maleNum, Integer femaleNum, Integer entryYearZeroTwo, Integer entryYearThreeFive, Integer entryYearSixTen, Integer entryYearTen, Integer politicalOutlook, Long createTime, Timestamp modifyDate) {
        super(LoginCountDay.LOGIN_COUNT_DAY);

        set(0, id);
        set(1, day);
        set(2, month);
        set(3, year);
        set(4, memberNum);
        set(5, memberTime);
        set(6, systemNum);
        set(7, maleNum);
        set(8, femaleNum);
        set(9, entryYearZeroTwo);
        set(10, entryYearThreeFive);
        set(11, entryYearSixTen);
        set(12, entryYearTen);
        set(13, politicalOutlook);
        set(14, createTime);
        set(15, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.LoginCountDayEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.LoginCountDayEntity pojo = (com.zxy.product.log.jooq.tables.pojos.LoginCountDayEntity)source;
        pojo.into(this);
        return true;
    }
}
