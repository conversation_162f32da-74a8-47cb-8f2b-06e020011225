package com.zxy.product.log.entity;

import java.io.Serializable;

/**
 * Created by xiewp on 18/2/24
 */
public class OnlineInfo implements Serializable{

	private static final long serialVersionUID = 5668103931100812497L;

	private String memberId;
	
	private String companyId;
	
	private String phoneSystem;
	
	private String phoneModel;
	
	private String appIP;
	
	private int appLoginTime;
	
	private int appExpirationTime;
	
	private String pcSystem;
	
	private String pcBrowser;
	
	private String pcIP;
	
	private int pcLoginTime;
	
	private int pcExpirationTime;

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public String getCompanyId() {
		return companyId;
	}

	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	public String getPhoneSystem() {
		return phoneSystem;
	}

	public void setPhoneSystem(String phoneSystem) {
		this.phoneSystem = phoneSystem;
	}

	public String getPhoneModel() {
		return phoneModel;
	}

	public void setPhoneModel(String phoneModel) {
		this.phoneModel = phoneModel;
	}

	public String getAppIP() {
		return appIP;
	}

	public void setAppIP(String appIP) {
		this.appIP = appIP;
	}

	public int getAppLoginTime() {
		return appLoginTime;
	}

	public void setAppLoginTime(int appLoginTime) {
		this.appLoginTime = appLoginTime;
	}

	public int getAppExpirationTime() {
		return appExpirationTime;
	}

	public void setAppExpirationTime(int appExpirationTime) {
		this.appExpirationTime = appExpirationTime;
	}

	public String getPcSystem() {
		return pcSystem;
	}

	public void setPcSystem(String pcSystem) {
		this.pcSystem = pcSystem;
	}

	public String getPcBrowser() {
		return pcBrowser;
	}

	public void setPcBrowser(String pcBrowser) {
		this.pcBrowser = pcBrowser;
	}

	public String getPcIP() {
		return pcIP;
	}

	public void setPcIP(String pcIP) {
		this.pcIP = pcIP;
	}

	public int getPcLoginTime() {
		return pcLoginTime;
	}

	public void setPcLoginTime(int pcLoginTime) {
		this.pcLoginTime = pcLoginTime;
	}

	public int getPcExpirationTime() {
		return pcExpirationTime;
	}

	public void setPcExpirationTime(int pcExpirationTime) {
		this.pcExpirationTime = pcExpirationTime;
	}

	@Override
	public String toString() {
		return "OnlineInfo [memberId=" + memberId + ", companyId=" + companyId + ", phoneSystem=" + phoneSystem
				+ ", phoneModel=" + phoneModel + ", appIP=" + appIP + ", appLoginTime=" + appLoginTime
				+ ", appExpirationTime=" + appExpirationTime + ", pcSystem=" + pcSystem + ", pcBrowser=" + pcBrowser
				+ ", pcIP=" + pcIP + ", pcLoginTime=" + pcLoginTime + ", pcExpirationTime=" + pcExpirationTime + "]";
	}
}
