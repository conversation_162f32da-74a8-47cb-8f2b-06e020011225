/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 在线人数统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IOnlineStatistics extends Serializable {

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_online_number</code>. 在线人数
     */
    public void setOnlineNumber(Integer value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_online_number</code>. 在线人数
     */
    public Integer getOnlineNumber();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_pc_online_number</code>. pc在线人数
     */
    public void setPcOnlineNumber(Integer value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_pc_online_number</code>. pc在线人数
     */
    public Integer getPcOnlineNumber();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_app_online_number</code>. app在线人数
     */
    public void setAppOnlineNumber(Integer value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_app_online_number</code>. app在线人数
     */
    public Integer getAppOnlineNumber();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_hour</code>. 小时
     */
    public void setHour(Integer value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_hour</code>. 小时
     */
    public Integer getHour();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_day</code>. 日
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_day</code>. 日
     */
    public Integer getDay();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_month</code>. 月
     */
    public void setMonth(Integer value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_month</code>. 月
     */
    public Integer getMonth();

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IOnlineStatistics
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.IOnlineStatistics from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IOnlineStatistics
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.IOnlineStatistics> E into(E into);
}
