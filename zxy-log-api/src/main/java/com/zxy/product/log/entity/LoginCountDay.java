package com.zxy.product.log.entity;

import com.zxy.product.log.jooq.tables.pojos.LoginCountDayEntity;

import java.math.BigDecimal;

public class LoginCountDay extends LoginCountDayEntity implements Comparable<LoginCountDay>{
    private static final long serialVersionUID = 2707607165072901252L;

    private String time;
    private BigDecimal activityRadio = BigDecimal.ZERO;
    private BigDecimal memberTimes   = BigDecimal.ZERO;

    public LoginCountDay() {
    }

    public LoginCountDay(String time) {
        this.time = time;
    }

    public LoginCountDay(String time,Integer day) {
        this.time = time;
        super.setDay(day);
    }
    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public BigDecimal getActivityRadio() {
        return activityRadio;
    }

    public void setActivityRadio(BigDecimal activityRadio) {
        this.activityRadio = activityRadio;
    }

    public BigDecimal getMemberTimes() {
        return memberTimes;
    }

    public void setMemberTimes(BigDecimal memberTimes) {
        this.memberTimes = memberTimes;
    }

    @Override
    public int compareTo(LoginCountDay o) {
        return this.time.compareTo(o.time);
    }
}
