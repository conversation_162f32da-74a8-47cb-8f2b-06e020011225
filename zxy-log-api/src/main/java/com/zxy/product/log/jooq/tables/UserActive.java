/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.UserActiveRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 用户活跃表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserActive extends TableImpl<UserActiveRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_user_active</code>
     */
    public static final UserActive USER_ACTIVE = new UserActive();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserActiveRecord> getRecordType() {
        return UserActiveRecord.class;
    }

    /**
     * The column <code>zxy-log.t_user_active.f_id</code>. 主键
     */
    public final TableField<UserActiveRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>zxy-log.t_user_active.f_member_id</code>. 人员id
     */
    public final TableField<UserActiveRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "人员id");

    /**
     * The column <code>zxy-log.t_user_active.f_login_times</code>. 登录次数
     */
    public final TableField<UserActiveRecord, Integer> LOGIN_TIMES = createField("f_login_times", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "登录次数");

    /**
     * The column <code>zxy-log.t_user_active.f_year_info</code>. 年
     */
    public final TableField<UserActiveRecord, Integer> YEAR_INFO = createField("f_year_info", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "年");

    /**
     * The column <code>zxy-log.t_user_active.f_month_info</code>. 月
     */
    public final TableField<UserActiveRecord, Integer> MONTH_INFO = createField("f_month_info", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "月");

    /**
     * The column <code>zxy-log.t_user_active.f_day_info</code>. 日
     */
    public final TableField<UserActiveRecord, Integer> DAY_INFO = createField("f_day_info", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "日");

    /**
     * The column <code>zxy-log.t_user_active.f_quarter_info</code>. 季度
     */
    public final TableField<UserActiveRecord, Integer> QUARTER_INFO = createField("f_quarter_info", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "季度");

    /**
     * The column <code>zxy-log.t_user_active.f_week_info</code>. 周
     */
    public final TableField<UserActiveRecord, Integer> WEEK_INFO = createField("f_week_info", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "周");

    /**
     * The column <code>zxy-log.t_user_active.f_create_time</code>. 创建时间
     */
    public final TableField<UserActiveRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>zxy-log.t_user_active.f_modify_date</code>. 修改时间
     */
    public final TableField<UserActiveRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>zxy-log.t_user_active</code> table reference
     */
    public UserActive() {
        this("t_user_active", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_user_active</code> table reference
     */
    public UserActive(String alias) {
        this(alias, USER_ACTIVE);
    }

    private UserActive(String alias, Table<UserActiveRecord> aliased) {
        this(alias, aliased, null);
    }

    private UserActive(String alias, Table<UserActiveRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "用户活跃表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<UserActiveRecord> getPrimaryKey() {
        return Keys.KEY_T_USER_ACTIVE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<UserActiveRecord>> getKeys() {
        return Arrays.<UniqueKey<UserActiveRecord>>asList(Keys.KEY_T_USER_ACTIVE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserActive as(String alias) {
        return new UserActive(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserActive rename(String name) {
        return new UserActive(name, null);
    }
}
