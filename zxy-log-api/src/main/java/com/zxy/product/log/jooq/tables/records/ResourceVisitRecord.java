/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.ResourceVisit;
import com.zxy.product.log.jooq.tables.interfaces.IResourceVisit;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 资源浏览统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResourceVisitRecord extends UpdatableRecordImpl<ResourceVisitRecord> implements Record7<String, String, String, Integer, Integer, Integer, Long>, IResourceVisit {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_content_id</code>. 内容id
     */
    @Override
    public void setContentId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_content_id</code>. 内容id
     */
    @Override
    public String getContentId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_content_name</code>. 内容名称
     */
    @Override
    public void setContentName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_content_name</code>. 内容名称
     */
    @Override
    public String getContentName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_content_type</code>. 内容类型：1-专题，2-课程
     */
    @Override
    public void setContentType(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_content_type</code>. 内容类型：1-专题，2-课程
     */
    @Override
    public Integer getContentType() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_visit</code>. 每日浏览量
     */
    @Override
    public void setVisit(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_visit</code>. 每日浏览量
     */
    @Override
    public Integer getVisit() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_day</code>. 日期YYYYMMDD
     */
    @Override
    public void setDay(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_day</code>. 日期YYYYMMDD
     */
    @Override
    public Integer getDay() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Integer, Integer, Long> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, String, Integer, Integer, Integer, Long> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ResourceVisit.RESOURCE_VISIT.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ResourceVisit.RESOURCE_VISIT.CONTENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return ResourceVisit.RESOURCE_VISIT.CONTENT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return ResourceVisit.RESOURCE_VISIT.CONTENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return ResourceVisit.RESOURCE_VISIT.VISIT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return ResourceVisit.RESOURCE_VISIT.DAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return ResourceVisit.RESOURCE_VISIT.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getContentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getContentName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getContentType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getVisit();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getDay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord value2(String value) {
        setContentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord value3(String value) {
        setContentName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord value4(Integer value) {
        setContentType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord value5(Integer value) {
        setVisit(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord value6(Integer value) {
        setDay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord value7(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResourceVisitRecord values(String value1, String value2, String value3, Integer value4, Integer value5, Integer value6, Long value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IResourceVisit from) {
        setId(from.getId());
        setContentId(from.getContentId());
        setContentName(from.getContentName());
        setContentType(from.getContentType());
        setVisit(from.getVisit());
        setDay(from.getDay());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IResourceVisit> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResourceVisitRecord
     */
    public ResourceVisitRecord() {
        super(ResourceVisit.RESOURCE_VISIT);
    }

    /**
     * Create a detached, initialised ResourceVisitRecord
     */
    public ResourceVisitRecord(String id, String contentId, String contentName, Integer contentType, Integer visit, Integer day, Long createTime) {
        super(ResourceVisit.RESOURCE_VISIT);

        set(0, id);
        set(1, contentId);
        set(2, contentName);
        set(3, contentType);
        set(4, visit);
        set(5, day);
        set(6, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.ResourceVisitEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.ResourceVisitEntity pojo = (com.zxy.product.log.jooq.tables.pojos.ResourceVisitEntity)source;
        pojo.into(this);
        return true;
    }
}
