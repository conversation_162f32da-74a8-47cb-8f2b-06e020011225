/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.LoginLog;
import com.zxy.product.log.jooq.tables.interfaces.ILoginLog;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 登录日至表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LoginLogRecord extends UpdatableRecordImpl<LoginLogRecord> implements Record13<String, String, String, String, Integer, String, Integer, String, String, Long, Integer, Integer, Timestamp>, ILoginLog {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_login_log.f_id</code>. 主键
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_id</code>. 主键
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_organization_id</code>. 组织机构id
     */
    @Override
    public void setOrganizationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_organization_id</code>. 组织机构id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_member_id</code>. 人员id
     */
    @Override
    public void setMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_member_id</code>. 人员id
     */
    @Override
    public String getMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_ip_addr</code>. ip 地址
     */
    @Override
    public void setIpAddr(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_ip_addr</code>. ip 地址
     */
    @Override
    public String getIpAddr() {
        return (String) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_terminal_type</code>. 1.pc 2.app
     */
    @Override
    public void setTerminalType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_terminal_type</code>. 1.pc 2.app
     */
    @Override
    public Integer getTerminalType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_terminal</code>. 终端名称 pc对应浏览器，app对应手机型号
     */
    @Override
    public void setTerminal(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_terminal</code>. 终端名称 pc对应浏览器，app对应手机型号
     */
    @Override
    public String getTerminal() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_status</code>. 1.登录成功 2.登录失败
     */
    @Override
    public void setStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_status</code>. 1.登录成功 2.登录失败
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_system</code>. 登录系统 pc对应系统 app对应手机系统
     */
    @Override
    public void setSystem(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_system</code>. 登录系统 pc对应系统 app对应手机系统
     */
    @Override
    public String getSystem() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_user_agent</code>. 登录UserAgent
     */
    @Override
    public void setUserAgent(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_user_agent</code>. 登录UserAgent
     */
    @Override
    public String getUserAgent() {
        return (String) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    @Override
    public void setPasswordType(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    @Override
    public Integer getPasswordType() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    @Override
    public void setSource(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    @Override
    public Integer getSource() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>zxy-log.t_login_log.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(12, value);
    }

    /**
     * Getter for <code>zxy-log.t_login_log.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(12);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record13 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, String, Integer, String, Integer, String, String, Long, Integer, Integer, Timestamp> fieldsRow() {
        return (Row13) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row13<String, String, String, String, Integer, String, Integer, String, String, Long, Integer, Integer, Timestamp> valuesRow() {
        return (Row13) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return LoginLog.LOGIN_LOG.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return LoginLog.LOGIN_LOG.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return LoginLog.LOGIN_LOG.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return LoginLog.LOGIN_LOG.IP_ADDR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return LoginLog.LOGIN_LOG.TERMINAL_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return LoginLog.LOGIN_LOG.TERMINAL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return LoginLog.LOGIN_LOG.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return LoginLog.LOGIN_LOG.SYSTEM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return LoginLog.LOGIN_LOG.USER_AGENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return LoginLog.LOGIN_LOG.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return LoginLog.LOGIN_LOG.PASSWORD_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return LoginLog.LOGIN_LOG.SOURCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field13() {
        return LoginLog.LOGIN_LOG.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getIpAddr();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getTerminalType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getTerminal();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getSystem();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getUserAgent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getPasswordType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getSource();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value13() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value2(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value3(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value4(String value) {
        setIpAddr(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value5(Integer value) {
        setTerminalType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value6(String value) {
        setTerminal(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value7(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value8(String value) {
        setSystem(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value9(String value) {
        setUserAgent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value10(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value11(Integer value) {
        setPasswordType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value12(Integer value) {
        setSource(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord value13(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginLogRecord values(String value1, String value2, String value3, String value4, Integer value5, String value6, Integer value7, String value8, String value9, Long value10, Integer value11, Integer value12, Timestamp value13) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILoginLog from) {
        setId(from.getId());
        setOrganizationId(from.getOrganizationId());
        setMemberId(from.getMemberId());
        setIpAddr(from.getIpAddr());
        setTerminalType(from.getTerminalType());
        setTerminal(from.getTerminal());
        setStatus(from.getStatus());
        setSystem(from.getSystem());
        setUserAgent(from.getUserAgent());
        setCreateTime(from.getCreateTime());
        setPasswordType(from.getPasswordType());
        setSource(from.getSource());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILoginLog> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached LoginLogRecord
     */
    public LoginLogRecord() {
        super(LoginLog.LOGIN_LOG);
    }

    /**
     * Create a detached, initialised LoginLogRecord
     */
    public LoginLogRecord(String id, String organizationId, String memberId, String ipAddr, Integer terminalType, String terminal, Integer status, String system, String userAgent, Long createTime, Integer passwordType, Integer source, Timestamp modifyDate) {
        super(LoginLog.LOGIN_LOG);

        set(0, id);
        set(1, organizationId);
        set(2, memberId);
        set(3, ipAddr);
        set(4, terminalType);
        set(5, terminal);
        set(6, status);
        set(7, system);
        set(8, userAgent);
        set(9, createTime);
        set(10, passwordType);
        set(11, source);
        set(12, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.LoginLogEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.LoginLogEntity pojo = (com.zxy.product.log.jooq.tables.pojos.LoginLogEntity)source;
        pojo.into(this);
        return true;
    }
}
