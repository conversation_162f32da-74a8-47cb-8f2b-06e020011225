/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 资源浏览统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IResourceVisit extends Serializable {

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_content_id</code>. 内容id
     */
    public void setContentId(String value);

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_content_id</code>. 内容id
     */
    public String getContentId();

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_content_name</code>. 内容名称
     */
    public void setContentName(String value);

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_content_name</code>. 内容名称
     */
    public String getContentName();

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_content_type</code>. 内容类型：1-专题，2-课程
     */
    public void setContentType(Integer value);

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_content_type</code>. 内容类型：1-专题，2-课程
     */
    public Integer getContentType();

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_visit</code>. 每日浏览量
     */
    public void setVisit(Integer value);

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_visit</code>. 每日浏览量
     */
    public Integer getVisit();

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_day</code>. 日期YYYYMMDD
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_day</code>. 日期YYYYMMDD
     */
    public Integer getDay();

    /**
     * Setter for <code>zxy-log.t_resource_visit.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_resource_visit.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IResourceVisit
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.IResourceVisit from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IResourceVisit
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.IResourceVisit> E into(E into);
}
