package com.zxy.product.log.content.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 限流：业务枚举
 * <AUTHOR>
 * @date 2024年12月12日 15:17
 */
public enum FlowLimitEnum {
    /**无效限流，业务上直接通过*/
    LimitInvalid(-1,""),

    /**登录*/
    OauthProvider(0,"oauth-provider"),

    /**首页*/
    Home(1,"home"),

    /**课程列表页*/
    CoursePage(2,"course-page"),

    /**专题列表*/
    SubjectPage(3,"subject-page"),

    /**知识列表*/
    KnowledgePage(4,"knowledge-page"),

    /**活动列表*/
    ActivityPage(5,"activity-page"),

    /**问吧列表*/
    AskPage(6,"ask-page"),

    /**专家工作室列表*/
    ExpertPage(7,"expert-page"),

    /**考试列表*/
    ExamPage(8,"exam-page"),

    /**个人中心*/
    PersonalCenter(9,"personal-center"),

    /**个人画像*/
    SinglePortrait(10,"single-portrait"),

    /**群体画像*/
    GroupPortrait(11,"group-portrait"),

    /**课程详情*/
    CourseInfo(100,"course-info"),

    /**专题详情*/
    SubjectInfo(101,"subject-info"),

    /**考试提交*/
    ExamSubmit(102,"exam-submit"),

    /**PK赛答题*/
    PkSubmit(103,"pk-submit"),;

    Integer businessType;
    String applicationName;

    FlowLimitEnum(Integer businessType, String applicationName) {
        this.businessType=businessType;
        this.applicationName=applicationName;
    }

    public Integer getBusinessType(){return this.businessType; }

    /**限流：仅QPS相关业务类型集合数据（不含普通资源）*/
    public static List<Integer> keyQpsCollect() {
        return Arrays.asList(
                OauthProvider.getBusinessType(), Home.getBusinessType(),
                CoursePage.getBusinessType(), SubjectPage.getBusinessType(), ExpertPage.getBusinessType(),
                ActivityPage.getBusinessType(), ExamPage.getBusinessType(), KnowledgePage.getBusinessType(),
                AskPage.getBusinessType(), PersonalCenter.getBusinessType(), SinglePortrait.getBusinessType(),
                GroupPortrait.getBusinessType());
    }

    /**限流：QPS（包含核心资源 与 非核心资源）业务类型集合数据*/
    public static List<Integer> keyCoreQpsCollect(){ return Collections.singletonList(SubjectInfo.getBusinessType()); }

    /**限流：QPS与最大在线（包含核心资源与非核心资源）业务类型集合数据*/
    public static List<Integer> keyCoreOnlineQpsCollect(){ return Collections.singletonList(CourseInfo.getBusinessType()); }
}
