package com.zxy.product.log.content;

import com.zxy.common.base.exception.Code;

/**
 * Created by tianc on 2019/4/3.
 */
public enum ErrorCode implements Code{
    DateCheckFailed(89757, "data check failed"),
    OnlineTypeError(89758, "在线人数统计类型获取错误"),
    DATA_NOT_EXISTS(89759, "数据不存在"),
    RESOURCE_STATUS_NOT_UPDATED(89760, "热门学习资源状态更新失败"),
    RESOURCE_SHOW_COUNT_MORE_THEN_3(89761, "热门学习资源显示数不能超过3个"),

    /**QPS相关限制提示语*/
    RequestIsTooFrequentTryAgainLater(89761,"请求频率太高,请稍后重试")
    ;
    private final int code;

    ErrorCode(int code, String desc) {
        this.code = code;
    }

    @Override
    public int getCode() {
        return code;
    }

}
