package com.zxy.product.log.entity;

import com.zxy.product.log.jooq.tables.pojos.ResourceVisitEntity;

/**
 * <AUTHOR> zhouyong
 */
public class ResourceVisit extends ResourceVisitEntity {

    private static final long serialVersionUID = -1963888042020053487L;

    public static final Integer COURSE_TYPE = 2;
    public static final Integer SUBJECT_TYPE = 1;

    public ResourceVisit() {
    }

    public ResourceVisit(String ContentId, String name, Integer visit, String organizationId, String organizationName) {
        this.setContentId(ContentId);
        this.setContentName(name);
        this.setVisit(visit);
        this.setOrganizationId(organizationId);
        this.setOrganizationName(organizationName);
    }

    public ResourceVisit(Integer type,String ContentId, Integer visit) {
        this.setContentType(type);
        this.setContentId(ContentId);
        this.setVisit(visit);
    }

    private String organizationId;
    private String organizationName;

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
}
