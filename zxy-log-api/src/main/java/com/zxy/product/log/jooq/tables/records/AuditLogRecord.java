/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.AuditLog;
import com.zxy.product.log.jooq.tables.interfaces.IAuditLog;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AuditLogRecord extends UpdatableRecordImpl<AuditLogRecord> implements Record14<String, Long, String, String, String, String, Integer, String, String, String, String, String, String, Long>, IAuditLog {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_audit_log.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_organization_id</code>. 所属组织
     */
    @Override
    public void setOrganizationId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_organization_id</code>. 所属组织
     */
    @Override
    public String getOrganizationId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_desc</code>. 描述
     */
    @Override
    public void setDesc(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_desc</code>. 描述
     */
    @Override
    public String getDesc() {
        return (String) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_module</code>. 模块
     */
    @Override
    public void setModule(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_module</code>. 模块
     */
    @Override
    public String getModule() {
        return (String) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_sub_module</code>. 子模块
     */
    @Override
    public void setSubModule(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_sub_module</code>. 子模块
     */
    @Override
    public String getSubModule() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_action</code>. 操作类型
     */
    @Override
    public void setAction(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_action</code>. 操作类型
     */
    @Override
    public Integer getAction() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_first_action</code>. 一级操作
     */
    @Override
    public void setFirstAction(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_first_action</code>. 一级操作
     */
    @Override
    public String getFirstAction() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_second_action</code>. 二级操作
     */
    @Override
    public void setSecondAction(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_second_action</code>. 二级操作
     */
    @Override
    public String getSecondAction() {
        return (String) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_member_full_name</code>. 操作人
     */
    @Override
    public void setMemberFullName(String value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_member_full_name</code>. 操作人
     */
    @Override
    public String getMemberFullName() {
        return (String) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_browser</code>. 浏览器
     */
    @Override
    public void setBrowser(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_browser</code>. 浏览器
     */
    @Override
    public String getBrowser() {
        return (String) get(10);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_user_agent</code>. 浏览器userAgent
     */
    @Override
    public void setUserAgent(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_user_agent</code>. 浏览器userAgent
     */
    @Override
    public String getUserAgent() {
        return (String) get(11);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_ip</code>. ip
     */
    @Override
    public void setIp(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_ip</code>. ip
     */
    @Override
    public String getIp() {
        return (String) get(12);
    }

    /**
     * Setter for <code>zxy-log.t_audit_log.f_log_time</code>. 日志时间
     */
    @Override
    public void setLogTime(Long value) {
        set(13, value);
    }

    /**
     * Getter for <code>zxy-log.t_audit_log.f_log_time</code>. 日志时间
     */
    @Override
    public Long getLogTime() {
        return (Long) get(13);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record14 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row14<String, Long, String, String, String, String, Integer, String, String, String, String, String, String, Long> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row14<String, Long, String, String, String, String, Integer, String, String, String, String, String, String, Long> valuesRow() {
        return (Row14) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return AuditLog.AUDIT_LOG.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return AuditLog.AUDIT_LOG.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return AuditLog.AUDIT_LOG.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return AuditLog.AUDIT_LOG.DESC;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return AuditLog.AUDIT_LOG.MODULE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return AuditLog.AUDIT_LOG.SUB_MODULE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return AuditLog.AUDIT_LOG.ACTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return AuditLog.AUDIT_LOG.FIRST_ACTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return AuditLog.AUDIT_LOG.SECOND_ACTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field10() {
        return AuditLog.AUDIT_LOG.MEMBER_FULL_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return AuditLog.AUDIT_LOG.BROWSER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return AuditLog.AUDIT_LOG.USER_AGENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field13() {
        return AuditLog.AUDIT_LOG.IP;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field14() {
        return AuditLog.AUDIT_LOG.LOG_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getDesc();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getModule();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getSubModule();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getAction();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getFirstAction();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getSecondAction();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value10() {
        return getMemberFullName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getBrowser();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getUserAgent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value13() {
        return getIp();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value14() {
        return getLogTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value3(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value4(String value) {
        setDesc(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value5(String value) {
        setModule(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value6(String value) {
        setSubModule(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value7(Integer value) {
        setAction(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value8(String value) {
        setFirstAction(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value9(String value) {
        setSecondAction(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value10(String value) {
        setMemberFullName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value11(String value) {
        setBrowser(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value12(String value) {
        setUserAgent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value13(String value) {
        setIp(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord value14(Long value) {
        setLogTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLogRecord values(String value1, Long value2, String value3, String value4, String value5, String value6, Integer value7, String value8, String value9, String value10, String value11, String value12, String value13, Long value14) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAuditLog from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setOrganizationId(from.getOrganizationId());
        setDesc(from.getDesc());
        setModule(from.getModule());
        setSubModule(from.getSubModule());
        setAction(from.getAction());
        setFirstAction(from.getFirstAction());
        setSecondAction(from.getSecondAction());
        setMemberFullName(from.getMemberFullName());
        setBrowser(from.getBrowser());
        setUserAgent(from.getUserAgent());
        setIp(from.getIp());
        setLogTime(from.getLogTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAuditLog> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AuditLogRecord
     */
    public AuditLogRecord() {
        super(AuditLog.AUDIT_LOG);
    }

    /**
     * Create a detached, initialised AuditLogRecord
     */
    public AuditLogRecord(String id, Long createTime, String organizationId, String desc, String module, String subModule, Integer action, String firstAction, String secondAction, String memberFullName, String browser, String userAgent, String ip, Long logTime) {
        super(AuditLog.AUDIT_LOG);

        set(0, id);
        set(1, createTime);
        set(2, organizationId);
        set(3, desc);
        set(4, module);
        set(5, subModule);
        set(6, action);
        set(7, firstAction);
        set(8, secondAction);
        set(9, memberFullName);
        set(10, browser);
        set(11, userAgent);
        set(12, ip);
        set(13, logTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.AuditLogEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.AuditLogEntity pojo = (com.zxy.product.log.jooq.tables.pojos.AuditLogEntity)source;
        pojo.into(this);
        return true;
    }
}
