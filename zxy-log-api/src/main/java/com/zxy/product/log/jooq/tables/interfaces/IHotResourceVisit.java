/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 热门学习资源表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IHotResourceVisit extends Serializable {

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_business_id</code>. 资源ID
     */
    public void setBusinessId(String value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_business_id</code>. 资源ID
     */
    public String getBusinessId();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_business_name</code>. 资源名称
     */
    public void setBusinessName(String value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_business_name</code>. 资源名称
     */
    public String getBusinessName();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_business_type</code>. 资源类型:1 课程,2 专题,3 直播
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_business_type</code>. 资源类型:1 课程,2 专题,3 直播
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_organization_id</code>. 资源所属组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_organization_id</code>. 资源所属组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_organization_name</code>. 资源所属组织名称
     */
    public void setOrganizationName(String value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_organization_name</code>. 资源所属组织名称
     */
    public String getOrganizationName();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_day30_visit</code>. 资源近30天访问次数
     */
    public void setDay30Visit(Integer value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_day30_visit</code>. 资源近30天访问次数
     */
    public Integer getDay30Visit();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_status</code>. 状态 显示0，隐藏1
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_status</code>. 状态 显示0，隐藏1
     */
    public Integer getStatus();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_hot_resource_visit.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>zxy-log.t_hot_resource_visit.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IHotResourceVisit
     */
    public void from(IHotResourceVisit from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IHotResourceVisit
     */
    public <E extends IHotResourceVisit> E into(E into);
}
