/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 白名单登陆异常记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IWhiteRecord extends Serializable {

    /**
     * Setter for <code>zxy-log.t_white_record.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_white_record.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_white_record.f_member_id</code>. 用户id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_member_id</code>. 用户id
     */
    public String getMemberId();

    /**
     * Setter for <code>zxy-log.t_white_record.f_login_name</code>. 登陆账号
     */
    public void setLoginName(String value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_login_name</code>. 登陆账号
     */
    public String getLoginName();

    /**
     * Setter for <code>zxy-log.t_white_record.f_type</code>. 登陆终端：1pc 2app
     */
    public void setType(Integer value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_type</code>. 登陆终端：1pc 2app
     */
    public Integer getType();

    /**
     * Setter for <code>zxy-log.t_white_record.f_ip</code>. 登陆ip地址
     */
    public void setIp(String value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_ip</code>. 登陆ip地址
     */
    public String getIp();

    /**
     * Setter for <code>zxy-log.t_white_record.f_model</code>. 手机型号
     */
    public void setModel(String value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_model</code>. 手机型号
     */
    public String getModel();

    /**
     * Setter for <code>zxy-log.t_white_record.f_error</code>. 错误原因
     */
    public void setError(String value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_error</code>. 错误原因
     */
    public String getError();

    /**
     * Setter for <code>zxy-log.t_white_record.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_white_record.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    public void setPasswordType(Integer value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    public Integer getPasswordType();

    /**
     * Setter for <code>zxy-log.t_white_record.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>zxy-log.t_white_record.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    public Integer getSource();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IWhiteRecord
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.IWhiteRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IWhiteRecord
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.IWhiteRecord> E into(E into);
}
