/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.WhiteRecord;
import com.zxy.product.log.jooq.tables.interfaces.IWhiteRecord;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 白名单登陆异常记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class WhiteRecordRecord extends UpdatableRecordImpl<WhiteRecordRecord> implements Record11<String, Long, String, String, Integer, String, String, String, String, Integer, Integer>, IWhiteRecord {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_white_record.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_member_id</code>. 用户id
     */
    @Override
    public void setMemberId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_member_id</code>. 用户id
     */
    @Override
    public String getMemberId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_login_name</code>. 登陆账号
     */
    @Override
    public void setLoginName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_login_name</code>. 登陆账号
     */
    @Override
    public String getLoginName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_type</code>. 登陆终端：1pc 2app
     */
    @Override
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_type</code>. 登陆终端：1pc 2app
     */
    @Override
    public Integer getType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_ip</code>. 登陆ip地址
     */
    @Override
    public void setIp(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_ip</code>. 登陆ip地址
     */
    @Override
    public String getIp() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_model</code>. 手机型号
     */
    @Override
    public void setModel(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_model</code>. 手机型号
     */
    @Override
    public String getModel() {
        return (String) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_error</code>. 错误原因
     */
    @Override
    public void setError(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_error</code>. 错误原因
     */
    @Override
    public String getError() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_organization_id</code>. 组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_organization_id</code>. 组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    @Override
    public void setPasswordType(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    @Override
    public Integer getPasswordType() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_white_record.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    @Override
    public void setSource(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_white_record.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    @Override
    public Integer getSource() {
        return (Integer) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row11<String, Long, String, String, Integer, String, String, String, String, Integer, Integer> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row11<String, Long, String, String, Integer, String, String, String, String, Integer, Integer> valuesRow() {
        return (Row11) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return WhiteRecord.WHITE_RECORD.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return WhiteRecord.WHITE_RECORD.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return WhiteRecord.WHITE_RECORD.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return WhiteRecord.WHITE_RECORD.LOGIN_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return WhiteRecord.WHITE_RECORD.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return WhiteRecord.WHITE_RECORD.IP;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return WhiteRecord.WHITE_RECORD.MODEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return WhiteRecord.WHITE_RECORD.ERROR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return WhiteRecord.WHITE_RECORD.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return WhiteRecord.WHITE_RECORD.PASSWORD_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return WhiteRecord.WHITE_RECORD.SOURCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getLoginName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getIp();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getModel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getError();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getPasswordType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getSource();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value3(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value4(String value) {
        setLoginName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value5(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value6(String value) {
        setIp(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value7(String value) {
        setModel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value8(String value) {
        setError(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value9(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value10(Integer value) {
        setPasswordType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord value11(Integer value) {
        setSource(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public WhiteRecordRecord values(String value1, Long value2, String value3, String value4, Integer value5, String value6, String value7, String value8, String value9, Integer value10, Integer value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IWhiteRecord from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setMemberId(from.getMemberId());
        setLoginName(from.getLoginName());
        setType(from.getType());
        setIp(from.getIp());
        setModel(from.getModel());
        setError(from.getError());
        setOrganizationId(from.getOrganizationId());
        setPasswordType(from.getPasswordType());
        setSource(from.getSource());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IWhiteRecord> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached WhiteRecordRecord
     */
    public WhiteRecordRecord() {
        super(WhiteRecord.WHITE_RECORD);
    }

    /**
     * Create a detached, initialised WhiteRecordRecord
     */
    public WhiteRecordRecord(String id, Long createTime, String memberId, String loginName, Integer type, String ip, String model, String error, String organizationId, Integer passwordType, Integer source) {
        super(WhiteRecord.WHITE_RECORD);

        set(0, id);
        set(1, createTime);
        set(2, memberId);
        set(3, loginName);
        set(4, type);
        set(5, ip);
        set(6, model);
        set(7, error);
        set(8, organizationId);
        set(9, passwordType);
        set(10, source);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.WhiteRecordEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.WhiteRecordEntity pojo = (com.zxy.product.log.jooq.tables.pojos.WhiteRecordEntity)source;
        pojo.into(this);
        return true;
    }
}
