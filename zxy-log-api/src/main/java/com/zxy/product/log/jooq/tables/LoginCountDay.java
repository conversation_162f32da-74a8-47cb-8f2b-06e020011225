/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.LoginCountDayRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 用户登录每日统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LoginCountDay extends TableImpl<LoginCountDayRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_login_count_day</code>
     */
    public static final LoginCountDay LOGIN_COUNT_DAY = new LoginCountDay();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<LoginCountDayRecord> getRecordType() {
        return LoginCountDayRecord.class;
    }

    /**
     * The column <code>zxy-log.t_login_count_day.f_id</code>. 主键
     */
    public final TableField<LoginCountDayRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>zxy-log.t_login_count_day.f_day</code>. 日
     */
    public final TableField<LoginCountDayRecord, Integer> DAY = createField("f_day", org.jooq.impl.SQLDataType.INTEGER, this, "日");

    /**
     * The column <code>zxy-log.t_login_count_day.f_month</code>. 月
     */
    public final TableField<LoginCountDayRecord, Integer> MONTH = createField("f_month", org.jooq.impl.SQLDataType.INTEGER, this, "月");

    /**
     * The column <code>zxy-log.t_login_count_day.f_year</code>. 年
     */
    public final TableField<LoginCountDayRecord, Integer> YEAR = createField("f_year", org.jooq.impl.SQLDataType.INTEGER, this, "年");

    /**
     * The column <code>zxy-log.t_login_count_day.f_member_num</code>. 登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> MEMBER_NUM = createField("f_member_num", org.jooq.impl.SQLDataType.INTEGER, this, "登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_member_time</code>. 登录人次
     */
    public final TableField<LoginCountDayRecord, Integer> MEMBER_TIME = createField("f_member_time", org.jooq.impl.SQLDataType.INTEGER, this, "登录人次");

    /**
     * The column <code>zxy-log.t_login_count_day.f_system_num</code>. 系统总人数
     */
    public final TableField<LoginCountDayRecord, Integer> SYSTEM_NUM = createField("f_system_num", org.jooq.impl.SQLDataType.INTEGER, this, "系统总人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_male_num</code>. 男登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> MALE_NUM = createField("f_male_num", org.jooq.impl.SQLDataType.INTEGER, this, "男登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_female_num</code>. 女登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> FEMALE_NUM = createField("f_female_num", org.jooq.impl.SQLDataType.INTEGER, this, "女登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_entry_year_zero_two</code>. 入职年限0~2年登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> ENTRY_YEAR_ZERO_TWO = createField("f_entry_year_zero_two", org.jooq.impl.SQLDataType.INTEGER, this, "入职年限0~2年登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_entry_year_three_five</code>. 入职年限3~5年登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> ENTRY_YEAR_THREE_FIVE = createField("f_entry_year_three_five", org.jooq.impl.SQLDataType.INTEGER, this, "入职年限3~5年登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_entry_year_six_ten</code>. 入职年限6~10年登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> ENTRY_YEAR_SIX_TEN = createField("f_entry_year_six_ten", org.jooq.impl.SQLDataType.INTEGER, this, "入职年限6~10年登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_entry_year_ten</code>. 入职年限10年以上登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> ENTRY_YEAR_TEN = createField("f_entry_year_ten", org.jooq.impl.SQLDataType.INTEGER, this, "入职年限10年以上登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_political_outlook</code>. 政治面貌(中共党员（含预备党员）)登录人数
     */
    public final TableField<LoginCountDayRecord, Integer> POLITICAL_OUTLOOK = createField("f_political_outlook", org.jooq.impl.SQLDataType.INTEGER, this, "政治面貌(中共党员（含预备党员）)登录人数");

    /**
     * The column <code>zxy-log.t_login_count_day.f_create_time</code>. 创建时间
     */
    public final TableField<LoginCountDayRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>zxy-log.t_login_count_day.f_modify_date</code>. 修改时间
     */
    public final TableField<LoginCountDayRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("CURRENT_TIMESTAMP", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>zxy-log.t_login_count_day</code> table reference
     */
    public LoginCountDay() {
        this("t_login_count_day", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_login_count_day</code> table reference
     */
    public LoginCountDay(String alias) {
        this(alias, LOGIN_COUNT_DAY);
    }

    private LoginCountDay(String alias, Table<LoginCountDayRecord> aliased) {
        this(alias, aliased, null);
    }

    private LoginCountDay(String alias, Table<LoginCountDayRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "用户登录每日统计表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<LoginCountDayRecord> getPrimaryKey() {
        return Keys.KEY_T_LOGIN_COUNT_DAY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<LoginCountDayRecord>> getKeys() {
        return Arrays.<UniqueKey<LoginCountDayRecord>>asList(Keys.KEY_T_LOGIN_COUNT_DAY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public LoginCountDay as(String alias) {
        return new LoginCountDay(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public LoginCountDay rename(String name) {
        return new LoginCountDay(name, null);
    }
}
