/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.MemberDetailRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberDetail extends TableImpl<MemberDetailRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_member_detail</code>
     */
    public static final MemberDetail MEMBER_DETAIL = new MemberDetail();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MemberDetailRecord> getRecordType() {
        return MemberDetailRecord.class;
    }

    /**
     * The column <code>zxy-log.t_member_detail.f_id</code>. ID
     */
    public final TableField<MemberDetailRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(45).nullable(false), this, "ID");

    /**
     * The column <code>zxy-log.t_member_detail.f_create_time</code>. 创建时间
     */
    public final TableField<MemberDetailRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>zxy-log.t_member_detail.f_member_id</code>. 人员ID
     */
    public final TableField<MemberDetailRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "人员ID");

    /**
     * The column <code>zxy-log.t_member_detail.f_incumbency_status</code>. 在职状态
     */
    public final TableField<MemberDetailRecord, String> INCUMBENCY_STATUS = createField("f_incumbency_status", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "在职状态");

    /**
     * The column <code>zxy-log.t_member_detail.f_entry_date</code>. 入职时间
     */
    public final TableField<MemberDetailRecord, Long> ENTRY_DATE = createField("f_entry_date", org.jooq.impl.SQLDataType.BIGINT, this, "入职时间");

    /**
     * The column <code>zxy-log.t_member_detail.f_head_portrait</code>. 头像图片ID
     */
    public final TableField<MemberDetailRecord, String> HEAD_PORTRAIT = createField("f_head_portrait", org.jooq.impl.SQLDataType.VARCHAR.length(45), this, "头像图片ID");

    /**
     * The column <code>zxy-log.t_member_detail.f_head_portrait_path</code>. 头像图片路径
     */
    public final TableField<MemberDetailRecord, String> HEAD_PORTRAIT_PATH = createField("f_head_portrait_path", org.jooq.impl.SQLDataType.VARCHAR.length(200), this, "头像图片路径");

    /**
     * The column <code>zxy-log.t_member_detail.f_customer_type</code>. 客户类型
     */
    public final TableField<MemberDetailRecord, String> CUSTOMER_TYPE = createField("f_customer_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "客户类型");

    /**
     * The column <code>zxy-log.t_member_detail.f_expiry_date</code>. 有效期
     */
    public final TableField<MemberDetailRecord, Long> EXPIRY_DATE = createField("f_expiry_date", org.jooq.impl.SQLDataType.BIGINT, this, "有效期");

    /**
     * The column <code>zxy-log.t_member_detail.f_nationality_id</code>. 国籍id
     */
    public final TableField<MemberDetailRecord, String> NATIONALITY_ID = createField("f_nationality_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "国籍id");

    /**
     * The column <code>zxy-log.t_member_detail.f_ethnicity_id</code>. 民族id
     */
    public final TableField<MemberDetailRecord, String> ETHNICITY_ID = createField("f_ethnicity_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "民族id");

    /**
     * The column <code>zxy-log.t_member_detail.f_politicalization_id</code>. 政治面貌id
     */
    public final TableField<MemberDetailRecord, String> POLITICALIZATION_ID = createField("f_politicalization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "政治面貌id");

    /**
     * The column <code>zxy-log.t_member_detail.f_education_id</code>. 学历id
     */
    public final TableField<MemberDetailRecord, String> EDUCATION_ID = createField("f_education_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "学历id");

    /**
     * The column <code>zxy-log.t_member_detail.f_credential_type</code>. 证件类型id
     */
    public final TableField<MemberDetailRecord, String> CREDENTIAL_TYPE = createField("f_credential_type", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "证件类型id");

    /**
     * The column <code>zxy-log.t_member_detail.f_credential_value</code>. 证件值
     */
    public final TableField<MemberDetailRecord, String> CREDENTIAL_VALUE = createField("f_credential_value", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "证件值");

    /**
     * The column <code>zxy-log.t_member_detail.f_is_leader</code>. 是否领导班子成员(1=是;0=否)
     */
    public final TableField<MemberDetailRecord, Integer> IS_LEADER = createField("f_is_leader", org.jooq.impl.SQLDataType.INTEGER, this, "是否领导班子成员(1=是;0=否)");

    /**
     * The column <code>zxy-log.t_member_detail.f_join_date</code>. 参加本单位时间
     */
    public final TableField<MemberDetailRecord, Long> JOIN_DATE = createField("f_join_date", org.jooq.impl.SQLDataType.BIGINT, this, "参加本单位时间");

    /**
     * The column <code>zxy-log.t_member_detail.f_born_date</code>. 出生日期
     */
    public final TableField<MemberDetailRecord, Long> BORN_DATE = createField("f_born_date", org.jooq.impl.SQLDataType.BIGINT, this, "出生日期");

    /**
     * The column <code>zxy-log.t_member_detail.f_school</code>. 学院
     */
    public final TableField<MemberDetailRecord, String> SCHOOL = createField("f_school", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "学院");

    /**
     * The column <code>zxy-log.t_member_detail.f_graduate_date</code>. 毕业时间
     */
    public final TableField<MemberDetailRecord, Long> GRADUATE_DATE = createField("f_graduate_date", org.jooq.impl.SQLDataType.BIGINT, this, "毕业时间");

    /**
     * The column <code>zxy-log.t_member_detail.f_summary</code>. 个人简介
     */
    public final TableField<MemberDetailRecord, String> SUMMARY = createField("f_summary", org.jooq.impl.SQLDataType.VARCHAR.length(500), this, "个人简介");

    /**
     * Create a <code>zxy-log.t_member_detail</code> table reference
     */
    public MemberDetail() {
        this("t_member_detail", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_member_detail</code> table reference
     */
    public MemberDetail(String alias) {
        this(alias, MEMBER_DETAIL);
    }

    private MemberDetail(String alias, Table<MemberDetailRecord> aliased) {
        this(alias, aliased, null);
    }

    private MemberDetail(String alias, Table<MemberDetailRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<MemberDetailRecord> getPrimaryKey() {
        return Keys.KEY_T_MEMBER_DETAIL_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<MemberDetailRecord>> getKeys() {
        return Arrays.<UniqueKey<MemberDetailRecord>>asList(Keys.KEY_T_MEMBER_DETAIL_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberDetail as(String alias) {
        return new MemberDetail(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public MemberDetail rename(String name) {
        return new MemberDetail(name, null);
    }
}
