/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq;


import com.zxy.product.log.jooq.tables.*;
import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;

import javax.annotation.Generated;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ZxyLog extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log</code>
     */
    public static final ZxyLog ZXY_LOG_SCHEMA = new ZxyLog();

    /**
     * The table <code>zxy-log.t_audit_log</code>.
     */
    public final AuditLog AUDIT_LOG = com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG;

    /**
     * 登录日至表
     */
    public final LoginLog LOGIN_LOG = com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG;

    /**
     * 在线人数统计表
     */
    public final OnlineStatistics ONLINE_STATISTICS = com.zxy.product.log.jooq.tables.OnlineStatistics.ONLINE_STATISTICS;

    /**
     * 白名单登陆异常记录
     */
    public final WhiteRecord WHITE_RECORD = com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD;

    /**
     * The table <code>zxy-log.t_user_behavior</code>.
     */
    public final UserBehavior USER_BEHAVIOR = com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR;

    /**
     * 人员表
     */
    public final Member MEMBER = com.zxy.product.log.jooq.tables.Member.MEMBER;

    /**
     * 资源浏览统计表
     */
    public final ResourceVisit RESOURCE_VISIT = com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT;

    /**
     * 用户活跃表
     */
    public final UserActive USER_ACTIVE = com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE;

    /**
     * 用户登录每日统计表
     */
    public final LoginCountDay LOGIN_COUNT_DAY = com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY;

    /**
     * 各组织日登陆统计表
     */
    public final OrganizationLoginCountDay ORGANIZATION_LOGIN_COUNT_DAY = com.zxy.product.log.jooq.tables.OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY;

    public final MemberDetail MEMBER_DETAIL = com.zxy.product.log.jooq.tables.MemberDetail.MEMBER_DETAIL;

    public final Organization ORGANIZATION = com.zxy.product.log.jooq.tables.Organization.ORGANIZATION;

    public final OrganizationDetail ORGANIZATION_DETAIL = com.zxy.product.log.jooq.tables.OrganizationDetail.ORGANIZATION_DETAIL;

    /**
     * 热门学习资源表
     */
    public final HotResourceVisit HOT_RESOURCE_VISIT = com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT;

    /**
     * otter同步课程表
     */
    public final CourseInfo COURSE_INFO = com.zxy.product.log.jooq.tables.CourseInfo.COURSE_INFO;

    /**
     * No further instances allowed
     */
    private ZxyLog() {
        super("zxy-log", null);
    }


    /**
     * {@inheritDoc}
     */
    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        List result = new ArrayList();
        result.addAll(getTables0());
        return result;
    }

    private final List<Table<?>> getTables0() {
        return Arrays.<Table<?>>asList(
            AuditLog.AUDIT_LOG,
            LoginLog.LOGIN_LOG,
            OnlineStatistics.ONLINE_STATISTICS,
            WhiteRecord.WHITE_RECORD,
            UserBehavior.USER_BEHAVIOR,
            Member.MEMBER,
            ResourceVisit.RESOURCE_VISIT,
            UserActive.USER_ACTIVE,
            OrganizationLoginCountDay.ORGANIZATION_LOGIN_COUNT_DAY,
            MemberDetail.MEMBER_DETAIL,
            Organization.ORGANIZATION,
            OrganizationDetail.ORGANIZATION_DETAIL,
            LoginCountDay.LOGIN_COUNT_DAY,
            HotResourceVisit.HOT_RESOURCE_VISIT,
            CourseInfo.COURSE_INFO);
    }
}
