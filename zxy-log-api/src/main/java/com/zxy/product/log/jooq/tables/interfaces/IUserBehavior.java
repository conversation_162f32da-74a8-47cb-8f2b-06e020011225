/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IUserBehavior extends Serializable {

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_id</code>. id
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_id</code>. id
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_user_id</code>. 用户id
     */
    public void setUserId(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_user_id</code>. 用户id
     */
    public String getUserId();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_content_id</code>. 内容id
     */
    public void setContentId(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_content_id</code>. 内容id
     */
    public String getContentId();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_content_type</code>. 内容类型
     */
    public void setContentType(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_content_type</code>. 内容类型
     */
    public String getContentType();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_content_name</code>. 内容名称
     */
    public void setContentName(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_content_name</code>. 内容名称
     */
    public String getContentName();

    /**
     * Setter for <code>zxy-log.t_user_behavior.client_type</code>. 客户端类型
     */
    public void setClientType(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.client_type</code>. 客户端类型
     */
    public String getClientType();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_type</code>. 1:收藏；2：评论；3：评分；4：不喜欢/踩（暂时没有数据）; 5：赞；6：下载；7：分享
     */
    public void setType(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_type</code>. 1:收藏；2：评论；3：评分；4：不喜欢/踩（暂时没有数据）; 5：赞；6：下载；7：分享
     */
    public String getType();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_value</code>. 具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
     */
    public void setValue(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_value</code>. 具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
     */
    public String getValue();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_page_source</code>. 页面来源
     */
    public void setPageSource(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_page_source</code>. 页面来源
     */
    public String getPageSource();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_status</code>. 状态
     */
    public void setStatus(String value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_status</code>. 状态
     */
    public String getStatus();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_user_behavior.f_modifi_time</code>. 修改时间
     */
    public void setModifiTime(Long value);

    /**
     * Getter for <code>zxy-log.t_user_behavior.f_modifi_time</code>. 修改时间
     */
    public Long getModifiTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IUserBehavior
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.IUserBehavior from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IUserBehavior
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.IUserBehavior> E into(E into);
}
