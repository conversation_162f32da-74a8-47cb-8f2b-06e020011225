/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IWhiteRecord;

import javax.annotation.Generated;


/**
 * 白名单登陆异常记录
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class WhiteRecordEntity extends BaseEntity implements IWhiteRecord {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  loginName;
    private Integer type;
    private String  ip;
    private String  model;
    private String  error;
    private String  organizationId;
    private Integer passwordType;
    private Integer source;

    public WhiteRecordEntity() {}

    public WhiteRecordEntity(WhiteRecordEntity value) {
        this.memberId = value.memberId;
        this.loginName = value.loginName;
        this.type = value.type;
        this.ip = value.ip;
        this.model = value.model;
        this.error = value.error;
        this.organizationId = value.organizationId;
        this.passwordType = value.passwordType;
        this.source = value.source;
    }

    public WhiteRecordEntity(
        String  id,
        Long    createTime,
        String  memberId,
        String  loginName,
        Integer type,
        String  ip,
        String  model,
        String  error,
        String  organizationId,
        Integer passwordType,
        Integer source
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.memberId = memberId;
        this.loginName = loginName;
        this.type = type;
        this.ip = ip;
        this.model = model;
        this.error = error;
        this.organizationId = organizationId;
        this.passwordType = passwordType;
        this.source = source;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getLoginName() {
        return this.loginName;
    }

    @Override
    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String getIp() {
        return this.ip;
    }

    @Override
    public void setIp(String ip) {
        this.ip = ip;
    }

    @Override
    public String getModel() {
        return this.model;
    }

    @Override
    public void setModel(String model) {
        this.model = model;
    }

    @Override
    public String getError() {
        return this.error;
    }

    @Override
    public void setError(String error) {
        this.error = error;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Integer getPasswordType() {
        return this.passwordType;
    }

    @Override
    public void setPasswordType(Integer passwordType) {
        this.passwordType = passwordType;
    }

    @Override
    public Integer getSource() {
        return this.source;
    }

    @Override
    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("WhiteRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(memberId);
        sb.append(", ").append(loginName);
        sb.append(", ").append(type);
        sb.append(", ").append(ip);
        sb.append(", ").append(model);
        sb.append(", ").append(error);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(passwordType);
        sb.append(", ").append(source);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IWhiteRecord from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setMemberId(from.getMemberId());
        setLoginName(from.getLoginName());
        setType(from.getType());
        setIp(from.getIp());
        setModel(from.getModel());
        setError(from.getError());
        setOrganizationId(from.getOrganizationId());
        setPasswordType(from.getPasswordType());
        setSource(from.getSource());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IWhiteRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends WhiteRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.WhiteRecordRecord r = new com.zxy.product.log.jooq.tables.records.WhiteRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ID, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.MEMBER_ID, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.LOGIN_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.LOGIN_NAME, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.LOGIN_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.TYPE, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.IP.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.IP, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.IP));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.MODEL.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.MODEL, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.MODEL));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ERROR.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ERROR, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ERROR));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ORGANIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.PASSWORD_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.PASSWORD_TYPE, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.PASSWORD_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.SOURCE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.SOURCE, record.getValue(com.zxy.product.log.jooq.tables.WhiteRecord.WHITE_RECORD.SOURCE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
