package com.zxy.product.log.entity;

import java.io.Serializable;

/**
 * <AUTHOR> z<PERSON>yong
 * @ClassName : LoginRecord
 * @Description : 封装用户登录数据
 * @date : 2021-10-28 18:45
 */
public class LoginRecord implements Serializable {

    private static final long serialVersionUID = 1846795972519457751L;

    private long    totalVisit;             //登录总人次
    private long    todayVisit;             //今日登录人次
    private long    number;                 //登录总人数
    private String  pcRatio = "0%";         //pc端占比
    private String  appRatio= "0%";         //app端占比,xx%

    public long getTotalVisit() {
        return totalVisit;
    }

    public void setTotalVisit(long totalVisit) {
        this.totalVisit = totalVisit;
    }

    public long getTodayVisit() {
        return todayVisit;
    }

    public void setTodayVisit(long todayVisit) {
        this.todayVisit = todayVisit;
    }

    public long getNumber() {
        return number;
    }

    public void setNumber(long number) {
        this.number = number;
    }

    public String getPcRatio() {
        return pcRatio;
    }

    public void setPcRatio(String pcRatio) {
        this.pcRatio = pcRatio;
    }

    public String getAppRatio() {
        return appRatio;
    }

    public void setAppRatio(String appRatio) {
        this.appRatio = appRatio;
    }

    @Override
    public String toString() {
        return "LoginRecord{" +
            "totalVisit=" + totalVisit +
            ", todayVisit=" + todayVisit +
            ", number=" + number +
            ", pcRatio='" + pcRatio + '\'' +
            ", appRatio='" + appRatio + '\'' +
            '}';
    }
}
