/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 用户活跃表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IUserActive extends Serializable {

    /**
     * Setter for <code>zxy-log.t_user_active.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_user_active.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>zxy-log.t_user_active.f_login_times</code>. 登录次数
     */
    public void setLoginTimes(Integer value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_login_times</code>. 登录次数
     */
    public Integer getLoginTimes();

    /**
     * Setter for <code>zxy-log.t_user_active.f_year_info</code>. 年
     */
    public void setYearInfo(Integer value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_year_info</code>. 年
     */
    public Integer getYearInfo();

    /**
     * Setter for <code>zxy-log.t_user_active.f_month_info</code>. 月
     */
    public void setMonthInfo(Integer value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_month_info</code>. 月
     */
    public Integer getMonthInfo();

    /**
     * Setter for <code>zxy-log.t_user_active.f_day_info</code>. 日
     */
    public void setDayInfo(Integer value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_day_info</code>. 日
     */
    public Integer getDayInfo();

    /**
     * Setter for <code>zxy-log.t_user_active.f_quarter_info</code>. 季度
     */
    public void setQuarterInfo(Integer value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_quarter_info</code>. 季度
     */
    public Integer getQuarterInfo();

    /**
     * Setter for <code>zxy-log.t_user_active.f_week_info</code>. 周
     */
    public void setWeekInfo(Integer value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_week_info</code>. 周
     */
    public Integer getWeekInfo();

    /**
     * Setter for <code>zxy-log.t_user_active.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_user_active.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>zxy-log.t_user_active.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IUserActive
     */
    public void from(IUserActive from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IUserActive
     */
    public <E extends IUserActive> E into(E into);
}
