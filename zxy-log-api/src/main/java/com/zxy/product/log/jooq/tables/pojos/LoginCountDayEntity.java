/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.ILoginCountDay;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 用户登录每日统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LoginCountDayEntity extends BaseEntity implements ILoginCountDay {

    private static final long serialVersionUID = 1L;

    private Integer   day;
    private Integer   month;
    private Integer   year;
    private Integer   memberNum;
    private Integer   memberTime;
    private Integer   systemNum;
    private Integer   maleNum;
    private Integer   femaleNum;
    private Integer   entryYearZeroTwo;
    private Integer   entryYearThreeFive;
    private Integer   entryYearSixTen;
    private Integer   entryYearTen;
    private Integer   politicalOutlook;
    private Timestamp modifyDate;

    public LoginCountDayEntity() {}

    public LoginCountDayEntity(LoginCountDayEntity value) {
        this.day = value.day;
        this.month = value.month;
        this.year = value.year;
        this.memberNum = value.memberNum;
        this.memberTime = value.memberTime;
        this.systemNum = value.systemNum;
        this.maleNum = value.maleNum;
        this.femaleNum = value.femaleNum;
        this.entryYearZeroTwo = value.entryYearZeroTwo;
        this.entryYearThreeFive = value.entryYearThreeFive;
        this.entryYearSixTen = value.entryYearSixTen;
        this.entryYearTen = value.entryYearTen;
        this.politicalOutlook = value.politicalOutlook;
        this.modifyDate = value.modifyDate;
    }

    public LoginCountDayEntity(
        String    id,
        Integer   day,
        Integer   month,
        Integer   year,
        Integer   memberNum,
        Integer   memberTime,
        Integer   systemNum,
        Integer   maleNum,
        Integer   femaleNum,
        Integer   entryYearZeroTwo,
        Integer   entryYearThreeFive,
        Integer   entryYearSixTen,
        Integer   entryYearTen,
        Integer   politicalOutlook,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.day = day;
        this.month = month;
        this.year = year;
        this.memberNum = memberNum;
        this.memberTime = memberTime;
        this.systemNum = systemNum;
        this.maleNum = maleNum;
        this.femaleNum = femaleNum;
        this.entryYearZeroTwo = entryYearZeroTwo;
        this.entryYearThreeFive = entryYearThreeFive;
        this.entryYearSixTen = entryYearSixTen;
        this.entryYearTen = entryYearTen;
        this.politicalOutlook = politicalOutlook;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Integer getDay() {
        return this.day;
    }

    @Override
    public void setDay(Integer day) {
        this.day = day;
    }

    @Override
    public Integer getMonth() {
        return this.month;
    }

    @Override
    public void setMonth(Integer month) {
        this.month = month;
    }

    @Override
    public Integer getYear() {
        return this.year;
    }

    @Override
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public Integer getMemberNum() {
        return this.memberNum;
    }

    @Override
    public void setMemberNum(Integer memberNum) {
        this.memberNum = memberNum;
    }

    @Override
    public Integer getMemberTime() {
        return this.memberTime;
    }

    @Override
    public void setMemberTime(Integer memberTime) {
        this.memberTime = memberTime;
    }

    @Override
    public Integer getSystemNum() {
        return this.systemNum;
    }

    @Override
    public void setSystemNum(Integer systemNum) {
        this.systemNum = systemNum;
    }

    @Override
    public Integer getMaleNum() {
        return this.maleNum;
    }

    @Override
    public void setMaleNum(Integer maleNum) {
        this.maleNum = maleNum;
    }

    @Override
    public Integer getFemaleNum() {
        return this.femaleNum;
    }

    @Override
    public void setFemaleNum(Integer femaleNum) {
        this.femaleNum = femaleNum;
    }

    @Override
    public Integer getEntryYearZeroTwo() {
        return this.entryYearZeroTwo;
    }

    @Override
    public void setEntryYearZeroTwo(Integer entryYearZeroTwo) {
        this.entryYearZeroTwo = entryYearZeroTwo;
    }

    @Override
    public Integer getEntryYearThreeFive() {
        return this.entryYearThreeFive;
    }

    @Override
    public void setEntryYearThreeFive(Integer entryYearThreeFive) {
        this.entryYearThreeFive = entryYearThreeFive;
    }

    @Override
    public Integer getEntryYearSixTen() {
        return this.entryYearSixTen;
    }

    @Override
    public void setEntryYearSixTen(Integer entryYearSixTen) {
        this.entryYearSixTen = entryYearSixTen;
    }

    @Override
    public Integer getEntryYearTen() {
        return this.entryYearTen;
    }

    @Override
    public void setEntryYearTen(Integer entryYearTen) {
        this.entryYearTen = entryYearTen;
    }

    @Override
    public Integer getPoliticalOutlook() {
        return this.politicalOutlook;
    }

    @Override
    public void setPoliticalOutlook(Integer politicalOutlook) {
        this.politicalOutlook = politicalOutlook;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LoginCountDayEntity (");

        sb.append(getId());
        sb.append(", ").append(day);
        sb.append(", ").append(month);
        sb.append(", ").append(year);
        sb.append(", ").append(memberNum);
        sb.append(", ").append(memberTime);
        sb.append(", ").append(systemNum);
        sb.append(", ").append(maleNum);
        sb.append(", ").append(femaleNum);
        sb.append(", ").append(entryYearZeroTwo);
        sb.append(", ").append(entryYearThreeFive);
        sb.append(", ").append(entryYearSixTen);
        sb.append(", ").append(entryYearTen);
        sb.append(", ").append(politicalOutlook);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILoginCountDay from) {
        setId(from.getId());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setYear(from.getYear());
        setMemberNum(from.getMemberNum());
        setMemberTime(from.getMemberTime());
        setSystemNum(from.getSystemNum());
        setMaleNum(from.getMaleNum());
        setFemaleNum(from.getFemaleNum());
        setEntryYearZeroTwo(from.getEntryYearZeroTwo());
        setEntryYearThreeFive(from.getEntryYearThreeFive());
        setEntryYearSixTen(from.getEntryYearSixTen());
        setEntryYearTen(from.getEntryYearTen());
        setPoliticalOutlook(from.getPoliticalOutlook());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILoginCountDay> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends LoginCountDayEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.LoginCountDayRecord r = new com.zxy.product.log.jooq.tables.records.LoginCountDayRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ID, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.DAY) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.DAY, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.DAY));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MONTH) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MONTH, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MONTH));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.YEAR) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.YEAR, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.YEAR));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MEMBER_NUM) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MEMBER_NUM, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MEMBER_NUM));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MEMBER_TIME) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MEMBER_TIME, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MEMBER_TIME));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.SYSTEM_NUM) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.SYSTEM_NUM, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.SYSTEM_NUM));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MALE_NUM) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MALE_NUM, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MALE_NUM));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.FEMALE_NUM) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.FEMALE_NUM, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.FEMALE_NUM));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_ZERO_TWO) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_ZERO_TWO, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_ZERO_TWO));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_THREE_FIVE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_THREE_FIVE, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_THREE_FIVE));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_SIX_TEN) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_SIX_TEN, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_SIX_TEN));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_TEN) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_TEN, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.ENTRY_YEAR_TEN));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.POLITICAL_OUTLOOK) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.POLITICAL_OUTLOOK, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.POLITICAL_OUTLOOK));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MODIFY_DATE, record.getValue(com.zxy.product.log.jooq.tables.LoginCountDay.LOGIN_COUNT_DAY.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
