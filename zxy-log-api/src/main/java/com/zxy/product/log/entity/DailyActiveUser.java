package com.zxy.product.log.entity;

import java.io.Serializable;

/**
 * DAU
 * <AUTHOR> zhouyong
 */
public class DailyActiveUser implements Serializable,Comparable<DailyActiveUser> {

    private static final long serialVersionUID = 5211761784948323811L;

    private String  time;           //日期 m-dd
    private long    totalCount;     //每日登录总人次
    private long    pcCount;        //pc登录人次
    private long    appCount;       //app登录人次

    public DailyActiveUser() { }

    public DailyActiveUser(String time) {
        this.time = time;
    }

    public DailyActiveUser(String time, long totalCount, long pcCount, long appCount) {
        this.time = time;
        this.totalCount = totalCount;
        this.pcCount = pcCount;
        this.appCount = appCount;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public long getPcCount() {
        return pcCount;
    }

    public void setPcCount(long pcCount) {
        this.pcCount = pcCount;
    }

    public long getAppCount() {
        return appCount;
    }

    public void setAppCount(long appCount) {
        this.appCount = appCount;
    }

    @Override
    public int compareTo(DailyActiveUser o) {
        return this.time.compareTo(o.time);
    }
}
