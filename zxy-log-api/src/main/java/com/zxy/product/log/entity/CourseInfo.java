package com.zxy.product.log.entity;

import com.zxy.product.log.jooq.tables.pojos.CourseInfoEntity;

/**
 * <AUTHOR> zhouyong
 * @ClassName : CourseInfo
 * @Description : 课程信息
 * @date : 2024-09-23 22:35
 */
public class CourseInfo extends CourseInfoEntity {

    private static final long serialVersionUID = 5633122888987670965L;

    /**
     * 业务类型: 0-课程 2-专题
     */
    public static final int BUSINESS_TYPE_COURSE    = 0;
    public static final int BUSINESS_TYPE_SUBJECT   = 2;

    /**
     * 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    public static final int STATUS_UNPUBLISHED  = 0;
    public static final int STATUS_PUBLISHED    = 1;
    public static final int STATUS_CANCEL       = 2;

    /**
     * 删除标识 0 未删除， 1 已删除
     */
    public static final int DELETE_FLAG_NO      = 0;
    public static final int DELETE_FLAG_YES     = 1;
}
