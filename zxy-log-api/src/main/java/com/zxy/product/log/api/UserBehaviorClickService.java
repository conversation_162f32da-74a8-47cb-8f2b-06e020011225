package com.zxy.product.log.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.log.entity.UserBehavior;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@RemoteService
public interface UserBehaviorClickService {
    /**
     * 保存
     *
     * @param userId      用户id
     * @param contentId   内容id
     * @param contentType 内容类型 1：专题，2：课程，3：知识，4：直播，5：话题，6：文章，7：问题，8：调研，9：考试，10：班级，11：培训班
     * @param contentName 内容名称
     * @param clientType  客户端类型 0：PC；1：APP
     * @param type        类型：9：点击，10：发现点击
     * @param value       具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
     * @param pageSource  页面来源
     * @return
     */
    @Transactional
    UserBehavior insert(Optional<String> userId, Optional<String> contentId, Optional<String> contentType,
                        Optional<String> contentName, Optional<String> clientType, Optional<String> type,
                        Optional<String> value, Optional<String> pageSource, Optional<String> status);

    /**
     * 提前前一天的数据集
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offSet 分页
     * @param pageSize 每页大小
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<UserBehavior> getListByPage(int offSet, int pageSize, Optional<Long> startTime, Optional<Long> endTime);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String get(String sql);

    /**
     * 执行插入操作的sql
     *
     * @param sql
     * @return
     */
    @Transactional
    int insert(String sql);

    @Transactional
    void delete(Optional<Long> startTime, Optional<Long> endTime);
}
