/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 各组织日登陆统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IOrganizationLoginCountDay extends Serializable {

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_day</code>. 日
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_day</code>. 日
     */
    public Integer getDay();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_month</code>. 月
     */
    public void setMonth(Integer value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_month</code>. 月
     */
    public Integer getMonth();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_year</code>. 年
     */
    public void setYear(Integer value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_year</code>. 年
     */
    public Integer getYear();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_member_num</code>. 登录人数
     */
    public void setMemberNum(Integer value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_member_num</code>. 登录人数
     */
    public Integer getMemberNum();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_member_time</code>. 登录人次
     */
    public void setMemberTime(Integer value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_member_time</code>. 登录人次
     */
    public Integer getMemberTime();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_organization_num</code>. 各组织可用总人数
     */
    public void setOrganizationNum(Integer value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_organization_num</code>. 各组织可用总人数
     */
    public Integer getOrganizationNum();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_organization_id</code>. 组织id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_organization_id</code>. 组织id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_organization_login_count_day.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>zxy-log.t_organization_login_count_day.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IOrganizationLoginCountDay
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.IOrganizationLoginCountDay from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IOrganizationLoginCountDay
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.IOrganizationLoginCountDay> E into(E into);
}
