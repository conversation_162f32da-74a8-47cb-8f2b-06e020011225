/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.UserBehaviorRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserBehavior extends TableImpl<UserBehaviorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_user_behavior</code>
     */
    public static final UserBehavior USER_BEHAVIOR = new UserBehavior();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserBehaviorRecord> getRecordType() {
        return UserBehaviorRecord.class;
    }

    /**
     * The column <code>zxy-log.t_user_behavior.f_id</code>. id
     */
    public final TableField<UserBehaviorRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "id");

    /**
     * The column <code>zxy-log.t_user_behavior.f_user_id</code>. 用户id
     */
    public final TableField<UserBehaviorRecord, String> USER_ID = createField("f_user_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户id");

    /**
     * The column <code>zxy-log.t_user_behavior.f_content_id</code>. 内容id
     */
    public final TableField<UserBehaviorRecord, String> CONTENT_ID = createField("f_content_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "内容id");

    /**
     * The column <code>zxy-log.t_user_behavior.f_content_type</code>. 内容类型
     */
    public final TableField<UserBehaviorRecord, String> CONTENT_TYPE = createField("f_content_type", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "内容类型");

    /**
     * The column <code>zxy-log.t_user_behavior.f_content_name</code>. 内容名称
     */
    public final TableField<UserBehaviorRecord, String> CONTENT_NAME = createField("f_content_name", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "内容名称");

    /**
     * The column <code>zxy-log.t_user_behavior.client_type</code>. 客户端类型
     */
    public final TableField<UserBehaviorRecord, String> CLIENT_TYPE = createField("client_type", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "客户端类型");

    /**
     * The column <code>zxy-log.t_user_behavior.f_type</code>. 1:收藏；2：评论；3：评分；4：不喜欢/踩（暂时没有数据）; 5：赞；6：下载；7：分享
     */
    public final TableField<UserBehaviorRecord, String> TYPE = createField("f_type", org.jooq.impl.SQLDataType.VARCHAR.length(10).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "1:收藏；2：评论；3：评分；4：不喜欢/踩（暂时没有数据）; 5：赞；6：下载；7：分享");

    /**
     * The column <code>zxy-log.t_user_behavior.f_value</code>. 具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
     */
    public final TableField<UserBehaviorRecord, String> VALUE = createField("f_value", org.jooq.impl.SQLDataType.VARCHAR.length(255).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值");

    /**
     * The column <code>zxy-log.t_user_behavior.f_page_source</code>. 页面来源
     */
    public final TableField<UserBehaviorRecord, String> PAGE_SOURCE = createField("f_page_source", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "页面来源");

    /**
     * The column <code>zxy-log.t_user_behavior.f_status</code>. 状态
     */
    public final TableField<UserBehaviorRecord, String> STATUS = createField("f_status", org.jooq.impl.SQLDataType.VARCHAR.length(2).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "状态");

    /**
     * The column <code>zxy-log.t_user_behavior.f_create_time</code>. 创建时间
     */
    public final TableField<UserBehaviorRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>zxy-log.t_user_behavior.f_modifi_time</code>. 修改时间
     */
    public final TableField<UserBehaviorRecord, Long> MODIFI_TIME = createField("f_modifi_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "修改时间");

    /**
     * Create a <code>zxy-log.t_user_behavior</code> table reference
     */
    public UserBehavior() {
        this("t_user_behavior", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_user_behavior</code> table reference
     */
    public UserBehavior(String alias) {
        this(alias, USER_BEHAVIOR);
    }

    private UserBehavior(String alias, Table<UserBehaviorRecord> aliased) {
        this(alias, aliased, null);
    }

    private UserBehavior(String alias, Table<UserBehaviorRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<UserBehaviorRecord> getPrimaryKey() {
        return Keys.KEY_T_USER_BEHAVIOR_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<UserBehaviorRecord>> getKeys() {
        return Arrays.<UniqueKey<UserBehaviorRecord>>asList(Keys.KEY_T_USER_BEHAVIOR_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UserBehavior as(String alias) {
        return new UserBehavior(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserBehavior rename(String name) {
        return new UserBehavior(name, null);
    }
}
