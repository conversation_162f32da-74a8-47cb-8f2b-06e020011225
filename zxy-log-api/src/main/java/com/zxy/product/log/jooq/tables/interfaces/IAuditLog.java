/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAuditLog extends Serializable {

    /**
     * Setter for <code>zxy-log.t_audit_log.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_organization_id</code>. 所属组织
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_organization_id</code>. 所属组织
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_desc</code>. 描述
     */
    public void setDesc(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_desc</code>. 描述
     */
    public String getDesc();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_module</code>. 模块
     */
    public void setModule(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_module</code>. 模块
     */
    public String getModule();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_sub_module</code>. 子模块
     */
    public void setSubModule(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_sub_module</code>. 子模块
     */
    public String getSubModule();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_action</code>. 操作类型
     */
    public void setAction(Integer value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_action</code>. 操作类型
     */
    public Integer getAction();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_first_action</code>. 一级操作
     */
    public void setFirstAction(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_first_action</code>. 一级操作
     */
    public String getFirstAction();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_second_action</code>. 二级操作
     */
    public void setSecondAction(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_second_action</code>. 二级操作
     */
    public String getSecondAction();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_member_full_name</code>. 操作人
     */
    public void setMemberFullName(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_member_full_name</code>. 操作人
     */
    public String getMemberFullName();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_browser</code>. 浏览器
     */
    public void setBrowser(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_browser</code>. 浏览器
     */
    public String getBrowser();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_user_agent</code>. 浏览器userAgent
     */
    public void setUserAgent(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_user_agent</code>. 浏览器userAgent
     */
    public String getUserAgent();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_ip</code>. ip
     */
    public void setIp(String value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_ip</code>. ip
     */
    public String getIp();

    /**
     * Setter for <code>zxy-log.t_audit_log.f_log_time</code>. 日志时间
     */
    public void setLogTime(Long value);

    /**
     * Getter for <code>zxy-log.t_audit_log.f_log_time</code>. 日志时间
     */
    public Long getLogTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAuditLog
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.IAuditLog from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAuditLog
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.IAuditLog> E into(E into);
}
