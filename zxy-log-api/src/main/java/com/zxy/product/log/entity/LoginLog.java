package com.zxy.product.log.entity;

import com.zxy.product.log.jooq.tables.pojos.LoginLogEntity;

/**
 * Created by ch<PERSON><PERSON> on 17/8/11.
 */
public class LoginLog extends LoginLogEntity {
    private static final long serialVersionUID = 4049343343709507810L;
    public static final int TERMINAL_TYPE_PC = 1;
    public static final int TERMINAL_TYPE_APP = 2;
    public static final int FEMALE = 1;

    private Integer sex;
    private String politicalizationId;
    private Long entryDate;
    private String path;

    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    public String getPoliticalizationId() {
        return politicalizationId;
    }

    public void setPoliticalizationId(String politicalizationId) {
        this.politicalizationId = politicalizationId;
    }

    public Long getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Long entryDate) {
        this.entryDate = entryDate;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}

