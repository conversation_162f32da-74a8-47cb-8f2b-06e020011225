/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.OnlineStatistics;
import com.zxy.product.log.jooq.tables.interfaces.IOnlineStatistics;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 在线人数统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OnlineStatisticsRecord extends UpdatableRecordImpl<OnlineStatisticsRecord> implements Record9<String, String, Integer, Integer, Inte<PERSON>, Inte<PERSON>, Integer, Integer, Long>, IOnlineStatistics {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_organization_id</code>. 组织id
     */
    @Override
    public void setOrganizationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_organization_id</code>. 组织id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_online_number</code>. 在线人数
     */
    @Override
    public void setOnlineNumber(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_online_number</code>. 在线人数
     */
    @Override
    public Integer getOnlineNumber() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_pc_online_number</code>. pc在线人数
     */
    @Override
    public void setPcOnlineNumber(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_pc_online_number</code>. pc在线人数
     */
    @Override
    public Integer getPcOnlineNumber() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_app_online_number</code>. app在线人数
     */
    @Override
    public void setAppOnlineNumber(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_app_online_number</code>. app在线人数
     */
    @Override
    public Integer getAppOnlineNumber() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_hour</code>. 小时
     */
    @Override
    public void setHour(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_hour</code>. 小时
     */
    @Override
    public Integer getHour() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_day</code>. 日
     */
    @Override
    public void setDay(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_day</code>. 日
     */
    @Override
    public Integer getDay() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_month</code>. 月
     */
    @Override
    public void setMonth(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_month</code>. 月
     */
    @Override
    public Integer getMonth() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_online_statistics.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_online_statistics.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return OnlineStatistics.ONLINE_STATISTICS.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return OnlineStatistics.ONLINE_STATISTICS.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return OnlineStatistics.ONLINE_STATISTICS.ONLINE_NUMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return OnlineStatistics.ONLINE_STATISTICS.PC_ONLINE_NUMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return OnlineStatistics.ONLINE_STATISTICS.APP_ONLINE_NUMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return OnlineStatistics.ONLINE_STATISTICS.HOUR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return OnlineStatistics.ONLINE_STATISTICS.DAY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return OnlineStatistics.ONLINE_STATISTICS.MONTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return OnlineStatistics.ONLINE_STATISTICS.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getOnlineNumber();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getPcOnlineNumber();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getAppOnlineNumber();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getHour();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getDay();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getMonth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value2(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value3(Integer value) {
        setOnlineNumber(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value4(Integer value) {
        setPcOnlineNumber(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value5(Integer value) {
        setAppOnlineNumber(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value6(Integer value) {
        setHour(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value7(Integer value) {
        setDay(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value8(Integer value) {
        setMonth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord value9(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OnlineStatisticsRecord values(String value1, String value2, Integer value3, Integer value4, Integer value5, Integer value6, Integer value7, Integer value8, Long value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOnlineStatistics from) {
        setId(from.getId());
        setOrganizationId(from.getOrganizationId());
        setOnlineNumber(from.getOnlineNumber());
        setPcOnlineNumber(from.getPcOnlineNumber());
        setAppOnlineNumber(from.getAppOnlineNumber());
        setHour(from.getHour());
        setDay(from.getDay());
        setMonth(from.getMonth());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOnlineStatistics> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OnlineStatisticsRecord
     */
    public OnlineStatisticsRecord() {
        super(OnlineStatistics.ONLINE_STATISTICS);
    }

    /**
     * Create a detached, initialised OnlineStatisticsRecord
     */
    public OnlineStatisticsRecord(String id, String organizationId, Integer onlineNumber, Integer pcOnlineNumber, Integer appOnlineNumber, Integer hour, Integer day, Integer month, Long createTime) {
        super(OnlineStatistics.ONLINE_STATISTICS);

        set(0, id);
        set(1, organizationId);
        set(2, onlineNumber);
        set(3, pcOnlineNumber);
        set(4, appOnlineNumber);
        set(5, hour);
        set(6, day);
        set(7, month);
        set(8, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.OnlineStatisticsEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.OnlineStatisticsEntity pojo = (com.zxy.product.log.jooq.tables.pojos.OnlineStatisticsEntity)source;
        pojo.into(this);
        return true;
    }
}
