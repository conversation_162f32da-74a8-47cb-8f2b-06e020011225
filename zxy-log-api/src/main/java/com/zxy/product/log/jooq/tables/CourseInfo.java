/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.CourseInfoRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * otter同步课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfo extends TableImpl<CourseInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_course_info</code>
     */
    public static final CourseInfo COURSE_INFO = new CourseInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseInfoRecord> getRecordType() {
        return CourseInfoRecord.class;
    }

    /**
     * The column <code>zxy-log.t_course_info.f_id</code>.
     */
    public final TableField<CourseInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>zxy-log.t_course_info.f_name</code>. 课程名称
     */
    public final TableField<CourseInfoRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程名称");

    /**
     * The column <code>zxy-log.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    public final TableField<CourseInfoRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "业务类型，0-课程；1-学习路径；2-专题");

    /**
     * The column <code>zxy-log.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    public final TableField<CourseInfoRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)");

    /**
     * The column <code>zxy-log.t_course_info.f_organization_id</code>. 组织ID
     */
    public final TableField<CourseInfoRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织ID");

    /**
     * The column <code>zxy-log.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    public final TableField<CourseInfoRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "删除标识 0 未删除， 1 已删除");

    /**
     * The column <code>zxy-log.t_course_info.f_create_time</code>. 创建时间
     */
    public final TableField<CourseInfoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>zxy-log.t_course_info</code> table reference
     */
    public CourseInfo() {
        this("t_course_info", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_course_info</code> table reference
     */
    public CourseInfo(String alias) {
        this(alias, COURSE_INFO);
    }

    private CourseInfo(String alias, Table<CourseInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseInfo(String alias, Table<CourseInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "otter同步课程表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseInfoRecord>>asList(Keys.KEY_T_COURSE_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfo as(String alias) {
        return new CourseInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseInfo rename(String name) {
        return new CourseInfo(name, null);
    }
}
