/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IMember;

import javax.annotation.Generated;


/**
 * 人员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberEntity extends BaseEntity implements IMember {

    private static final long serialVersionUID = 1L;

    private String  name;
    private String  fullName;
    private String  password;
    private String  salt;
    private String  majorPositionId;
    private String  email;
    private String  phoneNumber;
    private Integer status;
    private Integer sex;
    private Integer sequence;
    private Integer init;
    private String  organizationId;
    private Integer initSetting;
    private Integer from;
    private String  companyId;

    public MemberEntity() {}

    public MemberEntity(MemberEntity value) {
        this.name = value.name;
        this.fullName = value.fullName;
        this.password = value.password;
        this.salt = value.salt;
        this.majorPositionId = value.majorPositionId;
        this.email = value.email;
        this.phoneNumber = value.phoneNumber;
        this.status = value.status;
        this.sex = value.sex;
        this.sequence = value.sequence;
        this.init = value.init;
        this.organizationId = value.organizationId;
        this.initSetting = value.initSetting;
        this.from = value.from;
        this.companyId = value.companyId;
    }

    public MemberEntity(
        String  id,
        Long    createTime,
        String  name,
        String  fullName,
        String  password,
        String  salt,
        String  majorPositionId,
        String  email,
        String  phoneNumber,
        Integer status,
        Integer sex,
        Integer sequence,
        Integer init,
        String  organizationId,
        Integer initSetting,
        Integer from,
        String  companyId
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.name = name;
        this.fullName = fullName;
        this.password = password;
        this.salt = salt;
        this.majorPositionId = majorPositionId;
        this.email = email;
        this.phoneNumber = phoneNumber;
        this.status = status;
        this.sex = sex;
        this.sequence = sequence;
        this.init = init;
        this.organizationId = organizationId;
        this.initSetting = initSetting;
        this.from = from;
        this.companyId = companyId;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getFullName() {
        return this.fullName;
    }

    @Override
    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String getSalt() {
        return this.salt;
    }

    @Override
    public void setSalt(String salt) {
        this.salt = salt;
    }

    @Override
    public String getMajorPositionId() {
        return this.majorPositionId;
    }

    @Override
    public void setMajorPositionId(String majorPositionId) {
        this.majorPositionId = majorPositionId;
    }

    @Override
    public String getEmail() {
        return this.email;
    }

    @Override
    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    @Override
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getSex() {
        return this.sex;
    }

    @Override
    public void setSex(Integer sex) {
        this.sex = sex;
    }

    @Override
    public Integer getSequence() {
        return this.sequence;
    }

    @Override
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    @Override
    public Integer getInit() {
        return this.init;
    }

    @Override
    public void setInit(Integer init) {
        this.init = init;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Integer getInitSetting() {
        return this.initSetting;
    }

    @Override
    public void setInitSetting(Integer initSetting) {
        this.initSetting = initSetting;
    }

    @Override
    public Integer getFrom() {
        return this.from;
    }

    @Override
    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String getCompanyId() {
        return this.companyId;
    }

    @Override
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("MemberEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(name);
        sb.append(", ").append(fullName);
        sb.append(", ").append(password);
        sb.append(", ").append(salt);
        sb.append(", ").append(majorPositionId);
        sb.append(", ").append(email);
        sb.append(", ").append(phoneNumber);
        sb.append(", ").append(status);
        sb.append(", ").append(sex);
        sb.append(", ").append(sequence);
        sb.append(", ").append(init);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(initSetting);
        sb.append(", ").append(from);
        sb.append(", ").append(companyId);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMember from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setFullName(from.getFullName());
        setPassword(from.getPassword());
        setSalt(from.getSalt());
        setMajorPositionId(from.getMajorPositionId());
        setEmail(from.getEmail());
        setPhoneNumber(from.getPhoneNumber());
        setStatus(from.getStatus());
        setSex(from.getSex());
        setSequence(from.getSequence());
        setInit(from.getInit());
        setOrganizationId(from.getOrganizationId());
        setInitSetting(from.getInitSetting());
        setFrom(from.getFrom());
        setCompanyId(from.getCompanyId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMember> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends MemberEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.MemberRecord r = new com.zxy.product.log.jooq.tables.records.MemberRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.ID, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.NAME, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.FULL_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.FULL_NAME, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.FULL_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.PASSWORD.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.PASSWORD, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.PASSWORD));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.SALT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.SALT, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.SALT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.MAJOR_POSITION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.MAJOR_POSITION_ID, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.MAJOR_POSITION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.EMAIL.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.EMAIL, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.EMAIL));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.PHONE_NUMBER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.PHONE_NUMBER, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.PHONE_NUMBER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.STATUS, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.SEX.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.SEX, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.SEX));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.SEQUENCE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.SEQUENCE, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.SEQUENCE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.INIT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.INIT, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.INIT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.ORGANIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.INIT_SETTING.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.INIT_SETTING, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.INIT_SETTING));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.FROM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.FROM, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.FROM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.Member.MEMBER.COMPANY_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.Member.MEMBER.COMPANY_ID, record.getValue(com.zxy.product.log.jooq.tables.Member.MEMBER.COMPANY_ID));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
