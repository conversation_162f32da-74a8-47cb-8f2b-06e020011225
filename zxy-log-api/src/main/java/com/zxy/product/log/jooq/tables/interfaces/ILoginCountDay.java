/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 用户登录每日统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILoginCountDay extends Serializable {

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_day</code>. 日
     */
    public void setDay(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_day</code>. 日
     */
    public Integer getDay();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_month</code>. 月
     */
    public void setMonth(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_month</code>. 月
     */
    public Integer getMonth();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_year</code>. 年
     */
    public void setYear(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_year</code>. 年
     */
    public Integer getYear();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_member_num</code>. 登录人数
     */
    public void setMemberNum(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_member_num</code>. 登录人数
     */
    public Integer getMemberNum();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_member_time</code>. 登录人次
     */
    public void setMemberTime(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_member_time</code>. 登录人次
     */
    public Integer getMemberTime();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_system_num</code>. 系统总人数
     */
    public void setSystemNum(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_system_num</code>. 系统总人数
     */
    public Integer getSystemNum();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_male_num</code>. 男登录人数
     */
    public void setMaleNum(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_male_num</code>. 男登录人数
     */
    public Integer getMaleNum();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_female_num</code>. 女登录人数
     */
    public void setFemaleNum(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_female_num</code>. 女登录人数
     */
    public Integer getFemaleNum();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_zero_two</code>. 入职年限0~2年登录人数
     */
    public void setEntryYearZeroTwo(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_zero_two</code>. 入职年限0~2年登录人数
     */
    public Integer getEntryYearZeroTwo();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_three_five</code>. 入职年限3~5年登录人数
     */
    public void setEntryYearThreeFive(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_three_five</code>. 入职年限3~5年登录人数
     */
    public Integer getEntryYearThreeFive();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_six_ten</code>. 入职年限6~10年登录人数
     */
    public void setEntryYearSixTen(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_six_ten</code>. 入职年限6~10年登录人数
     */
    public Integer getEntryYearSixTen();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_entry_year_ten</code>. 入职年限10年以上登录人数
     */
    public void setEntryYearTen(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_entry_year_ten</code>. 入职年限10年以上登录人数
     */
    public Integer getEntryYearTen();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_political_outlook</code>. 政治面貌(中共党员（含预备党员）)登录人数
     */
    public void setPoliticalOutlook(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_political_outlook</code>. 政治面貌(中共党员（含预备党员）)登录人数
     */
    public Integer getPoliticalOutlook();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_login_count_day.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>zxy-log.t_login_count_day.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILoginCountDay
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.ILoginCountDay from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILoginCountDay
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.ILoginCountDay> E into(E into);
}
