/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * otter同步课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseInfo extends Serializable {

    /**
     * Setter for <code>zxy-log.t_course_info.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_course_info.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_course_info.f_name</code>. 课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>zxy-log.t_course_info.f_name</code>. 课程名称
     */
    public String getName();

    /**
     * Setter for <code>zxy-log.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>zxy-log.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>zxy-log.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>zxy-log.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    public Integer getStatus();

    /**
     * Setter for <code>zxy-log.t_course_info.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_course_info.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>zxy-log.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>zxy-log.t_course_info.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_course_info.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseInfo
     */
    public void from(ICourseInfo from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseInfo
     */
    public <E extends ICourseInfo> E into(E into);
}
