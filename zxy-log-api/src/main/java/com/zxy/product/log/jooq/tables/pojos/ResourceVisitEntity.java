/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IResourceVisit;

import javax.annotation.Generated;


/**
 * 资源浏览统计表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResourceVisitEntity extends BaseEntity implements IResourceVisit {

    private static final long serialVersionUID = 1L;

    private String  contentId;
    private String  contentName;
    private Integer contentType;
    private Integer visit;
    private Integer day;

    public ResourceVisitEntity() {}

    public ResourceVisitEntity(ResourceVisitEntity value) {
        this.contentId = value.contentId;
        this.contentName = value.contentName;
        this.contentType = value.contentType;
        this.visit = value.visit;
        this.day = value.day;
    }

    public ResourceVisitEntity(
        String  id,
        String  contentId,
        String  contentName,
        Integer contentType,
        Integer visit,
        Integer day,
        Long    createTime
    ) {
        super.setId(id);
        this.contentId = contentId;
        this.contentName = contentName;
        this.contentType = contentType;
        this.visit = visit;
        this.day = day;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getContentId() {
        return this.contentId;
    }

    @Override
    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    @Override
    public String getContentName() {
        return this.contentName;
    }

    @Override
    public void setContentName(String contentName) {
        this.contentName = contentName;
    }

    @Override
    public Integer getContentType() {
        return this.contentType;
    }

    @Override
    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    @Override
    public Integer getVisit() {
        return this.visit;
    }

    @Override
    public void setVisit(Integer visit) {
        this.visit = visit;
    }

    @Override
    public Integer getDay() {
        return this.day;
    }

    @Override
    public void setDay(Integer day) {
        this.day = day;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ResourceVisitEntity (");

        sb.append(getId());
        sb.append(", ").append(contentId);
        sb.append(", ").append(contentName);
        sb.append(", ").append(contentType);
        sb.append(", ").append(visit);
        sb.append(", ").append(day);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IResourceVisit from) {
        setId(from.getId());
        setContentId(from.getContentId());
        setContentName(from.getContentName());
        setContentType(from.getContentType());
        setVisit(from.getVisit());
        setDay(from.getDay());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IResourceVisit> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ResourceVisitEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.ResourceVisitRecord r = new com.zxy.product.log.jooq.tables.records.ResourceVisitRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.ID, record.getValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_ID, record.getValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_NAME, record.getValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_TYPE, record.getValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CONTENT_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.VISIT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.VISIT, record.getValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.VISIT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.DAY.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.DAY, record.getValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.DAY));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.ResourceVisit.RESOURCE_VISIT.CREATE_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
