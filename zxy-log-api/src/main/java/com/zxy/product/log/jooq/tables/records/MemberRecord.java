/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.Member;
import com.zxy.product.log.jooq.tables.interfaces.IMember;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record17;
import org.jooq.Row17;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;


/**
 * 人员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MemberRecord extends UpdatableRecordImpl<MemberRecord> implements Record17<String, Long, String, String, String, String, String, String, String, Integer, Integer, Integer, Integer, String, Integer, Integer, String>, IMember {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_member.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_name</code>. 人员名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_name</code>. 人员名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_full_name</code>. 姓名
     */
    @Override
    public void setFullName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_full_name</code>. 姓名
     */
    @Override
    public String getFullName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_password</code>. 密码 
     */
    @Override
    public void setPassword(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_password</code>. 密码 
     */
    @Override
    public String getPassword() {
        return (String) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_salt</code>. 密码盐
     */
    @Override
    public void setSalt(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_salt</code>. 密码盐
     */
    @Override
    public String getSalt() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_major_position_id</code>. 岗位
     */
    @Override
    public void setMajorPositionId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_major_position_id</code>. 岗位
     */
    @Override
    public String getMajorPositionId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_email</code>.
     */
    @Override
    public void setEmail(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_email</code>.
     */
    @Override
    public String getEmail() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_phone_number</code>. 手机号
     */
    @Override
    public void setPhoneNumber(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_phone_number</code>. 手机号
     */
    @Override
    public String getPhoneNumber() {
        return (String) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_status</code>. 人员状态
     */
    @Override
    public void setStatus(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_status</code>. 人员状态
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_sex</code>. 性别
     */
    @Override
    public void setSex(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_sex</code>. 性别
     */
    @Override
    public Integer getSex() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_sequence</code>. 排序号
     */
    @Override
    public void setSequence(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_sequence</code>. 排序号
     */
    @Override
    public Integer getSequence() {
        return (Integer) get(11);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_init</code>. 是否为初始化数据(0=是,1=否)
     */
    @Override
    public void setInit(Integer value) {
        set(12, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_init</code>. 是否为初始化数据(0=是,1=否)
     */
    @Override
    public Integer getInit() {
        return (Integer) get(12);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(13);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_init_setting</code>. 0-未初始化设置，1-已初始化设置
     */
    @Override
    public void setInitSetting(Integer value) {
        set(14, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_init_setting</code>. 0-未初始化设置，1-已初始化设置
     */
    @Override
    public Integer getInitSetting() {
        return (Integer) get(14);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_from</code>. 1-内部用户，2-注册用户
     */
    @Override
    public void setFrom(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_from</code>. 1-内部用户，2-注册用户
     */
    @Override
    public Integer getFrom() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>zxy-log.t_member.f_company_id</code>. 表示离当前用户所在组织的最近的4层内组织id
     */
    @Override
    public void setCompanyId(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>zxy-log.t_member.f_company_id</code>. 表示离当前用户所在组织的最近的4层内组织id
     */
    @Override
    public String getCompanyId() {
        return (String) get(16);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record17 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, Long, String, String, String, String, String, String, String, Integer, Integer, Integer, Integer, String, Integer, Integer, String> fieldsRow() {
        return (Row17) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row17<String, Long, String, String, String, String, String, String, String, Integer, Integer, Integer, Integer, String, Integer, Integer, String> valuesRow() {
        return (Row17) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return Member.MEMBER.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return Member.MEMBER.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return Member.MEMBER.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return Member.MEMBER.FULL_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return Member.MEMBER.PASSWORD;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return Member.MEMBER.SALT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return Member.MEMBER.MAJOR_POSITION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return Member.MEMBER.EMAIL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return Member.MEMBER.PHONE_NUMBER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return Member.MEMBER.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return Member.MEMBER.SEX;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return Member.MEMBER.SEQUENCE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field13() {
        return Member.MEMBER.INIT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field14() {
        return Member.MEMBER.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field15() {
        return Member.MEMBER.INIT_SETTING;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field16() {
        return Member.MEMBER.FROM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field17() {
        return Member.MEMBER.COMPANY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getFullName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getPassword();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getSalt();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getMajorPositionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getEmail();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getPhoneNumber();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getSex();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getSequence();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value13() {
        return getInit();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value14() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value15() {
        return getInitSetting();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value16() {
        return getFrom();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value17() {
        return getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value3(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value4(String value) {
        setFullName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value5(String value) {
        setPassword(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value6(String value) {
        setSalt(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value7(String value) {
        setMajorPositionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value8(String value) {
        setEmail(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value9(String value) {
        setPhoneNumber(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value10(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value11(Integer value) {
        setSex(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value12(Integer value) {
        setSequence(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value13(Integer value) {
        setInit(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value14(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value15(Integer value) {
        setInitSetting(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value16(Integer value) {
        setFrom(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord value17(String value) {
        setCompanyId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MemberRecord values(String value1, Long value2, String value3, String value4, String value5, String value6, String value7, String value8, String value9, Integer value10, Integer value11, Integer value12, Integer value13, String value14, Integer value15, Integer value16, String value17) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IMember from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setFullName(from.getFullName());
        setPassword(from.getPassword());
        setSalt(from.getSalt());
        setMajorPositionId(from.getMajorPositionId());
        setEmail(from.getEmail());
        setPhoneNumber(from.getPhoneNumber());
        setStatus(from.getStatus());
        setSex(from.getSex());
        setSequence(from.getSequence());
        setInit(from.getInit());
        setOrganizationId(from.getOrganizationId());
        setInitSetting(from.getInitSetting());
        setFrom(from.getFrom());
        setCompanyId(from.getCompanyId());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IMember> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MemberRecord
     */
    public MemberRecord() {
        super(Member.MEMBER);
    }

    /**
     * Create a detached, initialised MemberRecord
     */
    public MemberRecord(String id, Long createTime, String name, String fullName, String password, String salt, String majorPositionId, String email, String phoneNumber, Integer status, Integer sex, Integer sequence, Integer init, String organizationId, Integer initSetting, Integer from, String companyId) {
        super(Member.MEMBER);

        set(0, id);
        set(1, createTime);
        set(2, name);
        set(3, fullName);
        set(4, password);
        set(5, salt);
        set(6, majorPositionId);
        set(7, email);
        set(8, phoneNumber);
        set(9, status);
        set(10, sex);
        set(11, sequence);
        set(12, init);
        set(13, organizationId);
        set(14, initSetting);
        set(15, from);
        set(16, companyId);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.MemberEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.MemberEntity pojo = (com.zxy.product.log.jooq.tables.pojos.MemberEntity)source;
        pojo.into(this);
        return true;
    }
}
