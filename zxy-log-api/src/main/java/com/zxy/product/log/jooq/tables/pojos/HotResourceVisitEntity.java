/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IHotResourceVisit;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 热门学习资源表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HotResourceVisitEntity extends BaseEntity implements IHotResourceVisit {

    private static final long serialVersionUID = 1L;

    private String    businessId;
    private String    businessName;
    private Integer   businessType;
    private String    organizationId;
    private String    organizationName;
    private Integer   day30Visit;
    private Integer   status;
    private Timestamp modifyDate;

    public HotResourceVisitEntity() {}

    public HotResourceVisitEntity(HotResourceVisitEntity value) {
        this.businessId = value.businessId;
        this.businessName = value.businessName;
        this.businessType = value.businessType;
        this.organizationId = value.organizationId;
        this.organizationName = value.organizationName;
        this.day30Visit = value.day30Visit;
        this.status = value.status;
        this.modifyDate = value.modifyDate;
    }

    public HotResourceVisitEntity(
        String    id,
        String    businessId,
        String    businessName,
        Integer   businessType,
        String    organizationId,
        String    organizationName,
        Integer   day30Visit,
        Integer   status,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.businessId = businessId;
        this.businessName = businessName;
        this.businessType = businessType;
        this.organizationId = organizationId;
        this.organizationName = organizationName;
        this.day30Visit = day30Visit;
        this.status = status;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getBusinessId() {
        return this.businessId;
    }

    @Override
    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    @Override
    public String getBusinessName() {
        return this.businessName;
    }

    @Override
    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    @Override
    public Integer getBusinessType() {
        return this.businessType;
    }

    @Override
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getOrganizationName() {
        return this.organizationName;
    }

    @Override
    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    @Override
    public Integer getDay30Visit() {
        return this.day30Visit;
    }

    @Override
    public void setDay30Visit(Integer day30Visit) {
        this.day30Visit = day30Visit;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("HotResourceVisitEntity (");

        sb.append(getId());
        sb.append(", ").append(businessId);
        sb.append(", ").append(businessName);
        sb.append(", ").append(businessType);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(organizationName);
        sb.append(", ").append(day30Visit);
        sb.append(", ").append(status);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IHotResourceVisit from) {
        setId(from.getId());
        setBusinessId(from.getBusinessId());
        setBusinessName(from.getBusinessName());
        setBusinessType(from.getBusinessType());
        setOrganizationId(from.getOrganizationId());
        setOrganizationName(from.getOrganizationName());
        setDay30Visit(from.getDay30Visit());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IHotResourceVisit> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends HotResourceVisitEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.HotResourceVisitRecord r = new com.zxy.product.log.jooq.tables.records.HotResourceVisitRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ID, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_ID, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_NAME, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_TYPE, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.BUSINESS_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_NAME, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.ORGANIZATION_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.DAY30_VISIT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.DAY30_VISIT, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.DAY30_VISIT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.STATUS, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.MODIFY_DATE, record.getValue(com.zxy.product.log.jooq.tables.HotResourceVisit.HOT_RESOURCE_VISIT.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
