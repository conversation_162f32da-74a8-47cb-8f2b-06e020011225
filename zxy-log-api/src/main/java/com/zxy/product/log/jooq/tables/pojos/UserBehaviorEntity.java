/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IUserBehavior;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserBehaviorEntity extends BaseEntity implements IUserBehavior {

    private static final long serialVersionUID = 1L;

    private String userId;
    private String contentId;
    private String contentType;
    private String contentName;
    private String clientType;
    private String type;
    private String value;
    private String pageSource;
    private String status;
    private Long   modifiTime;

    public UserBehaviorEntity() {}

    public UserBehaviorEntity(UserBehaviorEntity value) {
        this.userId = value.userId;
        this.contentId = value.contentId;
        this.contentType = value.contentType;
        this.contentName = value.contentName;
        this.clientType = value.clientType;
        this.type = value.type;
        this.value = value.value;
        this.pageSource = value.pageSource;
        this.status = value.status;
        this.modifiTime = value.modifiTime;
    }

    public UserBehaviorEntity(
        String id,
        String userId,
        String contentId,
        String contentType,
        String contentName,
        String clientType,
        String type,
        String value,
        String pageSource,
        String status,
        Long   createTime,
        Long   modifiTime
    ) {
        super.setId(id);
        this.userId = userId;
        this.contentId = contentId;
        this.contentType = contentType;
        this.contentName = contentName;
        this.clientType = clientType;
        this.type = type;
        this.value = value;
        this.pageSource = pageSource;
        this.status = status;
        super.setCreateTime(createTime);
        this.modifiTime = modifiTime;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getUserId() {
        return this.userId;
    }

    @Override
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String getContentId() {
        return this.contentId;
    }

    @Override
    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    @Override
    public String getContentType() {
        return this.contentType;
    }

    @Override
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    @Override
    public String getContentName() {
        return this.contentName;
    }

    @Override
    public void setContentName(String contentName) {
        this.contentName = contentName;
    }

    @Override
    public String getClientType() {
        return this.clientType;
    }

    @Override
    public void setClientType(String clientType) {
        this.clientType = clientType;
    }

    @Override
    public String getType() {
        return this.type;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String getValue() {
        return this.value;
    }

    @Override
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getPageSource() {
        return this.pageSource;
    }

    @Override
    public void setPageSource(String pageSource) {
        this.pageSource = pageSource;
    }

    @Override
    public String getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getModifiTime() {
        return this.modifiTime;
    }

    @Override
    public void setModifiTime(Long modifiTime) {
        this.modifiTime = modifiTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("UserBehaviorEntity (");

        sb.append(getId());
        sb.append(", ").append(userId);
        sb.append(", ").append(contentId);
        sb.append(", ").append(contentType);
        sb.append(", ").append(contentName);
        sb.append(", ").append(clientType);
        sb.append(", ").append(type);
        sb.append(", ").append(value);
        sb.append(", ").append(pageSource);
        sb.append(", ").append(status);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifiTime);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IUserBehavior from) {
        setId(from.getId());
        setUserId(from.getUserId());
        setContentId(from.getContentId());
        setContentType(from.getContentType());
        setContentName(from.getContentName());
        setClientType(from.getClientType());
        setType(from.getType());
        setValue(from.getValue());
        setPageSource(from.getPageSource());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setModifiTime(from.getModifiTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IUserBehavior> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends UserBehaviorEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.UserBehaviorRecord r = new com.zxy.product.log.jooq.tables.records.UserBehaviorRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.ID, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.USER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.USER_ID, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.USER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_ID, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_TYPE, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_NAME, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CONTENT_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CLIENT_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CLIENT_TYPE, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CLIENT_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.TYPE, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.VALUE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.VALUE, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.VALUE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.PAGE_SOURCE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.PAGE_SOURCE, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.PAGE_SOURCE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.STATUS, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.MODIFI_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.MODIFI_TIME, record.getValue(com.zxy.product.log.jooq.tables.UserBehavior.USER_BEHAVIOR.MODIFI_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
