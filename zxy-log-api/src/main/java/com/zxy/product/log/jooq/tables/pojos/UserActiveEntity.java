/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IUserActive;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 用户活跃表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UserActiveEntity extends BaseEntity implements IUserActive {

    private static final long serialVersionUID = 1L;

    private String    memberId;
    private Integer   loginTimes;
    private Integer   yearInfo;
    private Integer   monthInfo;
    private Integer   dayInfo;
    private Integer   quarterInfo;
    private Integer   weekInfo;
    private Timestamp modifyDate;

    public UserActiveEntity() {}

    public UserActiveEntity(UserActiveEntity value) {
        this.memberId = value.memberId;
        this.loginTimes = value.loginTimes;
        this.yearInfo = value.yearInfo;
        this.monthInfo = value.monthInfo;
        this.dayInfo = value.dayInfo;
        this.quarterInfo = value.quarterInfo;
        this.weekInfo = value.weekInfo;
        this.modifyDate = value.modifyDate;
    }

    public UserActiveEntity(
        String    id,
        String    memberId,
        Integer   loginTimes,
        Integer   yearInfo,
        Integer   monthInfo,
        Integer   dayInfo,
        Integer   quarterInfo,
        Integer   weekInfo,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.loginTimes = loginTimes;
        this.yearInfo = yearInfo;
        this.monthInfo = monthInfo;
        this.dayInfo = dayInfo;
        this.quarterInfo = quarterInfo;
        this.weekInfo = weekInfo;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public Integer getLoginTimes() {
        return this.loginTimes;
    }

    @Override
    public void setLoginTimes(Integer loginTimes) {
        this.loginTimes = loginTimes;
    }

    @Override
    public Integer getYearInfo() {
        return this.yearInfo;
    }

    @Override
    public void setYearInfo(Integer yearInfo) {
        this.yearInfo = yearInfo;
    }

    @Override
    public Integer getMonthInfo() {
        return this.monthInfo;
    }

    @Override
    public void setMonthInfo(Integer monthInfo) {
        this.monthInfo = monthInfo;
    }

    @Override
    public Integer getDayInfo() {
        return this.dayInfo;
    }

    @Override
    public void setDayInfo(Integer dayInfo) {
        this.dayInfo = dayInfo;
    }

    @Override
    public Integer getQuarterInfo() {
        return this.quarterInfo;
    }

    @Override
    public void setQuarterInfo(Integer quarterInfo) {
        this.quarterInfo = quarterInfo;
    }

    @Override
    public Integer getWeekInfo() {
        return this.weekInfo;
    }

    @Override
    public void setWeekInfo(Integer weekInfo) {
        this.weekInfo = weekInfo;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("UserActiveEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(loginTimes);
        sb.append(", ").append(yearInfo);
        sb.append(", ").append(monthInfo);
        sb.append(", ").append(dayInfo);
        sb.append(", ").append(quarterInfo);
        sb.append(", ").append(weekInfo);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IUserActive from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setLoginTimes(from.getLoginTimes());
        setYearInfo(from.getYearInfo());
        setMonthInfo(from.getMonthInfo());
        setDayInfo(from.getDayInfo());
        setQuarterInfo(from.getQuarterInfo());
        setWeekInfo(from.getWeekInfo());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IUserActive> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends UserActiveEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.UserActiveRecord r = new com.zxy.product.log.jooq.tables.records.UserActiveRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.ID, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MEMBER_ID, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.LOGIN_TIMES) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.LOGIN_TIMES, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.LOGIN_TIMES));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.YEAR_INFO) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.YEAR_INFO, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.YEAR_INFO));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MONTH_INFO) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MONTH_INFO, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MONTH_INFO));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.DAY_INFO) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.DAY_INFO, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.DAY_INFO));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.QUARTER_INFO) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.QUARTER_INFO, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.QUARTER_INFO));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.WEEK_INFO) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.WEEK_INFO, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.WEEK_INFO));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MODIFY_DATE, record.getValue(com.zxy.product.log.jooq.tables.UserActive.USER_ACTIVE.MODIFY_DATE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
