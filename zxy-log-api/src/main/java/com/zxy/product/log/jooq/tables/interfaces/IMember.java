/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * 人员表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMember extends Serializable {

    /**
     * Setter for <code>zxy-log.t_member.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_member.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_member.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_member.f_name</code>. 人员名称
     */
    public void setName(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_name</code>. 人员名称
     */
    public String getName();

    /**
     * Setter for <code>zxy-log.t_member.f_full_name</code>. 姓名
     */
    public void setFullName(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_full_name</code>. 姓名
     */
    public String getFullName();

    /**
     * Setter for <code>zxy-log.t_member.f_password</code>. 密码 
     */
    public void setPassword(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_password</code>. 密码 
     */
    public String getPassword();

    /**
     * Setter for <code>zxy-log.t_member.f_salt</code>. 密码盐
     */
    public void setSalt(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_salt</code>. 密码盐
     */
    public String getSalt();

    /**
     * Setter for <code>zxy-log.t_member.f_major_position_id</code>. 岗位
     */
    public void setMajorPositionId(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_major_position_id</code>. 岗位
     */
    public String getMajorPositionId();

    /**
     * Setter for <code>zxy-log.t_member.f_email</code>.
     */
    public void setEmail(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_email</code>.
     */
    public String getEmail();

    /**
     * Setter for <code>zxy-log.t_member.f_phone_number</code>. 手机号
     */
    public void setPhoneNumber(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_phone_number</code>. 手机号
     */
    public String getPhoneNumber();

    /**
     * Setter for <code>zxy-log.t_member.f_status</code>. 人员状态
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>zxy-log.t_member.f_status</code>. 人员状态
     */
    public Integer getStatus();

    /**
     * Setter for <code>zxy-log.t_member.f_sex</code>. 性别
     */
    public void setSex(Integer value);

    /**
     * Getter for <code>zxy-log.t_member.f_sex</code>. 性别
     */
    public Integer getSex();

    /**
     * Setter for <code>zxy-log.t_member.f_sequence</code>. 排序号
     */
    public void setSequence(Integer value);

    /**
     * Getter for <code>zxy-log.t_member.f_sequence</code>. 排序号
     */
    public Integer getSequence();

    /**
     * Setter for <code>zxy-log.t_member.f_init</code>. 是否为初始化数据(0=是,1=否)
     */
    public void setInit(Integer value);

    /**
     * Getter for <code>zxy-log.t_member.f_init</code>. 是否为初始化数据(0=是,1=否)
     */
    public Integer getInit();

    /**
     * Setter for <code>zxy-log.t_member.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_member.f_init_setting</code>. 0-未初始化设置，1-已初始化设置
     */
    public void setInitSetting(Integer value);

    /**
     * Getter for <code>zxy-log.t_member.f_init_setting</code>. 0-未初始化设置，1-已初始化设置
     */
    public Integer getInitSetting();

    /**
     * Setter for <code>zxy-log.t_member.f_from</code>. 1-内部用户，2-注册用户
     */
    public void setFrom(Integer value);

    /**
     * Getter for <code>zxy-log.t_member.f_from</code>. 1-内部用户，2-注册用户
     */
    public Integer getFrom();

    /**
     * Setter for <code>zxy-log.t_member.f_company_id</code>. 表示离当前用户所在组织的最近的4层内组织id
     */
    public void setCompanyId(String value);

    /**
     * Getter for <code>zxy-log.t_member.f_company_id</code>. 表示离当前用户所在组织的最近的4层内组织id
     */
    public String getCompanyId();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMember
     */
    public void from(IMember from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMember
     */
    public <E extends IMember> E into(E into);
}
