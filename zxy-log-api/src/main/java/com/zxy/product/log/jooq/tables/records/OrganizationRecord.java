/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.Organization;
import com.zxy.product.log.jooq.tables.interfaces.IOrganization;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record20;
import org.jooq.Row20;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class OrganizationRecord extends UpdatableRecordImpl<OrganizationRecord> implements Record20<String, Long, String, String, String, String, String, String, Integer, Integer, Integer, String, String, Integer, String, String, String, Integer, Integer, String>, IOrganization {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_organization.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_name</code>.
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_name</code>.
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_short_name</code>.
     */
    @Override
    public void setShortName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_short_name</code>.
     */
    @Override
    public String getShortName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_code</code>. 组织编码
     */
    @Override
    public void setCode(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_code</code>. 组织编码
     */
    @Override
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_ihr_code</code>. ihr新组织编码
     */
    @Override
    public void setIhrCode(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_ihr_code</code>. ihr新组织编码
     */
    @Override
    public String getIhrCode() {
        return (String) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_parent_id</code>. 上级组织
     */
    @Override
    public void setParentId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_parent_id</code>. 上级组织
     */
    @Override
    public String getParentId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_path</code>. 当前节点的所有父节点
     */
    @Override
    public void setPath(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_path</code>. 当前节点的所有父节点
     */
    @Override
    public String getPath() {
        return (String) get(7);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_cmcc_level</code>. 公司分类(移动项目)
     */
    @Override
    public void setCmccLevel(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_cmcc_level</code>. 公司分类(移动项目)
     */
    @Override
    public Integer getCmccLevel() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_level</code>. 组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)
     */
    @Override
    public void setLevel(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_level</code>. 组织类型(1=管理节点;2=公司节点;3=分公司节点;4=部门节点)
     */
    @Override
    public Integer getLevel() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_order</code>. 排序
     */
    @Override
    public void setOrder(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_order</code>. 排序
     */
    @Override
    public Integer getOrder() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_cmcc_attribute</code>. 组织机构属性(移动项目)
     */
    @Override
    public void setCmccAttribute(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_cmcc_attribute</code>. 组织机构属性(移动项目)
     */
    @Override
    public String getCmccAttribute() {
        return (String) get(11);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_cmcc_category</code>. 组织机构类型(移动项目)
     */
    @Override
    public void setCmccCategory(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_cmcc_category</code>. 组织机构类型(移动项目)
     */
    @Override
    public String getCmccCategory() {
        return (String) get(12);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_status</code>. 组织状态
     */
    @Override
    public void setStatus(Integer value) {
        set(13, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_status</code>. 组织状态
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(13);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_company_id</code>. 所属机构
     */
    @Override
    public void setCompanyId(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_company_id</code>. 所属机构
     */
    @Override
    public String getCompanyId() {
        return (String) get(14);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_site_id</code>. 站点id
     */
    @Override
    public void setSiteId(String value) {
        set(15, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_site_id</code>. 站点id
     */
    @Override
    public String getSiteId() {
        return (String) get(15);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_mis_code</code>. MIS省公司简称，用于同步数据
     */
    @Override
    public void setMisCode(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_mis_code</code>. MIS省公司简称，用于同步数据
     */
    @Override
    public String getMisCode() {
        return (String) get(16);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_depth</code>. 表示当前组织深度
     */
    @Override
    public void setDepth(Integer value) {
        set(17, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_depth</code>. 表示当前组织深度
     */
    @Override
    public Integer getDepth() {
        return (Integer) get(17);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_type</code>. 组织类型:0非内外部 1内部组织 2外部组织
     */
    @Override
    public void setType(Integer value) {
        set(18, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_type</code>. 组织类型:0非内外部 1内部组织 2外部组织
     */
    @Override
    public Integer getType() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>zxy-log.t_organization.f_area_code</code>. 区号
     */
    @Override
    public void setAreaCode(String value) {
        set(19, value);
    }

    /**
     * Getter for <code>zxy-log.t_organization.f_area_code</code>. 区号
     */
    @Override
    public String getAreaCode() {
        return (String) get(19);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record20 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row20<String, Long, String, String, String, String, String, String, Integer, Integer, Integer, String, String, Integer, String, String, String, Integer, Integer, String> fieldsRow() {
        return (Row20) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row20<String, Long, String, String, String, String, String, String, Integer, Integer, Integer, String, String, Integer, String, String, String, Integer, Integer, String> valuesRow() {
        return (Row20) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return Organization.ORGANIZATION.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return Organization.ORGANIZATION.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return Organization.ORGANIZATION.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return Organization.ORGANIZATION.SHORT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return Organization.ORGANIZATION.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return Organization.ORGANIZATION.IHR_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return Organization.ORGANIZATION.PARENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return Organization.ORGANIZATION.PATH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return Organization.ORGANIZATION.CMCC_LEVEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return Organization.ORGANIZATION.LEVEL;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field11() {
        return Organization.ORGANIZATION.ORDER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return Organization.ORGANIZATION.CMCC_ATTRIBUTE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field13() {
        return Organization.ORGANIZATION.CMCC_CATEGORY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field14() {
        return Organization.ORGANIZATION.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field15() {
        return Organization.ORGANIZATION.COMPANY_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field16() {
        return Organization.ORGANIZATION.SITE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field17() {
        return Organization.ORGANIZATION.MIS_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field18() {
        return Organization.ORGANIZATION.DEPTH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field19() {
        return Organization.ORGANIZATION.TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field20() {
        return Organization.ORGANIZATION.AREA_CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getShortName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getIhrCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getParentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getPath();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getCmccLevel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getLevel();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value11() {
        return getOrder();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getCmccAttribute();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value13() {
        return getCmccCategory();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value14() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value15() {
        return getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value16() {
        return getSiteId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value17() {
        return getMisCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value18() {
        return getDepth();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value19() {
        return getType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value20() {
        return getAreaCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value3(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value4(String value) {
        setShortName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value5(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value6(String value) {
        setIhrCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value7(String value) {
        setParentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value8(String value) {
        setPath(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value9(Integer value) {
        setCmccLevel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value10(Integer value) {
        setLevel(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value11(Integer value) {
        setOrder(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value12(String value) {
        setCmccAttribute(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value13(String value) {
        setCmccCategory(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value14(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value15(String value) {
        setCompanyId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value16(String value) {
        setSiteId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value17(String value) {
        setMisCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value18(Integer value) {
        setDepth(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value19(Integer value) {
        setType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord value20(String value) {
        setAreaCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public OrganizationRecord values(String value1, Long value2, String value3, String value4, String value5, String value6, String value7, String value8, Integer value9, Integer value10, Integer value11, String value12, String value13, Integer value14, String value15, String value16, String value17, Integer value18, Integer value19, String value20) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        value16(value16);
        value17(value17);
        value18(value18);
        value19(value19);
        value20(value20);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IOrganization from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setName(from.getName());
        setShortName(from.getShortName());
        setCode(from.getCode());
        setIhrCode(from.getIhrCode());
        setParentId(from.getParentId());
        setPath(from.getPath());
        setCmccLevel(from.getCmccLevel());
        setLevel(from.getLevel());
        setOrder(from.getOrder());
        setCmccAttribute(from.getCmccAttribute());
        setCmccCategory(from.getCmccCategory());
        setStatus(from.getStatus());
        setCompanyId(from.getCompanyId());
        setSiteId(from.getSiteId());
        setMisCode(from.getMisCode());
        setDepth(from.getDepth());
        setType(from.getType());
        setAreaCode(from.getAreaCode());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IOrganization> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached OrganizationRecord
     */
    public OrganizationRecord() {
        super(Organization.ORGANIZATION);
    }

    /**
     * Create a detached, initialised OrganizationRecord
     */
    public OrganizationRecord(String id, Long createTime, String name, String shortName, String code, String ihrCode, String parentId, String path, Integer cmccLevel, Integer level, Integer order, String cmccAttribute, String cmccCategory, Integer status, String companyId, String siteId, String misCode, Integer depth, Integer type, String areaCode) {
        super(Organization.ORGANIZATION);

        set(0, id);
        set(1, createTime);
        set(2, name);
        set(3, shortName);
        set(4, code);
        set(5, ihrCode);
        set(6, parentId);
        set(7, path);
        set(8, cmccLevel);
        set(9, level);
        set(10, order);
        set(11, cmccAttribute);
        set(12, cmccCategory);
        set(13, status);
        set(14, companyId);
        set(15, siteId);
        set(16, misCode);
        set(17, depth);
        set(18, type);
        set(19, areaCode);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.OrganizationEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.OrganizationEntity pojo = (com.zxy.product.log.jooq.tables.pojos.OrganizationEntity)source;
        pojo.into(this);
        return true;
    }
}
