/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.AuditLogRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AuditLog extends TableImpl<AuditLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_audit_log</code>
     */
    public static final AuditLog AUDIT_LOG = new AuditLog();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AuditLogRecord> getRecordType() {
        return AuditLogRecord.class;
    }

    /**
     * The column <code>zxy-log.t_audit_log.f_id</code>.
     */
    public final TableField<AuditLogRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>zxy-log.t_audit_log.f_create_time</code>. 创建时间
     */
    public final TableField<AuditLogRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT, this, "创建时间");

    /**
     * The column <code>zxy-log.t_audit_log.f_organization_id</code>. 所属组织
     */
    public final TableField<AuditLogRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "所属组织");

    /**
     * The column <code>zxy-log.t_audit_log.f_desc</code>. 描述
     */
    public final TableField<AuditLogRecord, String> DESC = createField("f_desc", org.jooq.impl.SQLDataType.VARCHAR.length(2000), this, "描述");

    /**
     * The column <code>zxy-log.t_audit_log.f_module</code>. 模块
     */
    public final TableField<AuditLogRecord, String> MODULE = createField("f_module", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "模块");

    /**
     * The column <code>zxy-log.t_audit_log.f_sub_module</code>. 子模块
     */
    public final TableField<AuditLogRecord, String> SUB_MODULE = createField("f_sub_module", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "子模块");

    /**
     * The column <code>zxy-log.t_audit_log.f_action</code>. 操作类型
     */
    public final TableField<AuditLogRecord, Integer> ACTION = createField("f_action", org.jooq.impl.SQLDataType.INTEGER, this, "操作类型");

    /**
     * The column <code>zxy-log.t_audit_log.f_first_action</code>. 一级操作
     */
    public final TableField<AuditLogRecord, String> FIRST_ACTION = createField("f_first_action", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "一级操作");

    /**
     * The column <code>zxy-log.t_audit_log.f_second_action</code>. 二级操作
     */
    public final TableField<AuditLogRecord, String> SECOND_ACTION = createField("f_second_action", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "二级操作");

    /**
     * The column <code>zxy-log.t_audit_log.f_member_full_name</code>. 操作人
     */
    public final TableField<AuditLogRecord, String> MEMBER_FULL_NAME = createField("f_member_full_name", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "操作人");

    /**
     * The column <code>zxy-log.t_audit_log.f_browser</code>. 浏览器
     */
    public final TableField<AuditLogRecord, String> BROWSER = createField("f_browser", org.jooq.impl.SQLDataType.VARCHAR.length(100), this, "浏览器");

    /**
     * The column <code>zxy-log.t_audit_log.f_user_agent</code>. 浏览器userAgent
     */
    public final TableField<AuditLogRecord, String> USER_AGENT = createField("f_user_agent", org.jooq.impl.SQLDataType.VARCHAR.length(1000), this, "浏览器userAgent");

    /**
     * The column <code>zxy-log.t_audit_log.f_ip</code>. ip
     */
    public final TableField<AuditLogRecord, String> IP = createField("f_ip", org.jooq.impl.SQLDataType.VARCHAR.length(40), this, "ip");

    /**
     * The column <code>zxy-log.t_audit_log.f_log_time</code>. 日志时间
     */
    public final TableField<AuditLogRecord, Long> LOG_TIME = createField("f_log_time", org.jooq.impl.SQLDataType.BIGINT, this, "日志时间");

    /**
     * Create a <code>zxy-log.t_audit_log</code> table reference
     */
    public AuditLog() {
        this("t_audit_log", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_audit_log</code> table reference
     */
    public AuditLog(String alias) {
        this(alias, AUDIT_LOG);
    }

    private AuditLog(String alias, Table<AuditLogRecord> aliased) {
        this(alias, aliased, null);
    }

    private AuditLog(String alias, Table<AuditLogRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AuditLogRecord> getPrimaryKey() {
        return Keys.KEY_T_AUDIT_LOG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AuditLogRecord>> getKeys() {
        return Arrays.<UniqueKey<AuditLogRecord>>asList(Keys.KEY_T_AUDIT_LOG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public AuditLog as(String alias) {
        return new AuditLog(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public AuditLog rename(String name) {
        return new AuditLog(name, null);
    }
}
