package com.zxy.product.log.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.log.entity.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> zhouyong
 */
@RemoteService
public interface DisplayService {

    /**
     * 获取登录统计数据
     * @return LoginRecord
     */
    LoginRecord loginCount();

    /**
     * 获取在线人数、人次
     * @return LoginRecord
     */
    LoginRecord getLoginUserCounts();

    /**
     * 人群登录情况分布
     * @return Map<String,MemberLoginDistribution>
     */
    Map<String,MemberLoginDistribution> getMemberLoginDistribution();

    /**
     * 日活数据趋势
     * @return list
     */
    List<DailyActiveUser> dauTrends();

    /**
     * 热门资源排行TOP3
     * @return map
     */
    Map<String,List<ResourceVisit>> resourceTop3();

    /**
     * 接口触发定时任务
     * @param type:大屏定时任务类型
     * 0 -> 所有(8个), 1 -> 登录数据统计, 2 -> 学员日活趋势, 3 -> 热门主题,4 -> 30日学习数据
     * 5 -> 学习时长走势, 6 -> 重点学习活动, 7 -> 在库资源, 8 -> 热门资源排行
     */
    String taskScheduler(String type);

    /**
     * 热门资源排行TOP10
     * @param date:日期
     * @param page:页数
     * @param pageSize:大屏定时任务类型
     * @return List<ResourceVisit>
     */

    //热门学习 -- LJY -- 2022/05/17
    List<ResourceVisit> resourceTop10(Date date, int page, int pageSize);

    //获取当天登陆人数
    LoginRecord getLoginMemberCounts();

    /**
     * 登录活跃状况
     * @return map
     */
    Map<String,List<LoginCountDay>> getLoginActivityView();

}
