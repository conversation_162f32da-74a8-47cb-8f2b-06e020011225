/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IMemberDetail extends Serializable {

    /**
     * Setter for <code>zxy-log.t_member_detail.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_member_id</code>. 人员ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_member_id</code>. 人员ID
     */
    public String getMemberId();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_incumbency_status</code>. 在职状态
     */
    public void setIncumbencyStatus(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_incumbency_status</code>. 在职状态
     */
    public String getIncumbencyStatus();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_entry_date</code>. 入职时间
     */
    public void setEntryDate(Long value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_entry_date</code>. 入职时间
     */
    public Long getEntryDate();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_head_portrait</code>. 头像图片ID
     */
    public void setHeadPortrait(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_head_portrait</code>. 头像图片ID
     */
    public String getHeadPortrait();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_head_portrait_path</code>. 头像图片路径
     */
    public void setHeadPortraitPath(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_head_portrait_path</code>. 头像图片路径
     */
    public String getHeadPortraitPath();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_customer_type</code>. 客户类型
     */
    public void setCustomerType(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_customer_type</code>. 客户类型
     */
    public String getCustomerType();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_expiry_date</code>. 有效期
     */
    public void setExpiryDate(Long value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_expiry_date</code>. 有效期
     */
    public Long getExpiryDate();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_nationality_id</code>. 国籍id
     */
    public void setNationalityId(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_nationality_id</code>. 国籍id
     */
    public String getNationalityId();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_ethnicity_id</code>. 民族id
     */
    public void setEthnicityId(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_ethnicity_id</code>. 民族id
     */
    public String getEthnicityId();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_politicalization_id</code>. 政治面貌id
     */
    public void setPoliticalizationId(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_politicalization_id</code>. 政治面貌id
     */
    public String getPoliticalizationId();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_education_id</code>. 学历id
     */
    public void setEducationId(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_education_id</code>. 学历id
     */
    public String getEducationId();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_credential_type</code>. 证件类型id
     */
    public void setCredentialType(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_credential_type</code>. 证件类型id
     */
    public String getCredentialType();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_credential_value</code>. 证件值
     */
    public void setCredentialValue(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_credential_value</code>. 证件值
     */
    public String getCredentialValue();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_is_leader</code>. 是否领导班子成员(1=是;0=否)
     */
    public void setIsLeader(Integer value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_is_leader</code>. 是否领导班子成员(1=是;0=否)
     */
    public Integer getIsLeader();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_join_date</code>. 参加本单位时间
     */
    public void setJoinDate(Long value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_join_date</code>. 参加本单位时间
     */
    public Long getJoinDate();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_born_date</code>. 出生日期
     */
    public void setBornDate(Long value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_born_date</code>. 出生日期
     */
    public Long getBornDate();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_school</code>. 学院
     */
    public void setSchool(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_school</code>. 学院
     */
    public String getSchool();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_graduate_date</code>. 毕业时间
     */
    public void setGraduateDate(Long value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_graduate_date</code>. 毕业时间
     */
    public Long getGraduateDate();

    /**
     * Setter for <code>zxy-log.t_member_detail.f_summary</code>. 个人简介
     */
    public void setSummary(String value);

    /**
     * Getter for <code>zxy-log.t_member_detail.f_summary</code>. 个人简介
     */
    public String getSummary();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IMemberDetail
     */
    public void from(com.zxy.product.log.jooq.tables.interfaces.IMemberDetail from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IMemberDetail
     */
    public <E extends com.zxy.product.log.jooq.tables.interfaces.IMemberDetail> E into(E into);
}
