/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.IAuditLog;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AuditLogEntity extends BaseEntity implements IAuditLog {

    private static final long serialVersionUID = 1L;

    private String  organizationId;
    private String  desc;
    private String  module;
    private String  subModule;
    private Integer action;
    private String  firstAction;
    private String  secondAction;
    private String  memberFullName;
    private String  browser;
    private String  userAgent;
    private String  ip;
    private Long    logTime;

    public AuditLogEntity() {}

    public AuditLogEntity(AuditLogEntity value) {
        this.organizationId = value.organizationId;
        this.desc = value.desc;
        this.module = value.module;
        this.subModule = value.subModule;
        this.action = value.action;
        this.firstAction = value.firstAction;
        this.secondAction = value.secondAction;
        this.memberFullName = value.memberFullName;
        this.browser = value.browser;
        this.userAgent = value.userAgent;
        this.ip = value.ip;
        this.logTime = value.logTime;
    }

    public AuditLogEntity(
        String  id,
        Long    createTime,
        String  organizationId,
        String  desc,
        String  module,
        String  subModule,
        Integer action,
        String  firstAction,
        String  secondAction,
        String  memberFullName,
        String  browser,
        String  userAgent,
        String  ip,
        Long    logTime
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.organizationId = organizationId;
        this.desc = desc;
        this.module = module;
        this.subModule = subModule;
        this.action = action;
        this.firstAction = firstAction;
        this.secondAction = secondAction;
        this.memberFullName = memberFullName;
        this.browser = browser;
        this.userAgent = userAgent;
        this.ip = ip;
        this.logTime = logTime;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }

    @Override
    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getModule() {
        return this.module;
    }

    @Override
    public void setModule(String module) {
        this.module = module;
    }

    @Override
    public String getSubModule() {
        return this.subModule;
    }

    @Override
    public void setSubModule(String subModule) {
        this.subModule = subModule;
    }

    @Override
    public Integer getAction() {
        return this.action;
    }

    @Override
    public void setAction(Integer action) {
        this.action = action;
    }

    @Override
    public String getFirstAction() {
        return this.firstAction;
    }

    @Override
    public void setFirstAction(String firstAction) {
        this.firstAction = firstAction;
    }

    @Override
    public String getSecondAction() {
        return this.secondAction;
    }

    @Override
    public void setSecondAction(String secondAction) {
        this.secondAction = secondAction;
    }

    @Override
    public String getMemberFullName() {
        return this.memberFullName;
    }

    @Override
    public void setMemberFullName(String memberFullName) {
        this.memberFullName = memberFullName;
    }

    @Override
    public String getBrowser() {
        return this.browser;
    }

    @Override
    public void setBrowser(String browser) {
        this.browser = browser;
    }

    @Override
    public String getUserAgent() {
        return this.userAgent;
    }

    @Override
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    @Override
    public String getIp() {
        return this.ip;
    }

    @Override
    public void setIp(String ip) {
        this.ip = ip;
    }

    @Override
    public Long getLogTime() {
        return this.logTime;
    }

    @Override
    public void setLogTime(Long logTime) {
        this.logTime = logTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AuditLogEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(organizationId);
        sb.append(", ").append(desc);
        sb.append(", ").append(module);
        sb.append(", ").append(subModule);
        sb.append(", ").append(action);
        sb.append(", ").append(firstAction);
        sb.append(", ").append(secondAction);
        sb.append(", ").append(memberFullName);
        sb.append(", ").append(browser);
        sb.append(", ").append(userAgent);
        sb.append(", ").append(ip);
        sb.append(", ").append(logTime);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IAuditLog from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setOrganizationId(from.getOrganizationId());
        setDesc(from.getDesc());
        setModule(from.getModule());
        setSubModule(from.getSubModule());
        setAction(from.getAction());
        setFirstAction(from.getFirstAction());
        setSecondAction(from.getSecondAction());
        setMemberFullName(from.getMemberFullName());
        setBrowser(from.getBrowser());
        setUserAgent(from.getUserAgent());
        setIp(from.getIp());
        setLogTime(from.getLogTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IAuditLog> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends AuditLogEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.AuditLogRecord r = new com.zxy.product.log.jooq.tables.records.AuditLogRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ID, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ORGANIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.DESC.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.DESC, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.DESC));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.MODULE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.MODULE, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.MODULE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.SUB_MODULE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.SUB_MODULE, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.SUB_MODULE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ACTION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ACTION, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.ACTION));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.FIRST_ACTION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.FIRST_ACTION, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.FIRST_ACTION));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.SECOND_ACTION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.SECOND_ACTION, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.SECOND_ACTION));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.MEMBER_FULL_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.MEMBER_FULL_NAME, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.MEMBER_FULL_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.BROWSER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.BROWSER, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.BROWSER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.USER_AGENT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.USER_AGENT, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.USER_AGENT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.IP.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.IP, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.IP));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.LOG_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.LOG_TIME, record.getValue(com.zxy.product.log.jooq.tables.AuditLog.AUDIT_LOG.LOG_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
