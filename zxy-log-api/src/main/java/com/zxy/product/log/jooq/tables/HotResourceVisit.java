/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.HotResourceVisitRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 热门学习资源表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HotResourceVisit extends TableImpl<HotResourceVisitRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_hot_resource_visit</code>
     */
    public static final HotResourceVisit HOT_RESOURCE_VISIT = new HotResourceVisit();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<HotResourceVisitRecord> getRecordType() {
        return HotResourceVisitRecord.class;
    }

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_id</code>.
     */
    public final TableField<HotResourceVisitRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_business_id</code>. 资源ID
     */
    public final TableField<HotResourceVisitRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "资源ID");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_business_name</code>. 资源名称
     */
    public final TableField<HotResourceVisitRecord, String> BUSINESS_NAME = createField("f_business_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "资源名称");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_business_type</code>. 资源类型:1 课程,2 专题,3 直播
     */
    public final TableField<HotResourceVisitRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "资源类型:1 课程,2 专题,3 直播");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_organization_id</code>. 资源所属组织ID
     */
    public final TableField<HotResourceVisitRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "资源所属组织ID");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_organization_name</code>. 资源所属组织名称
     */
    public final TableField<HotResourceVisitRecord, String> ORGANIZATION_NAME = createField("f_organization_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "资源所属组织名称");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_day30_visit</code>. 资源近30天访问次数
     */
    public final TableField<HotResourceVisitRecord, Integer> DAY30_VISIT = createField("f_day30_visit", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "资源近30天访问次数");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_status</code>. 状态 显示0，隐藏1
     */
    public final TableField<HotResourceVisitRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态 显示0，隐藏1");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_create_time</code>. 创建时间
     */
    public final TableField<HotResourceVisitRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>zxy-log.t_hot_resource_visit.f_modify_date</code>. 修改时间
     */
    public final TableField<HotResourceVisitRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>zxy-log.t_hot_resource_visit</code> table reference
     */
    public HotResourceVisit() {
        this("t_hot_resource_visit", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_hot_resource_visit</code> table reference
     */
    public HotResourceVisit(String alias) {
        this(alias, HOT_RESOURCE_VISIT);
    }

    private HotResourceVisit(String alias, Table<HotResourceVisitRecord> aliased) {
        this(alias, aliased, null);
    }

    private HotResourceVisit(String alias, Table<HotResourceVisitRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "热门学习资源表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<HotResourceVisitRecord> getPrimaryKey() {
        return Keys.KEY_T_HOT_RESOURCE_VISIT_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<HotResourceVisitRecord>> getKeys() {
        return Arrays.<UniqueKey<HotResourceVisitRecord>>asList(Keys.KEY_T_HOT_RESOURCE_VISIT_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public HotResourceVisit as(String alias) {
        return new HotResourceVisit(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public HotResourceVisit rename(String name) {
        return new HotResourceVisit(name, null);
    }
}
