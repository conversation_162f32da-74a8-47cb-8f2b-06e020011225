/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables;


import com.zxy.product.log.jooq.Keys;
import com.zxy.product.log.jooq.ZxyLog;
import com.zxy.product.log.jooq.tables.records.DeleteDataLogRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DeleteDataLog extends TableImpl<DeleteDataLogRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>zxy-log.t_delete_data_log</code>
     */
    public static final DeleteDataLog DELETE_DATA_LOG = new DeleteDataLog();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DeleteDataLogRecord> getRecordType() {
        return DeleteDataLogRecord.class;
    }

    /**
     * The column <code>zxy-log.t_delete_data_log.f_id</code>. 主键
     */
    public final TableField<DeleteDataLogRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "主键");

    /**
     * The column <code>zxy-log.t_delete_data_log.f_database_name</code>. 数据库名称
     */
    public final TableField<DeleteDataLogRecord, String> DATABASE_NAME = createField("f_database_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false).defaultValue(org.jooq.impl.DSL.inline("'human-resource'", org.jooq.impl.SQLDataType.VARCHAR)), this, "数据库名称");

    /**
     * The column <code>zxy-log.t_delete_data_log.f_table_name</code>. 表名称
     */
    public final TableField<DeleteDataLogRecord, String> TABLE_NAME = createField("f_table_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "表名称");

    /**
     * The column <code>zxy-log.t_delete_data_log.f_business_id</code>. 业务id
     */
    public final TableField<DeleteDataLogRecord, String> BUSINESS_ID = createField("f_business_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "业务id");

    /**
     * The column <code>zxy-log.t_delete_data_log.f_create_time</code>. 创建时间
     */
    public final TableField<DeleteDataLogRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>zxy-log.t_delete_data_log.f_modify_date</code>. 更新时间
     */
    public final TableField<DeleteDataLogRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "更新时间");

    /**
     * The column <code>zxy-log.t_delete_data_log.f_company_id</code>. 企业id
     */
    public final TableField<DeleteDataLogRecord, String> COMPANY_ID = createField("f_company_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "企业id");

    /**
     * Create a <code>zxy-log.t_delete_data_log</code> table reference
     */
    public DeleteDataLog() {
        this("t_delete_data_log", null);
    }

    /**
     * Create an aliased <code>zxy-log.t_delete_data_log</code> table reference
     */
    public DeleteDataLog(String alias) {
        this(alias, DELETE_DATA_LOG);
    }

    private DeleteDataLog(String alias, Table<DeleteDataLogRecord> aliased) {
        this(alias, aliased, null);
    }

    private DeleteDataLog(String alias, Table<DeleteDataLogRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return ZxyLog.ZXY_LOG_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<DeleteDataLogRecord> getPrimaryKey() {
        return Keys.KEY_T_DELETE_DATA_LOG_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<DeleteDataLogRecord>> getKeys() {
        return Arrays.<UniqueKey<DeleteDataLogRecord>>asList(Keys.KEY_T_DELETE_DATA_LOG_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public DeleteDataLog as(String alias) {
        return new DeleteDataLog(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DeleteDataLog rename(String name) {
        return new DeleteDataLog(name, null);
    }
}
