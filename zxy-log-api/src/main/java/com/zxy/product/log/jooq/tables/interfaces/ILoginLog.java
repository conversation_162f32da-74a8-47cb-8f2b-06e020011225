/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 登录日至表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ILoginLog extends Serializable {

    /**
     * Setter for <code>zxy-log.t_login_log.f_id</code>. 主键
     */
    public void setId(String value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_id</code>. 主键
     */
    public String getId();

    /**
     * Setter for <code>zxy-log.t_login_log.f_organization_id</code>. 组织机构id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_organization_id</code>. 组织机构id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>zxy-log.t_login_log.f_member_id</code>. 人员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_member_id</code>. 人员id
     */
    public String getMemberId();

    /**
     * Setter for <code>zxy-log.t_login_log.f_ip_addr</code>. ip 地址
     */
    public void setIpAddr(String value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_ip_addr</code>. ip 地址
     */
    public String getIpAddr();

    /**
     * Setter for <code>zxy-log.t_login_log.f_terminal_type</code>. 1.pc 2.app
     */
    public void setTerminalType(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_terminal_type</code>. 1.pc 2.app
     */
    public Integer getTerminalType();

    /**
     * Setter for <code>zxy-log.t_login_log.f_terminal</code>. 终端名称 pc对应浏览器，app对应手机型号
     */
    public void setTerminal(String value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_terminal</code>. 终端名称 pc对应浏览器，app对应手机型号
     */
    public String getTerminal();

    /**
     * Setter for <code>zxy-log.t_login_log.f_status</code>. 1.登录成功 2.登录失败
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_status</code>. 1.登录成功 2.登录失败
     */
    public Integer getStatus();

    /**
     * Setter for <code>zxy-log.t_login_log.f_system</code>. 登录系统 pc对应系统 app对应手机系统
     */
    public void setSystem(String value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_system</code>. 登录系统 pc对应系统 app对应手机系统
     */
    public String getSystem();

    /**
     * Setter for <code>zxy-log.t_login_log.f_user_agent</code>. 登录UserAgent
     */
    public void setUserAgent(String value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_user_agent</code>. 登录UserAgent
     */
    public String getUserAgent();

    /**
     * Setter for <code>zxy-log.t_login_log.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>zxy-log.t_login_log.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    public void setPasswordType(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_password_type</code>. 登录密码类型，0静态密码 1动态密码
     */
    public Integer getPasswordType();

    /**
     * Setter for <code>zxy-log.t_login_log.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_source</code>. 登录来源，0网大 1 OA单点 2党建单点
     */
    public Integer getSource();

    /**
     * Setter for <code>zxy-log.t_login_log.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>zxy-log.t_login_log.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ILoginLog
     */
    public void from(ILoginLog from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ILoginLog
     */
    public <E extends ILoginLog> E into(E into);
}
