/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.log.jooq.tables.interfaces.ILoginLog;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 登录日至表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class LoginLogEntity extends BaseEntity implements ILoginLog {

    private static final long serialVersionUID = 1L;

    private String    organizationId;
    private String    memberId;
    private String    ipAddr;
    private Integer   terminalType;
    private String    terminal;
    private Integer   status;
    private String    system;
    private String    userAgent;
    private Integer   passwordType;
    private Integer   source;
    private Timestamp modifyDate;

    public LoginLogEntity() {}

    public LoginLogEntity(LoginLogEntity value) {
        this.organizationId = value.organizationId;
        this.memberId = value.memberId;
        this.ipAddr = value.ipAddr;
        this.terminalType = value.terminalType;
        this.terminal = value.terminal;
        this.status = value.status;
        this.system = value.system;
        this.userAgent = value.userAgent;
        this.passwordType = value.passwordType;
        this.source = value.source;
        this.modifyDate = value.modifyDate;
    }

    public LoginLogEntity(
        String    id,
        String    organizationId,
        String    memberId,
        String    ipAddr,
        Integer   terminalType,
        String    terminal,
        Integer   status,
        String    system,
        String    userAgent,
        Long      createTime,
        Integer   passwordType,
        Integer   source,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.organizationId = organizationId;
        this.memberId = memberId;
        this.ipAddr = ipAddr;
        this.terminalType = terminalType;
        this.terminal = terminal;
        this.status = status;
        this.system = system;
        this.userAgent = userAgent;
        super.setCreateTime(createTime);
        this.passwordType = passwordType;
        this.source = source;
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getIpAddr() {
        return this.ipAddr;
    }

    @Override
    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    @Override
    public Integer getTerminalType() {
        return this.terminalType;
    }

    @Override
    public void setTerminalType(Integer terminalType) {
        this.terminalType = terminalType;
    }

    @Override
    public String getTerminal() {
        return this.terminal;
    }

    @Override
    public void setTerminal(String terminal) {
        this.terminal = terminal;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getSystem() {
        return this.system;
    }

    @Override
    public void setSystem(String system) {
        this.system = system;
    }

    @Override
    public String getUserAgent() {
        return this.userAgent;
    }

    @Override
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Integer getPasswordType() {
        return this.passwordType;
    }

    @Override
    public void setPasswordType(Integer passwordType) {
        this.passwordType = passwordType;
    }

    @Override
    public Integer getSource() {
        return this.source;
    }

    @Override
    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("LoginLogEntity (");

        sb.append(getId());
        sb.append(", ").append(organizationId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(ipAddr);
        sb.append(", ").append(terminalType);
        sb.append(", ").append(terminal);
        sb.append(", ").append(status);
        sb.append(", ").append(system);
        sb.append(", ").append(userAgent);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(passwordType);
        sb.append(", ").append(source);
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ILoginLog from) {
        setId(from.getId());
        setOrganizationId(from.getOrganizationId());
        setMemberId(from.getMemberId());
        setIpAddr(from.getIpAddr());
        setTerminalType(from.getTerminalType());
        setTerminal(from.getTerminal());
        setStatus(from.getStatus());
        setSystem(from.getSystem());
        setUserAgent(from.getUserAgent());
        setCreateTime(from.getCreateTime());
        setPasswordType(from.getPasswordType());
        setSource(from.getSource());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ILoginLog> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends LoginLogEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.log.jooq.tables.records.LoginLogRecord r = new com.zxy.product.log.jooq.tables.records.LoginLogRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.ID, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.ORGANIZATION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.ORGANIZATION_ID, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.ORGANIZATION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.MEMBER_ID, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.IP_ADDR.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.IP_ADDR, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.IP_ADDR));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.TERMINAL_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.TERMINAL_TYPE, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.TERMINAL_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.TERMINAL.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.TERMINAL, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.TERMINAL));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.STATUS, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.SYSTEM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.SYSTEM, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.SYSTEM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.USER_AGENT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.USER_AGENT, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.USER_AGENT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.CREATE_TIME, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.PASSWORD_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.PASSWORD_TYPE, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.PASSWORD_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.SOURCE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.SOURCE, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.SOURCE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.MODIFY_DATE, record.getValue(com.zxy.product.log.jooq.tables.LoginLog.LOGIN_LOG.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
