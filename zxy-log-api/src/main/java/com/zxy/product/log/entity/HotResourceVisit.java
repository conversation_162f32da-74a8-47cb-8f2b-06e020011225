package com.zxy.product.log.entity;

import com.zxy.product.log.jooq.tables.pojos.HotResourceVisitEntity;

/**
 * <AUTHOR> zhouyong
 * @ClassName : HotResourceVisit
 * @Description : 热门学习资源
 * @date : 2024-09-23 15:04
 */
public class HotResourceVisit extends HotResourceVisitEntity {

    private static final long serialVersionUID = 4823233808646403197L;

    /**
     * 资源类型:1 课程,2 专题,3 直播
     */
    public static final int BUSINESS_TYPE_COURSE    = 1;
    public static final int BUSINESS_TYPE_SUBJECT   = 2;
    public static final int BUSINESS_TYPE_LIVE      = 3;

    /**
     * 状态 显示0，隐藏1
     */
    public static final int STATUS_SHOW             = 0;
    public static final int STATUS_HIDE             = 1;

    public HotResourceVisit(){}

    public HotResourceVisit(String businessId, String businessName, Integer businessType,
                            String organizationId, String organizationName, Integer day30Visit, Integer status) {
        this.forInsert();
        this.setBusinessId(businessId);
        this.setBusinessName(businessName);
        this.setBusinessType(businessType);
        this.setOrganizationId(organizationId);
        this.setOrganizationName(organizationName);
        this.setDay30Visit(day30Visit);
        this.setStatus(status);
    }

}
