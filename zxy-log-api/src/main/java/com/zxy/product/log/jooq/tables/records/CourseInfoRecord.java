/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.log.jooq.tables.records;


import com.zxy.product.log.jooq.tables.CourseInfo;
import com.zxy.product.log.jooq.tables.interfaces.ICourseInfo;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * otter同步课程表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfoRecord extends UpdatableRecordImpl<CourseInfoRecord> implements Record7<String, String, Integer, Integer, String, Integer, Long>, ICourseInfo {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>zxy-log.t_course_info.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>zxy-log.t_course_info.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>zxy-log.t_course_info.f_name</code>. 课程名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>zxy-log.t_course_info.f_name</code>. 课程名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>zxy-log.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    @Override
    public void setBusinessType(Integer value) {
        set(2, value);
    }

    /**
     * Getter for <code>zxy-log.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    @Override
    public Integer getBusinessType() {
        return (Integer) get(2);
    }

    /**
     * Setter for <code>zxy-log.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    @Override
    public void setStatus(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>zxy-log.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>zxy-log.t_course_info.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>zxy-log.t_course_info.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>zxy-log.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>zxy-log.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>zxy-log.t_course_info.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>zxy-log.t_course_info.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, Integer, String, Integer, Long> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row7<String, String, Integer, Integer, String, Integer, Long> valuesRow() {
        return (Row7) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseInfo.COURSE_INFO.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseInfo.COURSE_INFO.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field3() {
        return CourseInfo.COURSE_INFO.BUSINESS_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field4() {
        return CourseInfo.COURSE_INFO.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return CourseInfo.COURSE_INFO.ORGANIZATION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return CourseInfo.COURSE_INFO.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return CourseInfo.COURSE_INFO.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value3() {
        return getBusinessType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value4() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getOrganizationId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord value3(Integer value) {
        setBusinessType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord value4(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord value5(String value) {
        setOrganizationId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord value6(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord value7(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfoRecord values(String value1, String value2, Integer value3, Integer value4, String value5, Integer value6, Long value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseInfo from) {
        setId(from.getId());
        setName(from.getName());
        setBusinessType(from.getBusinessType());
        setStatus(from.getStatus());
        setOrganizationId(from.getOrganizationId());
        setDeleteFlag(from.getDeleteFlag());
        setCreateTime(from.getCreateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseInfo> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseInfoRecord
     */
    public CourseInfoRecord() {
        super(CourseInfo.COURSE_INFO);
    }

    /**
     * Create a detached, initialised CourseInfoRecord
     */
    public CourseInfoRecord(String id, String name, Integer businessType, Integer status, String organizationId, Integer deleteFlag, Long createTime) {
        super(CourseInfo.COURSE_INFO);

        set(0, id);
        set(1, name);
        set(2, businessType);
        set(3, status);
        set(4, organizationId);
        set(5, deleteFlag);
        set(6, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.log.jooq.tables.pojos.CourseInfoEntity)) {
            return false;
        }
        com.zxy.product.log.jooq.tables.pojos.CourseInfoEntity pojo = (com.zxy.product.log.jooq.tables.pojos.CourseInfoEntity)source;
        pojo.into(this);
        return true;
    }
}
