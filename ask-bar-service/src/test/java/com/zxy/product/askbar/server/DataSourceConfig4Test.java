package com.zxy.product.askbar.server;

import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType;
import org.springframework.stereotype.Component;

@Component
//@Profile("testOne") // 指定单元测试活动所匹配的数据源
public class DataSourceConfig4Test {  
  
    @Bean(name="dataSourceUtils") // 要和模拟的数据源名称相对应
    DataSource dataSourceUtils() throws SQLException {
        return new EmbeddedDatabaseBuilder()
                .setType(EmbeddedDatabaseType.H2)
                .addScript("classpath:/H2_TYPE.sql")  
                .addScript("classpath:/INIT_TABLE.sql")  
                .addScript("classpath:/INIT_DATA.sql")
                .build();  
    }  
      
}  